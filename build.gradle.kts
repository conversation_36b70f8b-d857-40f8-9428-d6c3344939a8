plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.plugin.parcelize) apply false
    alias(libs.plugins.kotlin.plugin.serialization) apply false
    alias(libs.plugins.compose.compiler) apply false
    alias(libs.plugins.hilt) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.ktlint.gradle) apply false
    alias(libs.plugins.roborazzi) apply false
}

allprojects {
    afterEvaluate {
        apply(plugin = libs.plugins.ktlint.gradle.get().pluginId)
        configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
            reporters {
                reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.CHECKSTYLE)
            }
            filter {
                include("**/src/**/*.kt")
                include("**.kts")
                exclude("**/build/**")
            }
        }
    }
}

val clean by tasks.registering(Delete::class) {
    delete(rootProject.layout.buildDirectory)
}
