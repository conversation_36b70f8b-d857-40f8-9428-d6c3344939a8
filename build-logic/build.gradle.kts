plugins {
    `kotlin-dsl`
}

group = "jp.co.dhbk.connectbiz.buildlogic"

repositories {
    google()
    mavenCentral()
}

kotlin {
    jvmToolchain(17)
}

dependencies {
    implementation(libs.android.gradlePlugin)
    implementation(libs.kotlin.gradlePlugin)
    implementation(libs.ksp.gradlePlugin)
    implementation(libs.compose.gradlePlugin)
}

gradlePlugin {
    plugins {
        register("android.application") {
            id = "buildlogic.android.application"
            implementationClass = "jp.co.dhbk.connectbiz.buildlogic.AndroidApplicationPlugin"
        }
        register("android.library") {
            id = "buildlogic.android.library"
            implementationClass = "jp.co.dhbk.connectbiz.buildlogic.AndroidLibraryPlugin"
        }
        register("android.compose") {
            id = "buildlogic.android.compose"
            implementationClass = "jp.co.dhbk.connectbiz.buildlogic.AndroidComposePlugin"
        }
        register("android.hilt") {
            id = "buildlogic.android.hilt"
            implementationClass = "jp.co.dhbk.connectbiz.buildlogic.AndroidHiltPlugin"
        }
        register("roborazzi") {
            id = "buildlogic.roborazzi"
            implementationClass = "jp.co.dhbk.connectbiz.buildlogic.RoborazziPlugin"
        }
        register("retrofit"){
            id="buildlogic.retrofit"
            implementationClass="jp.co.dhbk.connectbiz.buildlogic.RetrofitNetworkingPlugin"
        }
    }
}
