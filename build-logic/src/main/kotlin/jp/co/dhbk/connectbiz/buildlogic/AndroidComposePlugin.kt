package jp.co.dhbk.connectbiz.buildlogic

import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.debugImplementation
import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.implementation
import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.ktlintRuleset
import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.lintChecks
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.android
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.libs
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.library
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.plugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.provider.Provider
import org.gradle.kotlin.dsl.assign
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.jetbrains.kotlin.compose.compiler.gradle.ComposeCompilerGradlePluginExtension
import java.io.File

class AndroidComposePlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply(libs.plugin("compose-compiler").pluginId)
                apply(libs.plugin("ktlint-gradle").pluginId)
            }

            android {
                buildFeatures.compose = true
                testOptions {
                    unitTests {
                        isIncludeAndroidResources = true
                    }
                }
            }

            dependencies {
                implementation(platform(libs.library("androidx-compose-bom")))
                implementation(libs.library("androidx-core"))
                implementation(libs.library("androidx-compose-material3"))
                implementation(libs.library("androidx-compose-ui"))
                implementation(libs.library("androidx-compose-ui-tooling-preview"))
                implementation(libs.library("androidx-lifecycle-runtimeCompose"))
                implementation(libs.library("androidx-compose-activity"))
                implementation(libs.library("androidx-navigation-compose"))
                debugImplementation(libs.library("androidx-compose-ui-tooling"))
                lintChecks(project(":lint:designsystem"))
                ktlintRuleset(libs.library("compose-rules-ktlint"))
            }

            extensions.configure<ComposeCompilerGradlePluginExtension> {
                fun Provider<String>.onlyIfTrue() =
                    flatMap { provider { it.takeIf(String::toBoolean) } }

                fun Provider<*>.relativeToRootProject(dir: String) = flatMap {
                    rootProject.layout.buildDirectory.dir(projectDir.toRelativeString(rootDir))
                }.map { it.dir(dir) }

                project.providers.gradleProperty("enableComposeCompilerMetrics").onlyIfTrue()
                    .relativeToRootProject("compose-metrics")
                    .let(metricsDestination::set)

                project.providers.gradleProperty("enableComposeCompilerReports").onlyIfTrue()
                    .relativeToRootProject("compose-reports")
                    .let(reportsDestination::set)

                // Check if the compose-compiler-config.conf exists
                val configFile = File(rootProject.projectDir, "compose-compiler-config.conf")
                if (configFile.exists()) {
                    stabilityConfigurationFile = rootProject.layout.projectDirectory.file("compose-compiler-config.conf")
                } else {
                    logger.warn("Compose compiler config file 'compose-compiler-config.conf' not found, skipping configuration.")
                }
            }
        }
    }
}