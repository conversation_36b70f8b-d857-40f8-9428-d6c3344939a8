package jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog

import org.gradle.api.artifacts.MinimalExternalModuleDependency
import org.gradle.api.artifacts.VersionCatalog

internal fun VersionCatalog.library(name: String): MinimalExternalModuleDependency {
    return findLibrary(name).orElseThrow {
        NoSuchElementException("Library with name '$name' not found in the version catalog.")
    }.get()
}
