package jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope


import jp.co.dhbk.connectbiz.buildlogic.extensions.project.libs
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.library
import org.gradle.api.Project
import org.gradle.kotlin.dsl.DependencyHandlerScope

internal fun DependencyHandlerScope.implementationCommonLibs(target: Project) = with(target) {
    implementation(libs.library("kotlinx-collections-immutable"))
    implementation(libs.library("kotlinx-coroutines-core"))
    implementation(libs.library("androidx-core"))
    implementation(libs.library("timber"))
    implementation(libs.library("androidx-constraintlayout-compose"))
    testImplementation(libs.library("junit"))
    testImplementation(libs.library("kotlinx-coroutines-test"))
    testImplementation(libs.library("google-truth"))
    testImplementation(libs.library("turbine"))
    testImplementation(libs.library("mockk"))
    testImplementation(libs.library("robolectric"))
    testImplementation(libs.library("mockito-core"))
    testImplementation(libs.library("androidx-test-core"))
    androidTestImplementation(libs.library("androidx-test-junit"))
    androidTestImplementation(libs.library("androidx-test-espresso"))
    androidTestImplementation(libs.library("androidx-test-runner"))
    androidTestImplementation(libs.library("androidx-test-rules"))
}
