package jp.co.dhbk.connectbiz.buildlogic

import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.implementation
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.libs
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.library
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.plugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class RetrofitNetworkingPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            // Apply necessary plugins
            with(pluginManager) {
                apply(libs.plugin("ksp").pluginId)  // KSP plugin for Kotlin Symbol Processing
            }

            dependencies {
                implementation(platform(libs.library("okhttp-bom")))
                implementation(libs.library("kotlinx-serialization-json"))
                // OkHttp dependencies
                implementation(libs.library("okhttp")) // OkHttp core
                implementation(libs.library("okhttp-logging")) // OkHttp logging interceptor
                implementation(platform(libs.library("retrofit-bom")))
                implementation(libs.library("google-code-gson"))
                // Retrofit dependencies
                implementation(libs.library("retrofit")) // Retrofit core
                implementation(libs.library("retrofit-serialization-converter")) // Serialization converter for Retrofit
            }

        }
    }
}
