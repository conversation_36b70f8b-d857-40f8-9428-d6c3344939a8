package jp.co.dhbk.connectbiz.buildlogic

import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.implementation
import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.ksp
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.libs
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.library
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.plugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class AndroidHiltPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply(libs.plugin("ksp").pluginId)
                apply(libs.plugin("hilt").pluginId)
            }

            dependencies {
                implementation(libs.library("hilt"))
                implementation(libs.library("hilt-navigation-compose"))
                ksp(libs.library("hilt-compiler"))
            }
        }
    }
}
