package jp.co.dhbk.connectbiz.buildlogic

import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.implementationCommonLibs
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.androidLibrary
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.libs
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.plugin
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.version
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinAndroidProjectExtension

class AndroidLibraryPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply(libs.plugin("android-library").pluginId)
                apply(libs.plugin("kotlin-android").pluginId)
            }

            androidLibrary {
                compileSdk = libs.version("compileSdk").toInt()
                defaultConfig.minSdk = libs.version("minSdk").toInt()
                defaultConfig.targetSdk = libs.version("targetSdk").toInt()

                compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_17
                    targetCompatibility = JavaVersion.VERSION_17
                }

                configure<KotlinAndroidProjectExtension> {
                    compilerOptions.apply {
                        jvmTarget.set(JvmTarget.JVM_17)
                    }
                }
            }

            dependencies {
                implementationCommonLibs(target)
            }
        }
    }
}
