package jp.co.dhbk.connectbiz.buildlogic

import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.debugImplementation
import jp.co.dhbk.connectbiz.buildlogic.extensions.dependencyhandlerscope.testImplementation
import jp.co.dhbk.connectbiz.buildlogic.extensions.project.libs
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.library
import jp.co.dhbk.connectbiz.buildlogic.extensions.versioncatalog.plugin
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class RoborazziPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply(libs.plugin("roborazzi").pluginId)
            }

            dependencies {
                testImplementation(libs.library("roborazzi"))
                testImplementation(libs.library("roborazzi-compose"))
                testImplementation(libs.library("roborazzi-compose-preview-scanner-support"))
                testImplementation(libs.library("composable-preview-scanner"))
                testImplementation(libs.library("androidx-compose-ui-test-junit4"))
                debugImplementation(libs.library("androidx-compose-ui-test-manifest"))
            }
        }
    }
}
