GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ansi (1.5.0)
    ast (2.4.2)
    base64 (0.2.0)
    claide (1.1.0)
    claide-plugins (0.9.2)
      cork
      nap
      open4 (~> 1.3)
    colored2 (3.1.2)
    cork (0.3.0)
      colored2 (~> 3.1)
    danger (9.5.1)
      base64 (~> 0.2)
      claide (~> 1.0)
      claide-plugins (>= 0.9.2)
      colored2 (~> 3.1)
      cork (~> 0.1)
      faraday (>= 0.9.0, < 3.0)
      faraday-http-cache (~> 2.0)
      git (~> 1.13)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      octokit (>= 4.0)
      pstore (~> 0.1)
      terminal-table (>= 1, < 4)
    danger-android_lint (0.0.12)
      danger-plugin-api (~> 1.0)
      oga
    danger-checkstyle_format (0.1.1)
      danger-plugin-api (~> 1.0)
      ox (~> 2.0)
    danger-plugin-api (1.0.0)
      danger (> 2.0)
    faraday (2.12.0)
      faraday-net_http (>= 2.0, < 3.4)
      json
      logger
    faraday-http-cache (2.5.1)
      faraday (>= 0.8)
    faraday-net_http (3.3.0)
      net-http
    git (1.19.1)
      addressable (~> 2.8)
      rchardet (~> 1.8)
    json (2.7.4)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    logger (1.6.1)
    nap (1.1.0)
    net-http (0.4.1)
      uri
    octokit (9.2.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    oga (3.4)
      ast
      ruby-ll (~> 2.1)
    open4 (1.3.4)
    ox (2.14.18)
    pstore (0.1.3)
    public_suffix (6.0.1)
    rchardet (1.8.0)
    rexml (3.3.9)
    ruby-ll (2.1.3)
      ansi
      ast
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    unicode-display_width (2.6.0)
    uri (0.13.1)

PLATFORMS
  arm64-darwin-23
  ruby

DEPENDENCIES
  danger
  danger-android_lint
  danger-checkstyle_format

BUNDLED WITH
   2.5.22
