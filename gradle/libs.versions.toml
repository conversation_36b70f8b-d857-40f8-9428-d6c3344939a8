[versions]
browser = "1.8.0"
constraintlayoutCompose = "1.1.0"
foundationAndroid = "1.7.6"
javaJwt = "4.2.1"
minSdk = "29"
mockitoCore = "4.1.0"
targetSdk = "35"
compileSdk = "35"
kotlin = "2.0.20"
androidGradlePlugin = "8.7.2"
kotlinxCoroutines = "1.9.0"
androidxLifecycle = "2.8.7"
androidTools = "31.7.2"
datastore = "1.1.1"
ksp = "2.0.20-1.0.25"
hilt = "2.52"
hiltNavigation ="1.2.0"
retrofit = "2.11.0"
aboutlibraries = "11.2.3"
roborazzi = "1.26.0"
composeUiTest = "1.7.5"
biometric = "1.4.0-alpha02"
accompanistPagerVersion = "0.30.0"
placeholder_version = "0.24.11-rc"
appcompat = "1.7.0"
hilt_worker = "1.2.0"
retrofit_version = "2.11.0"
accompanistPermissions = "0.26.4-beta"
pagging_version = "3.3.4"
room_version = "2.6.1"
gson = "2.10.1"
crypto = "1.0.0"
identityJvmVersion = "202411.1"
firebaseCrashlyticsKtxVersion = "19.2.1"
playServicesMeasurementApiVersion = "22.1.2"
firebaseMessagingKtxVersion = "24.1.0"
firebaseConfigKtx = "22.0.1"
testng = "6.9.6"
runner = "1.6.2"
lifecycleViewmodelKtx = "2.8.7"
lifecycleRuntimeKtx = "2.8.7"
firebaseConfig = "22.0.1"
constraintlayout = "2.2.0"
uiAndroid = "1.7.5"
coilCompose ="2.7.0"
androidxNavigationCompose = "2.8.4"
androidxComposeBom ="2024.11.00"
androidxComposeActivity = "1.9.3"
kotlinxSerializationJson = "1.7.3"
kotlinxCollectionsImmutable ="0.3.8"
composablePreviewScanner ="0.3.2"
androidxCore = "1.15.0"
okhttpBom = "4.12.0"
tink ="1.15.0"
firebaseBom ="33.6.0"
timber ="5.0.1"
composeRulesKtlint = "0.4.4"
junit = "4.13.2"
robolectric ="4.13"
googleTruth ="1.4.4"
turbine ="1.1.0"
mockk ="1.13.12"
androidxTestJunit ="1.2.1"
androidxTestEspresso = "3.6.1"
androidxTestRules ="1.6.1 "
androidxTestCore = "1.6.1"
leakcanary = "2.14"
googleServices = "4.4.2"
appdistribution = "5.0.0"
crashlytics = "3.0.2"
klint = "12.1.1"
exoplayer = "2.19.1"




[libraries]
# gradle
android-gradlePlugin = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
androidx-browser = { module = "androidx.browser:browser", version.ref = "browser" }
androidx-constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayoutCompose" }
androidx-foundation-android = { module = "androidx.compose.foundation:foundation-android", version.ref = "foundationAndroid" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "androidxLifecycle" }
java-jwt = { module = "com.auth0:java-jwt", version.ref = "javaJwt" }
kotlin-gradlePlugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
kotlin-parcelize-runtime = { module = "org.jetbrains.kotlin:kotlin-parcelize-runtime", version.ref = "kotlin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
compose-gradlePlugin = { module = "org.jetbrains.kotlin:compose-compiler-gradle-plugin", version.ref = "kotlin" }

# Core
androidx-core = { module = "androidx.core:core-ktx", version.ref = "androidxCore" }

# Lifecycle
androidx-lifecycle-runtimeCompose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodelCompose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodelSavedstate = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "androidxLifecycle" }

# KotlinX
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutines" }
kotlinx-collections-immutable = { module = "org.jetbrains.kotlinx:kotlinx-collections-immutable", version.ref = "kotlinxCollectionsImmutable" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }

# Compose
androidx-compose-activity = { module = "androidx.activity:activity-compose", version.ref ="androidxComposeActivity" }
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref ="androidxComposeBom" }
androidx-compose-material3 = { module = "androidx.compose.material3:material3" }
androidx-compose-ui = { module = "androidx.compose.ui:ui" }
androidx-compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref ="androidxNavigationCompose" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coilCompose" }

# Hilt
hilt = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
hilt-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hilt" }
hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "hiltNavigation" }
hiltWork = { group = "androidx.hilt", name = "hilt-work", version.ref = "hilt_worker" }


# OkHttp/Retrofit
okhttp-bom = { module = "com.squareup.okhttp3:okhttp-bom", version.ref = "okhttpBom" }
okhttp = { module = "com.squareup.okhttp3:okhttp" }
okhttp-logging = { module = "com.squareup.okhttp3:logging-interceptor" }
retrofit-bom = { module = "com.squareup.retrofit2:retrofit-bom", version.ref = "retrofit" }
retrofit = { module = "com.squareup.retrofit2:retrofit" }
retrofit-serialization-converter = { module = "com.squareup.retrofit2:converter-kotlinx-serialization" }
retrofit-gson = {group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit_version"}


# DataStore/Tink
androidx-datastore-core = { group = "androidx.datastore", name = "datastore-core", version.ref = "datastore" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastore" }
tink = { module = "com.google.crypto.tink:tink-android", version.ref = "tink" }

# Firebase
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }

# AboutLibraries
aboutlibraries-core = { module = "com.mikepenz:aboutlibraries-core", version.ref = "aboutlibraries" }
# Logging
timber = { module = "com.jakewharton.timber:timber", version.ref = "timber" }

# Lint
lint-api = { module = "com.android.tools.lint:lint-api", version.ref = "androidTools" }
lint-checks = { module = "com.android.tools.lint:lint-checks", version.ref = "androidTools" }
compose-rules-ktlint = { module = "io.nlopez.compose.rules:ktlint", version.ref ="composeRulesKtlint" }

# Biometric
androidx-biometric = { module = "androidx.biometric:biometric-ktx", version.ref = "biometric" }

# Test/Debug
junit = { module = "junit:junit", version.ref = "junit" }
robolectric = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinxCoroutines" }
google-truth = { module = "com.google.truth:truth", version.ref = "googleTruth" }
turbine = { module = "app.cash.turbine:turbine", version.ref = "turbine" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
androidx-test-junit = { module = "androidx.test.ext:junit", version.ref = "androidxTestJunit" }
androidx-test-espresso = { module = "androidx.test.espresso:espresso-core", version.ref ="androidxTestEspresso" }
androidx-test-runner = { module = "androidx.test:runner", version.ref = "runner" }
androidx-test-rules = { module = "androidx.test:rules", version = "1.6.1" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockitoCore" }
androidx-test-core={module="androidx.test:core",version.ref = "androidxTestCore"}


roborazzi = { module = "io.github.takahirom.roborazzi:roborazzi", version.ref = "roborazzi" }
roborazzi-compose = { module = "io.github.takahirom.roborazzi:roborazzi-compose", version.ref = "roborazzi" }
roborazzi-compose-preview-scanner-support = { module = "io.github.takahirom.roborazzi:roborazzi-compose-preview-scanner-support", version.ref = "roborazzi" }
composable-preview-scanner = { module = "com.github.sergio-sastre.ComposablePreviewScanner:android", version.ref = "composablePreviewScanner" }
androidx-compose-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4", version.ref = "composeUiTest" }
androidx-compose-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest", version.ref = "composeUiTest" }
leakcanary-android = { module = "com.squareup.leakcanary:leakcanary-android", version.ref ="leakcanary" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanistPermissions" }
paging-common = {group = "androidx.paging", name = "paging-common-ktx", version.ref = "pagging_version"}
google-code-gson = {group = "com.google.code.gson", name = "gson", version.ref = "gson"}
crypto = {group = "androidx.security", name = "security-crypto", version.ref = "crypto"}

#Pager
appcompanist-pager = {group = "com.google.accompanist", name = "accompanist-pager", version.ref = "accompanistPagerVersion"}
appcompanist-pager-indicator = {group = "com.google.accompanist", name = "accompanist-pager-indicators", version.ref = "accompanistPagerVersion"}
appcompanist-placeholder = {group = "com.google.accompanist", name = "accompanist-placeholder-material", version.ref = "placeholder_version"}


#Room Database
room = {group = "androidx.room", name = "room-runtime", version.ref = "room_version"}
room-compiler = {group = "androidx.room", name = "room-compiler", version.ref = "room_version"}
room-ktx = {group = "androidx.room", name = "room-ktx", version.ref = "room_version"}
identity-jvm = { group = "com.android.identity", name = "identity-jvm", version.ref = "identityJvmVersion" }

google-firebase-crashlytics-ktx = { group = "com.google.firebase", name = "firebase-crashlytics-ktx", version.ref = "firebaseCrashlyticsKtxVersion" }
play-services-measurement-api = { group = "com.google.android.gms", name = "play-services-measurement-api", version.ref = "playServicesMeasurementApiVersion" }
firebase-messaging-ktx = { group = "com.google.firebase", name = "firebase-messaging-ktx", version.ref = "firebaseMessagingKtxVersion" }
firebase-config-ktx = { group = "com.google.firebase", name = "firebase-config-ktx", version.ref = "firebaseConfigKtx" }
testng = { group = "org.testng", name = "testng", version.ref = "testng" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
firebase-config = { group = "com.google.firebase", name = "firebase-config", version.ref = "firebaseConfig" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-ui-android = { group = "androidx.compose.ui", name = "ui-android", version.ref = "uiAndroid" }

exoplayer-core = { module = "com.google.android.exoplayer:exoplayer", version.ref = "exoplayer" }
exoplayer-ui   = { module = "com.google.android.exoplayer:exoplayer-ui", version.ref = "exoplayer" }

[plugins]
# Android
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }

# Kotlin
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-plugin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
kotlin-plugin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }

# Java
java-library = { id = "org.gradle.java-library" }

# Compose
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

# Firebase
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
firebase-appdistribution = { id = "com.google.firebase.appdistribution", version.ref ="appdistribution"}
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }

# KSP
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }

# Hilt
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }

# AboutLibraries
aboutlibraries = { id = "com.mikepenz.aboutlibraries.plugin", version.ref = "aboutlibraries" }

# Lint
android-lint = { id = "com.android.lint" }
ktlint-gradle = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "klint" }

# Roborazzi
roborazzi = { id = "io.github.takahirom.roborazzi", version.ref = "roborazzi" }

# build-logic
buildlogic-android-application = { id = "buildlogic.android.application", version = "unspecified" }
buildlogic-android-library = { id = "buildlogic.android.library", version = "unspecified" }
buildlogic-android-compose = { id = "buildlogic.android.compose", version = "unspecified" }
buildlogic-android-hilt = { id = "buildlogic.android.hilt", version = "unspecified" }
buildlogic-roborazzi = { id = "buildlogic.roborazzi", version = "unspecified" }
buildlogic-retrofit={id="buildlogic.retrofit",version="unspecified"}
