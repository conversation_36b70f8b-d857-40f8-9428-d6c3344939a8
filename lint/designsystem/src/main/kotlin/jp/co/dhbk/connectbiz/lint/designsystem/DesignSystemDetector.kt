package jp.co.dhbk.connectbiz.lint.designsystem

import com.android.tools.lint.client.api.UElementHandler
import com.android.tools.lint.detector.api.Category
import com.android.tools.lint.detector.api.Detector
import com.android.tools.lint.detector.api.Implementation
import com.android.tools.lint.detector.api.Issue
import com.android.tools.lint.detector.api.JavaContext
import com.android.tools.lint.detector.api.Scope
import com.android.tools.lint.detector.api.Severity
import org.jetbrains.uast.UCallExpression
import org.jetbrains.uast.UElement

class DesignSystemDetector : Detector(), Detector.UastScanner {
    override fun getApplicableUastTypes(): List<Class<out UElement>> = listOf(
        UCallExpression::class.java,
    )

    override fun createUastHandler(context: JavaContext): UElementHandler =
        object : UElementHandler() {
            override fun visitCallExpression(node: UCallExpression) {
                val name = node.methodName ?: return
                val preferredName = METHOD_NAMES[name] ?: return
                context.report(
                    ISSUE,
                    node,
                    context.getLocation(node),
                    "Using $name instead of $preferredName",
                )
            }
        }

    companion object {
        @JvmField
        val ISSUE: Issue =
            Issue.create(
                id = "DesignSystem",
                briefDescription = "Design system",
                explanation = "This method does not conform to design system",
                category = Category.CUSTOM_LINT_CHECKS,
                severity = Severity.ERROR,
                implementation =
                Implementation(
                    DesignSystemDetector::class.java,
                    Scope.JAVA_FILE_SCOPE,
                ),
            )

        val METHOD_NAMES =
            mapOf(
                "MaterialTheme" to "TemplateTheme",
                "showSnackbar" to "showNormalSnackbar",
                "AsyncImage" to "NetworkImage",
            )
    }
}
