package jp.co.dhbk.connectbiz.data.network.biometricauth

object Constants {
    const val AUTHENTICATION_SUCCEEDED = "Login successful! Redirecting..."

    const val BIOMETRIC_AUTHENTICATION = "\"CONNECT-BIZ\"でアプリ認証 を使用します"
    const val PASSWORD_AUTHENTICATION_TITLE = "認証できませんでした"
    const val BIOMETRIC_AUTHENTICATION_SUBDESCRIPTION = "ロックを解除"
    const val PASSWORD_AUTHENTICATION_DESCRIPTION = "設定を続ける場合はもう一度お試し下さい"
    const val BIOMETRIC_AUTHENTICATION_NEGATIVE_BUTTON = "キャンセル"
    const val AUTHENTICATE_OTHER = "パターン/ピン/パスワード"
    const val AUTHENTICATE_FINGERPRINT = "Authenticate Using Fingerprint"
    const val AUTHENTICATION_FAILED = "Authentication Failed. Please try again."
    const val AUTHENTICATION_SUCCESSFUL = "Authentication Successful! Welcome Back!"
    const val AUTHENTICATION_ERROR = "An error occurred during authentication. " +
        "Please try again later."
    const val BIOMETRIC_AUTHENTICATION_PROMPT = "Please place your finger on the scanner."
    const val BIOMETRIC_UNAVAILABLE = "Biometric authentication is not " +
        "available on this device."
    const val DEVICE_NOT_SECURE = "Your device is not secure. Please set up a screen lock."
    const val SETTINGS_PROMPT = "To use biometric authentication, please enable it " +
        "in your device settings."
    const val LOGIN_FAILED = "Login failed. Please check your credentials."
    const val LOGIN_SUCCESS = "Login successful! Redirecting..."

    const val AVAILABLE = "Available"
    const val UNAVAILABLE = "Unavailable"
    const val TRUE = "True"
    const val FALSE = "False"
    const val CANCEL = "Cancel"
    const val PASSWORD_PIN_AUTHENTICATION = "Password/PIN Authentication"
    const val PASSWORD_PIN_AUTHENTICATION_SUBTITLE = "Secure Your Account with Password/PIN"
    const val PASSWORD_PIN_AUTHENTICATION_DESCRIPTION = "This app uses your device's " +
        "password or PIN to securely authenticate your identity."
    const val PASSWORD_PIN_AUTHENTICATION_NEGATIVE_BUTTON = "Cancel"
    const val PASSWORD_PIN_AUTHENTICATION_PROMPT = "Please enter your " +
        "password or PIN to continue."
    const val PASSWORD_INCORRECT = "The entered password/PIN is incorrect. Please try again."
    const val PASSWORD_AUTHENTICATION_SUCCESS = "Authentication Successful! Access Granted."
    const val PASSWORD_AUTHENTICATION_FAILED = "Authentication Failed. " +
        "Please verify your credentials."
    const val PASSWORD_PIN_AUTHENTICATION_UNAVAILABLE = "Password/PIN " +
        "authentication is not available on this device."
    const val DEVICE_LOCK_REQUIRED = "Your device needs to be locked with a " +
        "password or PIN for secure access."
    const val SETTINGS_PASSWORD_PROMPT = "To use password/PIN authentication, " +
        "please set it up in your device settings."

    const val PATTERN_AUTHENTICATION = "Pattern Authentication"
    const val PATTERN_AUTHENTICATION_SUBTITLE = "Secure your Account with Pattern"
    const val PATTERN_AUTHENTICATION_DESCRIPTION = "This app uses your device's " +
        "Pattern to securely authenticate your identity."

    const val DEVICE_CREDENTIAL_AUTHENTICATION = "Secure Your Device"
    const val DEVICE_CREDENTIAL_AUTHENTICATION_SUBTITLE = "Authenticate with " +
        "Your Device Credentials"
    const val DEVICE_CREDENTIAL_AUTHENTICATION_DESCRIPTION = "Please enter your device's " +
        "PIN, password, or pattern to continue. This ensures that your information " +
        "remains secure."
}
