package jp.co.dhbk.connectbiz.data.network.respositories

import android.app.Activity
import android.content.Intent
import androidx.fragment.app.FragmentActivity
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow

interface BiometricAuthRepository {
    fun authenticate(activity: FragmentActivity, screenType: ScreenType): Flow<BiometricAuthResult>
    fun canAuthenticate(screenType: ScreenType): AuthenticationStatus
    fun getFallbackAuthenticationIntent(activity: Activity, screenType: ScreenType): Intent?
    fun handleActivityResult(requestCode: Int, resultCode: Int): BiometricAuthResult
    fun getFallbackResult(): SharedFlow<BiometricAuthResult>
}
