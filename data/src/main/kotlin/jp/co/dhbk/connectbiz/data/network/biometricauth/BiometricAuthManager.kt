package jp.co.dhbk.connectbiz.data.network.biometricauth

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.CancellationSignal
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import timber.log.Timber
import javax.inject.Inject

// Sealed class for Biometric Authentication Result
sealed class BiometricAuthResult {
    data class Success(val data: BiometricAuthResultData) : BiometricAuthResult()
    data class Failure(val error: BiometricAuthResultData) : BiometricAuthResult()
    object Loading : BiometricAuthResult()
}

// Data class for successful authentication results
data class BiometricAuthResultData(
    val statusCode: Int, // HTTP-like status code for the result
    val biometricStatus: BiometricStatus, // Status of biometric authentication
    val attemptCount: Int, // Number of authentication attempts made
)

class BiometricAuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val biometricKeyService: BiometricKeyService,
) {
    private var failureCount = 0
    private lateinit var biometricPrompt: BiometricPrompt
    private var cancellationSignal: CancellationSignal? = null
    private var isAuthenticating = false // Track authentication state
    private val _fallbackResult = MutableSharedFlow<BiometricAuthResult>(replay = 1)
    val fallbackResult: SharedFlow<BiometricAuthResult> = _fallbackResult

    // Check if biometric authentication can be performed
    fun canAuthenticate(screenType: ScreenType): AuthenticationStatus {
        val biometricManager = BiometricManager.from(context)

        // Check if biometric hardware is available
        return when {
            DeviceUtilities.isBiometricHardwareAvailable(context) -> {
                when (
                    biometricManager.canAuthenticate(
                        if (screenType == ScreenType.SPLASH) {
                            BiometricManager.Authenticators.BIOMETRIC_STRONG or BiometricManager
                                .Authenticators.DEVICE_CREDENTIAL
                        } else {
                            BiometricManager.Authenticators.BIOMETRIC_STRONG
                        },
                    )
                ) {
                    BiometricManager.BIOMETRIC_SUCCESS ->
                        AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED

                    BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
                        // Biometric hardware exists but no biometrics are enrolled
                        if (DeviceUtilities.deviceHasPasswordPinLock(context)) {
                            AuthenticationStatus.BIOMETRIC_NOT_ENROLLED
                        } else {
                            AuthenticationStatus.DEVICE_NOT_SECURE
                        }
                    }

                    BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE,
                    BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED,
                    BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED,
                    -> {
                        val isFingerprintHardwarePresent = context.packageManager.hasSystemFeature(
                            PackageManager.FEATURE_FINGERPRINT,
                        )

                        if (isFingerprintHardwarePresent) {
                            AuthenticationStatus.BIOMETRIC_NOT_ENROLLED
                        } else {
                            AuthenticationStatus.SECURITY_UPDATE_REQUIRED
                        }
                    }
                    // Handle cases where biometric hardware is unavailable or the device is secure
                    BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
                        if (DeviceUtilities.deviceHasPasswordPinLock(context)) {
                            AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
                        } else {
                            AuthenticationStatus.DEVICE_NOT_SECURE
                        }
                    }

                    else -> AuthenticationStatus.DEVICE_NOT_SECURE
                }
            }

            else -> {
                // Handle cases where biometric hardware is unavailable or the device is not secure
                if (DeviceUtilities.deviceHasPasswordPinLock(context)) {
                    AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
                } else {
                    AuthenticationStatus.DEVICE_NOT_SECURE
                }
            }
        }
    }
    fun authenticate(
        activity: FragmentActivity,
        screenType: ScreenType,
        onResult: (BiometricAuthResult) -> Unit,
    ) {
        if (isAuthenticating) return
        isAuthenticating = true

        cancellationSignal = CancellationSignal().apply {
            setOnCancelListener { Timber.d("Authentication was canceled.") }
        }

        val executor = ContextCompat.getMainExecutor(context)
        val biometricManager = BiometricManager.from(context)
        val keyguardManager =
            context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager

        val authFlag = BiometricManager.Authenticators.BIOMETRIC_STRONG

        val authFlagFallback = BiometricManager.Authenticators.BIOMETRIC_STRONG or
            BiometricManager.Authenticators.DEVICE_CREDENTIAL

        val biometricResult =
            biometricManager.canAuthenticate(
                if (screenType == ScreenType.SPLASH) {
                    BiometricManager.Authenticators.BIOMETRIC_STRONG or BiometricManager
                        .Authenticators.DEVICE_CREDENTIAL
                } else {
                    BiometricManager.Authenticators.BIOMETRIC_STRONG
                },
            )

        // Check if biometric authentication is available
        val biometricResultFallback =
            biometricManager.canAuthenticate(
                BiometricManager.Authenticators.BIOMETRIC_STRONG,
            )

        // Check if device credential authentication is available
        val deviceCredentialResultFallback =
            biometricManager.canAuthenticate(
                BiometricManager.Authenticators.DEVICE_CREDENTIAL,
            )

        val promptInfo: BiometricPrompt.PromptInfo = when {
            biometricResultFallback == BiometricManager.BIOMETRIC_SUCCESS -> {
                if (screenType == ScreenType.SPLASH) {
                    BiometricPrompt.PromptInfo.Builder()
                        .setTitle(Constants.BIOMETRIC_AUTHENTICATION)
                        .setDescription(Constants.BIOMETRIC_AUTHENTICATION_SUBDESCRIPTION)
                        .setAllowedAuthenticators(authFlagFallback)
                        .setConfirmationRequired(false)
                        .build()
                } else {
                    BiometricPrompt.PromptInfo.Builder()
                        .setTitle(Constants.BIOMETRIC_AUTHENTICATION)
                        .setDescription(Constants.BIOMETRIC_AUTHENTICATION_SUBDESCRIPTION)
                        .setAllowedAuthenticators(authFlag)
                        .setNegativeButtonText(Constants.BIOMETRIC_AUTHENTICATION_NEGATIVE_BUTTON)
                        .setConfirmationRequired(false)
                        .build()
                }
            }
            deviceCredentialResultFallback == BiometricManager.BIOMETRIC_SUCCESS -> {
                BiometricPrompt.PromptInfo.Builder()
                    .setTitle(Constants.BIOMETRIC_AUTHENTICATION)
                    .setDescription(Constants.BIOMETRIC_AUTHENTICATION_SUBDESCRIPTION)
                    .setAllowedAuthenticators(BiometricManager.Authenticators.DEVICE_CREDENTIAL)
                    .setDeviceCredentialAllowed(true)
                    .build()
            }

            biometricResult == BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED ||
                keyguardManager.isKeyguardSecure -> {
                BiometricPrompt.PromptInfo.Builder()
                    .setTitle(Constants.PASSWORD_AUTHENTICATION_TITLE)
                    .setDescription(Constants.PASSWORD_AUTHENTICATION_DESCRIPTION)
                    .setAllowedAuthenticators(BiometricManager.Authenticators.DEVICE_CREDENTIAL)
                    .setDeviceCredentialAllowed(true)
                    .build()
            }

            biometricResult == BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE &&
                keyguardManager.isKeyguardSecure -> {
                // No biometric hardware but secure lock screen exists
                BiometricPrompt.PromptInfo.Builder()
                    .setTitle(Constants.PASSWORD_AUTHENTICATION_TITLE)
                    .setDescription(Constants.PASSWORD_AUTHENTICATION_DESCRIPTION)
                    .setAllowedAuthenticators(BiometricManager.Authenticators.DEVICE_CREDENTIAL)
                    .setDeviceCredentialAllowed(true)
                    .build()
            }

            else -> {
                val errorMessage = BiometricStatus.fromErrorCode(500, "")
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(501, errorMessage, 0),
                    ),
                )
                isAuthenticating = false
                return
            }
        }

        biometricPrompt = BiometricPrompt(
            activity, executor,
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationSucceeded(
                    result: BiometricPrompt.AuthenticationResult,
                ) {
                    val cryptoObject = result.cryptoObject
                    if (cryptoObject != null && cryptoObject.cipher != null) {
                        // Perform secure operations only if the cipher is valid
                        handleAuthSuccess(result.authenticationType, onResult)
                    } else {
                        handleAuthFailure(screenType, onResult)
                    }
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    Timber.e("onAuthenticationError $errorCode Failure count $failureCount")
                    handleAuthError(errorCode, errString, onResult, screenType)
                }

                override fun onAuthenticationFailed() {
                    failureCount++
                    Timber.e("onAuthenticationFailed $failureCount")
                    handleAuthFailure(screenType, onResult)
                }
            },
        )
        val cipher = biometricKeyService.getEncryptCipher()
        biometricPrompt.authenticate(promptInfo, BiometricPrompt.CryptoObject(cipher))
    }

    private fun handleAuthSuccess(
        authenticationType: Int,
        onResult: (BiometricAuthResult) -> Unit,
    ) {
        isAuthenticating = false
        cancellationSignal = null
        failureCount = 0
        val statusCode = if (authenticationType ==
            BiometricPrompt.AUTHENTICATION_RESULT_TYPE_BIOMETRIC
        ) {
            200
        } else {
            201
        }
        val successMessage = BiometricStatus.fromErrorCode(statusCode, "")
        Timber.e("Authentication Success $authenticationType Message $successMessage")
        onResult(
            BiometricAuthResult.Success(
                BiometricAuthResultData(
                    statusCode,
                    successMessage,
                    0,
                ),
            ),
        )
        failureCount = 0
    }

    private fun handleAuthError(
        errorCode: Int,
        errString: CharSequence,
        onResult: (BiometricAuthResult) -> Unit,
        screenType: ScreenType,
    ) {
        isAuthenticating = false
        when (errorCode) {
            BiometricPrompt.ERROR_LOCKOUT_PERMANENT,
            -> {
                Timber.e("Face ID locked out due to too many failed attempts.")
                biometricPrompt.cancelAuthentication()
                val errorMessage = BiometricStatus.fromErrorCode(errorCode, errString.toString())
                Timber.e(
                    "Authentication errorCode $errorCode errorMessage " +
                        "$errString failureCount $failureCount",
                )
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            errorCode,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }
            BiometricPrompt.ERROR_USER_CANCELED,
            BiometricPrompt.ERROR_CANCELED,
            BiometricPrompt.ERROR_NEGATIVE_BUTTON,
            -> {
                val errorMessage = BiometricStatus.fromErrorCode(errorCode, errString.toString())
                Timber.e(
                    "Authentication errorCode $errorCode errorMessage " +
                        "$errString failureCount $failureCount",
                )
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            errorCode,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }
            BiometricPrompt.ERROR_TIMEOUT,
            BiometricPrompt.ERROR_UNABLE_TO_PROCESS,
            BiometricPrompt.ERROR_HW_UNAVAILABLE,
            -> {
                Timber.e("Face not detected Error Code Detected $errorCode")
                val errorMessage = BiometricStatus.fromErrorCode(errorCode, errString.toString())
                Timber.e(
                    "Authentication errorCode $errorCode errorMessage " +
                        "$errString failureCount $failureCount",
                )
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            errorCode,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }

            else -> {
                val errorMessage = BiometricStatus.fromErrorCode(errorCode, errString.toString())
                Timber.e(
                    "Authentication errorCode $errorCode errorMessage " +
                        "$errString failureCount $failureCount",
                )
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            errorCode,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }
        }
    }

    private fun handleAuthFailure(
        screenType: ScreenType,
        onResult: (BiometricAuthResult) -> Unit,
    ) {
        when {
            failureCount == 3 && screenType == ScreenType.HOME -> {
                Timber.e("Too many failed attempts. Cancelling authentication.")
                biometricPrompt.cancelAuthentication()
            }
            failureCount == 3 && screenType == ScreenType.SPLASH -> {
                Timber.e("Too many failed attempts. Cancelling authentication.")
                biometricPrompt.cancelAuthentication()
            }
            failureCount == 3 && screenType == ScreenType.SETTINGS -> {
                Timber.e("Too many failed attempts. Cancelling authentication.")
                biometricPrompt.cancelAuthentication()
            }
            failureCount == 5 && screenType == ScreenType.SETTINGS -> {
                Timber.e("Too many failed attempts. Cancelling authentication.")
                biometricPrompt.cancelAuthentication()
                val errorMessage = BiometricStatus.fromErrorCode(10, "Authentication Cancel")
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            10,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }
            failureCount == 5 && screenType == ScreenType.HOME -> {
                Timber.e("Too many failed attempts. Cancelling authentication.")
                biometricPrompt.cancelAuthentication()
                val errorMessage = BiometricStatus.fromErrorCode(10, "Authentication Cancel")
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            10,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }
            failureCount == 2 && screenType == ScreenType.SETTINGS -> {
                Timber.e("Too many failed attempts. Cancelling authentication.")
                val errorMessage = BiometricStatus.fromErrorCode(10, "Authentication Cancel")
                onResult(
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(
                            10,
                            errorMessage,
                            failureCount,
                        ),
                    ),
                )
            }
        }
    }

    fun getFallbackAuthenticationIntent(activity: Activity): Intent? {
        val keyguardManager = activity.getSystemService(
            Context.KEYGUARD_SERVICE,
        ) as KeyguardManager
        return if (keyguardManager.isKeyguardSecure) {
            keyguardManager.createConfirmDeviceCredentialIntent(
                Constants.PASSWORD_AUTHENTICATION_TITLE,
                Constants.PASSWORD_AUTHENTICATION_DESCRIPTION,
            )
        } else {
            null
        }
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int) {
        val successMessage = BiometricStatus.fromErrorCode(
            200,
            "Authentication succeeded with device credentials.",
        )
        val failureMessage = BiometricStatus.fromErrorCode(
            400,
            "Authentication failed. Please try again.",
        )
        val cancelMessage = BiometricStatus.fromErrorCode(
            401,
            "Authentication canceled by user.",
        )
        if (requestCode == REQUEST_CODE_KEYGUARD_AUTHENTICATION) {
            val result = when (resultCode) {
                Activity.RESULT_OK -> BiometricAuthResult.Success(
                    BiometricAuthResultData(200, successMessage, 0),
                )
                Activity.RESULT_CANCELED -> BiometricAuthResult.Failure(
                    BiometricAuthResultData(401, cancelMessage, failureCount),
                )
                else -> {
                    failureCount++
                    BiometricAuthResult.Failure(
                        BiometricAuthResultData(400, failureMessage, failureCount),
                    )
                }
            }
            _fallbackResult.tryEmit(result)
        }
    }

    companion object {
        const val REQUEST_CODE_KEYGUARD_AUTHENTICATION = 1001
    }
}
