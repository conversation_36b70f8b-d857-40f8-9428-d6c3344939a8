package jp.co.dhbk.connectbiz.data.network.respositories.impls

import jp.co.dhbk.connectbiz.data.network.apis.ApiService
import jp.co.dhbk.connectbiz.domain.model.DeviceTokenRequest
import jp.co.dhbk.connectbiz.domain.model.DeviceTokenResponse
import jp.co.dhbk.connectbiz.domain.model.error.ApiException
import jp.co.dhbk.connectbiz.domain.model.error.DeviceTokenError
import jp.co.dhbk.connectbiz.domain.repository.DeviceTokenRepository
import timber.log.Timber
import java.io.IOException

class DeviceTokenRepositoryImpl(
    private val apiService: ApiService,
) : DeviceTokenRepository {

    override suspend fun sendDeviceToken(request: DeviceTokenRequest): Result<DeviceTokenResponse> {
        return try {
            val response = apiService.sendDeviceToken(request)
            Timber.e("Status Code: ${response.code()}")
            Timber.e("Message: ${response.message()}")

            if (response.isSuccessful) {
                val responseBody = response.body()

                // Handle empty or invalid response body
                if (responseBody == null || responseBody.toString().trim().isEmpty()) {
                    Timber.e("Empty or invalid response body")
                    // Return a fallback success response (e.g., "Token Updated Successfully!")
                    Result.success(DeviceTokenResponse("Token Updated Successfully!"))
                } else {
                    // Parse the valid response body
                    Result.success(responseBody)
                }
            } else {
                // Handle specific HTTP error codes and map them to custom errors
                val error = when (response.code()) {
                    400 -> DeviceTokenError.InvalidInput
                    401 -> DeviceTokenError.UnAuthorized
                    500 -> DeviceTokenError.TokenExpired
                    409 -> DeviceTokenError.ConflictUser
                    else -> DeviceTokenError.UnknownError(
                        "Unexpected error code: ${response.code()}",
                    )
                }
                Timber.e("Error: $error")
                Result.failure(ApiException(response.code(), error.toString()))
            }
        } catch (e: IOException) {
            // Handle network errors like no internet connection or timeouts
            Timber.e("IOException: ${e.localizedMessage}")
            Result.failure(Exception(DeviceTokenError.NetworkError.toString()))
        } catch (e: Exception) {
            // Catch any other unexpected exceptions (e.g., malformed JSON)
            Timber.e("Exception: ${e.localizedMessage}")
            Result.failure(
                Exception(
                    DeviceTokenError.UnknownError(
                        e.message ?: "Unknown error",
                    ).toString(),
                ),
            )
        }
    }
}
