package jp.co.dhbk.connectbiz.data.network.apis

import jp.co.dhbk.connectbiz.domain.model.DeviceTokenRequest
import jp.co.dhbk.connectbiz.domain.model.DeviceTokenResponse
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Streaming
import retrofit2.http.Url

interface ApiService {
    @POST("/api/deviceTokenApi/deviceToken/entry")
    suspend fun sendDeviceToken(@Body request: DeviceTokenRequest): Response<DeviceTokenResponse>

    @POST
    @Streaming
    suspend fun downloadFile(@Url fileUrl: String): Response<ResponseBody>
}
