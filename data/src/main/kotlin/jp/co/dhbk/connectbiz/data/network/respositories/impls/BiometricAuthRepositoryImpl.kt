package jp.co.dhbk.connectbiz.data.network.respositories.impls

import android.app.Activity
import android.content.Intent
import androidx.fragment.app.FragmentActivity
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthManager
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResultData
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.data.network.respositories.BiometricAuthRepository
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.callbackFlow
import timber.log.Timber
import javax.inject.Inject

class BiometricAuthRepositoryImpl @Inject constructor(
    private val biometricAuthManager: BiometricAuthManager,
) : BiometricAuthRepository {

    override fun canAuthenticate(screenType: ScreenType): AuthenticationStatus {
        return biometricAuthManager.canAuthenticate(screenType)
    }

    override fun authenticate(
        activity: FragmentActivity,
        screenType: ScreenType,
    ): Flow<BiometricAuthResult> = callbackFlow {
        try {
            biometricAuthManager.authenticate(activity, screenType) { result ->
                Timber.d("Authentication result: $result")
                trySend(result) // Send authentication result to the flow
            }
        } catch (e: Exception) {
            val errorMessage = BiometricStatus.fromErrorCode(
                400,
                e.message.toString(),
            )
            trySend(
                BiometricAuthResult.Failure(
                    BiometricAuthResultData(
                        400,
                        errorMessage,
                        3,
                    ),
                ),
            ) // Send failure result
        }

        // Close the flow when authentication completes
        awaitClose {
            // Clean up any resources if necessary
        }
    }

    override fun getFallbackAuthenticationIntent(
        activity: Activity,
        screenType: ScreenType,
    ): Intent? {
        return biometricAuthManager.getFallbackAuthenticationIntent(activity)
    }

    override fun handleActivityResult(requestCode: Int, resultCode: Int): BiometricAuthResult {
        biometricAuthManager.handleActivityResult(requestCode, resultCode)
        return biometricAuthManager.fallbackResult.replayCache.lastOrNull()
            ?: BiometricAuthResult.Loading
    }

    override fun getFallbackResult(): SharedFlow<BiometricAuthResult> {
        Timber.d("Repository fallback result: ${biometricAuthManager.fallbackResult.replayCache}")
        return biometricAuthManager.fallbackResult
    }
}
