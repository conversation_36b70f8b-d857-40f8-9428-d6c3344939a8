package jp.co.fuller.template.data.network.interceptors

import android.os.Build
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import okhttp3.Interceptor
import okhttp3.Response
import kotlin.text.split

/**
 * User-Agent を設定するための [Interceptor]
 *
 * see: https://www.notion.so/fuller-inc/3-fbe25adfd3294f46864f6496687eab2a#7bd4501ce2344c4682f6d2c20dd0baca
 *
 * @param apiConfig [ApiConfig]
 */
internal class UserAgentInterceptor(apiConfig: ApiConfig) : Interceptor {
    private val userAgent =
        "developer=fuller-inc, " +
            "build=${apiConfig.versionCode};" +
            "version=\"${apiConfig.versionName.split("-").first()}\", " +
            "platform=android;model=\"${Build.MODEL}\";os_version=\"${Build.VERSION.RELEASE}\""

    override fun intercept(chain: Interceptor.Chain): Response = chain.proceed(
        chain.request()
            .newBuilder()
            .header("User-Agent", userAgent)
            .build(),
    )
}
