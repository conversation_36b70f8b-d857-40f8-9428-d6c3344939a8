package jp.co.dhbk.connectbiz.data.network.interceptors

import android.webkit.CookieManager
import okhttp3.Cookie
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber

class DynamicCookieInterceptor : Interceptor {

    private val cookieJar = CookieJar()

    override fun intercept(chain: Interceptor.Chain): Response {
        // Get cookies from the CookieJar (if any) for the request URL
        val cookies = cookieJar.loadForRequest(chain.request().url)
        Timber.d("Sending cookies: $cookies") // Log outgoing cookies
        val request = chain.request().newBuilder()
            .apply {
                cookies.forEach { cookie ->
                    addHeader("Cookie", cookie.toString()) // Add cookies to the request header
                }
            }
            .build()

        // Proceed with the request
        val response = chain.proceed(request)

        // Save cookies from the response (if any)
        response.headers("Set-Cookie").forEach { cookieHeader ->
            // Ensure cookies are parsed and saved as a list
            val parsedCookies = Cookie.parseAll(chain.request().url, response.headers)
            cookieJar.saveFromResponse(chain.request().url, parsedCookies)
        }

        return response
    }

    // Simple CookieJar implementation (you can replace this with a more complex one if needed)
    private class CookieJar : okhttp3.CookieJar {
        private val cookieStore = mutableMapOf<String, MutableList<Cookie>>()

        override fun loadForRequest(url: HttpUrl): List<Cookie> {
            Timber.e("URL $url")
            val host = url.host
            val cookieHeader = CookieManager.getInstance().getCookie(
                url.toString(),
            ) // Fetch cookies for the URL
            Timber.d("Loaded cookies from CookieManager for $host: $cookieHeader")

            return cookieHeader?.split(";")?.mapNotNull { cookieString ->
                Cookie.parse(url, cookieString.trim())
            } ?: emptyList()
        }

        override fun saveFromResponse(url: HttpUrl, cookies: List<Cookie>) {
            val currentCookies = cookieStore[url.host] ?: mutableListOf()
            cookies.forEach { newCookie ->
                currentCookies.removeIf { it.name == newCookie.name }
                currentCookies.add(newCookie)
            }
            cookieStore[url.host] = currentCookies
        }

        // Helper to check if a cookie has expired
        private fun Cookie.hasExpired(): Boolean {
            return expiresAt < System.currentTimeMillis()
        }
    }
}
