package jp.co.fuller.template.data.network.interceptors

import jp.co.dhbk.connectbiz.core.common.utils.runCatchingOrThrow
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.Response
import java.net.HttpURLConnection

/**
 * メンテナンス時の処理を行うための [Interceptor]
 */
internal class MaintenanceInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        if (response.code == HttpURLConnection.HTTP_UNAVAILABLE) {
            val message =
                runCatchingOrThrow {
                    val json = response.peekBody(Long.MAX_VALUE).string()
                    val format = Json { ignoreUnknownKeys = true }
                    val responseData = format.decodeFromString<MaintenanceResponse>(json)
                    responseData.maintenance.message
                }
        }
        return response
    }

    /**
     * メンテナンス時のレスポンス
     */
    @Serializable
    private data class MaintenanceResponse(val maintenance: Maintenance) {
        @Serializable
        data class Maintenance(val message: String)
    }
}
