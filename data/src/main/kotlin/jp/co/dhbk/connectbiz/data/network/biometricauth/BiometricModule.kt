package jp.co.dhbk.connectbiz.data.network.biometricauth

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import jp.co.dhbk.connectbiz.data.network.respositories.BiometricAuthRepository
import jp.co.dhbk.connectbiz.data.network.respositories.impls.BiometricAuthRepositoryImpl

@Module
@InstallIn(SingletonComponent::class)
abstract class BiometricModule {

    @Binds
    abstract fun bindBiometricAuthRepository(
        biometricAuthRepositoryImpl: BiometricAuthRepositoryImpl,
    ): BiometricAuthRepository
}
