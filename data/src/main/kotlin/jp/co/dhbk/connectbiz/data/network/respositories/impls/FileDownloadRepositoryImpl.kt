package jp.co.dhbk.connectbiz.data.network.respositories.impls

import jp.co.dhbk.connectbiz.core.common.model.MimeTypes
import jp.co.dhbk.connectbiz.data.network.apis.ApiService
import jp.co.dhbk.connectbiz.domain.repository.FileDownloadRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class FileDownloadRepositoryImpl(
    private val apiService: ApiService,
) : FileDownloadRepository {

    override suspend fun downloadFile(fileUrl: String, destinationPath: String): Flow<Int> = flow {
        val mediaType = MimeTypes.application.JSON.toMediaTypeOrNull()
        val json = """{"key": "value"}""" // Example of JSON data
        val requestBody = json.toRequestBody(mediaType)
        val response = apiService.downloadFile(fileUrl)
        Timber.e("Response Header ${response.headers()}")
        if (response.isSuccessful && response.body() != null) {
            val body = response.body()!!
            val file = File(destinationPath)
            val inputStream = body.byteStream()
            val outputStream = FileOutputStream(file)

            val totalSize = body.contentLength()
            var downloadedSize = 0L
            val buffer = ByteArray(8 * 1024)

            while (true) {
                val read = inputStream.read(buffer)
                if (read == -1) break
                outputStream.write(buffer, 0, read)
                downloadedSize += read

                val progress = ((downloadedSize * 100) / totalSize).toInt()
                emit(progress)
            }

            outputStream.flush()
            outputStream.close()
            inputStream.close()

            emit(100) // Complete
        } else {
            throw IOException("Failed to download file")
        }
    }.catch { e ->
        // Catch the exception and emit an error progress value or rethrow
        emit(-1) //  emit any custom value to indicate failure
        throw e // Optionally rethrow the exception to propagate it further
    }
}
