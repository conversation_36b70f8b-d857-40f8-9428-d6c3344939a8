package jp.co.dhbk.connectbiz.data.network.biometricauth

import android.app.KeyguardManager
import android.content.Context
import android.content.pm.PackageManager
import androidx.appcompat.app.AppCompatActivity
import androidx.biometric.BiometricManager

object DeviceUtilities {
    fun isBiometricHardwareAvailable(con: Context): Boolean {
        val biometricManager = BiometricManager.from(con)

        // Check for biometric availability
        val canAuthenticate = biometricManager.canAuthenticate(
            BiometricManager.Authenticators.BIOMETRIC_STRONG,
        )

        // Check for biometric hardware (fingerprint sensor specifically)
        val isFingerprintHardwarePresent = con.packageManager.hasSystemFeature(
            PackageManager.FEATURE_FINGERPRINT,
        )

        return when (canAuthenticate) {
            BiometricManager.BIOMETRIC_SUCCESS -> true
            BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> true
            BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> isFingerprintHardwarePresent
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> true
            BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED -> false
            BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED -> false
            BiometricManager.BIOMETRIC_STATUS_UNKNOWN -> false
            else -> false
        }
    }

    fun deviceHasPasswordPinLock(con: Context): Boolean {
        val keyguardManager = con.getSystemService(
            AppCompatActivity.KEYGUARD_SERVICE,
        ) as KeyguardManager
        return keyguardManager.isKeyguardSecure
    }
}
