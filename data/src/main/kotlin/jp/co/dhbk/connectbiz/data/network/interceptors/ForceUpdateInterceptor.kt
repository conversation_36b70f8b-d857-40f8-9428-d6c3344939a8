package jp.co.fuller.template.data.network.interceptors

import okhttp3.Interceptor
import okhttp3.Response
import java.net.HttpURLConnection

/**
 * 強制アップデートが必要な時の処理を行うための [Interceptor]
 */
internal class ForceUpdateInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        if (response.code == HttpURLConnection.HTTP_NOT_ACCEPTABLE) {
            // TODO: 強制アップデートが必要な時の処理を行う
        }
        return response
    }
}
