package jp.co.dhbk.connectbiz.data.network

import android.webkit.CookieManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import jp.co.dhbk.connectbiz.core.common.model.MimeTypes
import jp.co.dhbk.connectbiz.data.network.apis.ApiService
import jp.co.dhbk.connectbiz.data.network.interceptors.DynamicCookieInterceptor
import jp.co.fuller.template.data.network.interceptors.ForceUpdateInterceptor
import jp.co.fuller.template.data.network.interceptors.MaintenanceInterceptor
import jp.co.fuller.template.data.network.interceptors.UserAgentInterceptor
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.StringFormat
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
internal object NetworkModule {
    @Provides
    @Singleton
    fun providesUserAgentInterceptor(apiConfig: ApiConfig): UserAgentInterceptor =
        UserAgentInterceptor(apiConfig = apiConfig)

    @Provides
    @Singleton
    fun providesForceUpdateInterceptor(): ForceUpdateInterceptor = ForceUpdateInterceptor()

    @Provides
    @Singleton
    fun providesMaintenanceInterceptor(): MaintenanceInterceptor = MaintenanceInterceptor()

    @Provides
    @Singleton
    fun providesDynamicCookieInterceptor(): DynamicCookieInterceptor {
        return DynamicCookieInterceptor()
    }

    @Provides
    @Singleton
    fun providesHttpLoggingInterceptor(apiConfig: ApiConfig): HttpLoggingInterceptor =

        HttpLoggingInterceptor().apply {
            Timber.e("Is Debug ${apiConfig.isDebug}")
            if (apiConfig.isDebug) {
                setLevel(HttpLoggingInterceptor.Level.BODY)
            } else {
                setLevel(HttpLoggingInterceptor.Level.NONE) // Disable logging in production
            }
        }

    @Provides
    @Singleton
    fun providesOkHttpClient(
        userAgentInterceptor: UserAgentInterceptor,
        forceUpdateInterceptor: ForceUpdateInterceptor,
        maintenanceInterceptor: MaintenanceInterceptor,
        httpLoggingInterceptor: HttpLoggingInterceptor,
        // dynamicCookieInterceptor: DynamicCookieInterceptor,
        apiConfig: ApiConfig,
    ): OkHttpClient {
        // Custom interceptor to add headers
        val headersInterceptor = Interceptor { chain ->
            val originalRequest = chain.request()
            val newRequestBuilder = originalRequest.newBuilder()

            // Check if the request is for CSV download based on URL or other criteria
            if (originalRequest.url.toString().contains("Log/Export")) {
                newRequestBuilder
                    .header(
                        "Content-Type",
                        "application/x-www-form-urlencoded",
                    ) // Content-Type for POST request
                    .header("Accept", "text/csv") // Expecting a CSV response

                // Extract cookies for the specific URL
                /* val cookies = extractCookies(originalRequest.url.toString())
                 if (cookies.isNotEmpty()) {
                     newRequestBuilder.header("Cookie", cookies)
                 }*/
            } else {
                // Default headers for other requests
                newRequestBuilder
                    .header("Content-Type", MimeTypes.application.JSON)
                    .header("Accept", MimeTypes.application.JSON)
            }

            chain.proceed(newRequestBuilder.build())
        }

        val okHttpClientBuilder = OkHttpClient.Builder()
            .addInterceptor(userAgentInterceptor)
            .addInterceptor(forceUpdateInterceptor)
            .addInterceptor(maintenanceInterceptor)
            .addInterceptor(httpLoggingInterceptor)
            .addInterceptor(headersInterceptor)
            // .addInterceptor(dynamicCookieInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .callTimeout(30, TimeUnit.SECONDS)

        // Only trust all certificates if in debug mode
        if (apiConfig.isDebug) {
            Timber.w("Trusting all SSL certificates - for development only")
        } else {
            Timber.e("Production mode - SSL certificates are verified")
        }

        return okHttpClientBuilder.build()
    }

    fun extractCookies(url: String): String {
        // Get the CookieManager instance
        val cookieManager = CookieManager.getInstance()

        // Retrieve cookies for the given URL
        val cookies = cookieManager.getCookie(url)
        Timber.e("Cookie From Network module $cookies")
        // Return the cookies as a string or an empty string if no cookies are found
        return cookies ?: ""
    }

    @OptIn(ExperimentalSerializationApi::class)
    @Provides
    @Singleton
    fun providesRetrofit(apiConfig: ApiConfig, okHttpClient: OkHttpClient): Retrofit {
        val format: StringFormat =
            Json {
                ignoreUnknownKeys = true
                encodeDefaults = true
                // namingStrategy = JsonNamingStrategy.SnakeCase
            }
        val contentType = MimeTypes.application.JSON.toMediaType()
        return Retrofit.Builder()
            .baseUrl(apiConfig.baseUrl)
            .client(okHttpClient)
            .addConverterFactory(format.asConverterFactory(contentType))
            .build()
    }

    @Provides
    @Singleton
    fun providesApiService(retrofit: Retrofit): ApiService = retrofit.create(ApiService::class.java)
}
