package jp.co.dhbk.connectbiz.data.network.biometricauth

enum class BiometricStatus(val code: Int, val message: String) {
    // Success states
    SUCCESS_BIOMETRIC(200, "アプリ認証の設定が完了しました"),
    SUCCESS_DEVICE_CREDENTIAL(201, "アプリパスコード/パターン/PINの設定が完了しました"),

    // Error states
    ERROR_HW_UNAVAILABLE(
        androidx.biometric.BiometricPrompt.ERROR_HW_UNAVAILABLE,
        "Hardware unavailable. Please try again later.",
    ),
    ERROR_UNABLE_TO_PROCESS(
        androidx.biometric.BiometricPrompt.ERROR_UNABLE_TO_PROCESS,
        "Unable to process authentication request.",
    ),
    ERROR_TIMEOUT(
        androidx.biometric.BiometricPrompt.ERROR_TIMEOUT,
        "Authentication timed out. Please try again",
    ),
    ERROR_NO_SPACE(
        androidx.biometric.BiometricPrompt.ERROR_NO_SPACE,
        "Not enough device storage remaining.",
    ),
    ERROR_CANCELED(
        androidx.biometric.BiometricPrompt.ERROR_CANCELED,
        "Operation canceled. Biometric sensor unavailable.",
    ),
    ERROR_LOCKOUT(
        androidx.biometric.BiometricPrompt.ERROR_LOCKOUT,
        "Too many failed attempts. Temporarily locked.",
    ),
    ERROR_VENDOR(androidx.biometric.BiometricPrompt.ERROR_VENDOR, "Vendor-specific error."),
    ERROR_LOCKOUT_PERMANENT(
        androidx.biometric.BiometricPrompt.ERROR_LOCKOUT_PERMANENT,
        "Too many failed attempts. Biometric disabled permanently.",
    ),
    ERROR_USER_CANCELED(
        androidx.biometric.BiometricPrompt.ERROR_USER_CANCELED,
        "Authentication canceled by user.",
    ),
    ERROR_NO_BIOMETRICS(
        androidx.biometric.BiometricPrompt.ERROR_NO_BIOMETRICS,
        "No biometric enrolled.",
    ),
    ERROR_HW_NOT_PRESENT(
        androidx.biometric.BiometricPrompt.ERROR_HW_NOT_PRESENT,
        "No biometric hardware present on this device.",
    ),
    ERROR_NEGATIVE_BUTTON(
        androidx.biometric.BiometricPrompt.ERROR_NEGATIVE_BUTTON,
        "User clicked the negative button.",
    ),
    ERROR_NO_DEVICE_CREDENTIAL(
        androidx.biometric.BiometricPrompt.ERROR_NO_DEVICE_CREDENTIAL,
        "No device credential set. Please set up a PIN, pattern, or password.",
    ),
    ERROR_SECURITY_UPDATE_REQUIRED(
        androidx.biometric.BiometricPrompt.ERROR_SECURITY_UPDATE_REQUIRED,
        "Security update required for hardware.",
    ),

    AUTHENTICATION_FAILED(400, "Authentication failed. Please try again."),
    TOO_MANY_ATTEMPTS(401, "Too many failed attempts. Use another method."),
    NO_SECURE_LOCK_SCREEN(500, "Device does not have a secure lock screen."),

    UNKNOWN(501, "Unknown Error"),
    ;

    companion object {
        fun fromErrorCode(errorCode: Int, defaultMessage: CharSequence): BiometricStatus {
            return values().firstOrNull { it.code == errorCode }
                ?: AUTHENTICATION_FAILED
        }
    }
}
