package jp.co.dhbk.connectbiz.data.network.respositories.impls

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import jp.co.dhbk.connectbiz.domain.model.AppMaintenanceAndUpdateInfoData
import jp.co.dhbk.connectbiz.domain.repository.AppMaintenanceAndUpdateInfoRepository
import kotlinx.coroutines.tasks.await

class AppMaintenanceAndUpdateInfoRepositoryImpl(
    private val firebaseRemoteConfig: FirebaseRemoteConfig,
    private val currentVersionName: ApiConfig,
) : AppMaintenanceAndUpdateInfoRepository {

    override suspend fun getMaintenanceAndUpdateData(): Result<AppMaintenanceAndUpdateInfoData> {
        return try {
            val isMaintenanceMode = firebaseRemoteConfig.getBoolean("maintenance_required")
            val maintenanceMessage = firebaseRemoteConfig.getString("maintenance_message")

            // Fetch additional fields
            val requiredVersionAndroid =
                firebaseRemoteConfig.getString("required_version_android")
            val requiredVersionMessage =
                firebaseRemoteConfig.getString("required_version_message")
            val destinationLink =
                firebaseRemoteConfig.getString("required_version_destination_link")

            val signInWithMFA = firebaseRemoteConfig.getString("sign_in_url_with_mfa")

            val signInWithoutMFA = firebaseRemoteConfig.getString("sign_in_url_without_mfa")

            val apiBaseURL = firebaseRemoteConfig.getString("api_base_url")

            val isForceUpdateRequired = shouldForceUpdate(
                requiredVersionAndroid.toIntOrNull() ?: 0,
                currentVersionName.versionCode,
            )
            // Create the data object with the new fields
            val appMaintenanceAndUpdateInfo = AppMaintenanceAndUpdateInfoData(
                message = maintenanceMessage,
                isMaintenance = isMaintenanceMode,
                requiredVersion = requiredVersionAndroid,
                updateMessage = requiredVersionMessage,
                destinationLink = destinationLink,
                isForceUpdateRequired = isForceUpdateRequired,
                signInWithMfa = signInWithMFA,
                signInWithoutMfa = signInWithoutMFA,
                apiBaseURL = apiBaseURL,
            )

            Result.success(appMaintenanceAndUpdateInfo)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun shouldForceUpdate(requiredVersion: Int, currentVersion: Int): Boolean {
        return requiredVersion > currentVersion
    }

    override suspend fun refreshRemoteConfig(): Result<Unit> {
        return try {
            firebaseRemoteConfig.fetchAndActivate().await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
