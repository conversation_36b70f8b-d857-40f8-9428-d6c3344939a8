package jp.co.dhbk.connectbiz.data.network

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import jp.co.dhbk.connectbiz.data.network.apis.ApiService
import jp.co.dhbk.connectbiz.data.network.respositories.impls.AppMaintenanceAndUpdateInfoRepositoryImpl
import jp.co.dhbk.connectbiz.data.network.respositories.impls.DeviceTokenRepositoryImpl
import jp.co.dhbk.connectbiz.data.network.respositories.impls.FileDownloadRepositoryImpl
import jp.co.dhbk.connectbiz.domain.repository.AppMaintenanceAndUpdateInfoRepository
import jp.co.dhbk.connectbiz.domain.repository.DeviceTokenRepository
import jp.co.dhbk.connectbiz.domain.repository.FileDownloadRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
internal object ApisModule {
    @Provides
    @Singleton
    fun provideFirebaseRemoteConfig(): FirebaseRemoteConfig {
        return FirebaseRemoteConfig.getInstance()
    }

    @Provides
    @Singleton
    fun provideMaintenanceRepository(
        firebaseRemoteConfig: FirebaseRemoteConfig,
        currentVersionName: ApiConfig,
    ): AppMaintenanceAndUpdateInfoRepository {
        return AppMaintenanceAndUpdateInfoRepositoryImpl(firebaseRemoteConfig, currentVersionName)
    }

    @Provides
    @Singleton
    fun provideDeviceTokenRepository(apiService: ApiService): DeviceTokenRepository {
        return DeviceTokenRepositoryImpl(apiService)
    }

    @Provides
    @Singleton
    fun provideFileDownloadRepository(apiService: ApiService): FileDownloadRepository {
        return FileDownloadRepositoryImpl(apiService)
    }
}
