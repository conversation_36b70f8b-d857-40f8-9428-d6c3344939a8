plugins {
    alias(libs.plugins.buildlogic.android.library)
    alias(libs.plugins.buildlogic.android.hilt)
    alias(libs.plugins.kotlin.plugin.serialization)
    alias(libs.plugins.buildlogic.retrofit)
}

android.namespace = "jp.co.dhbk.connectbiz.data"

dependencies {
    implementation(libs.crypto)
    implementation(libs.identity.jvm)
    implementation(libs.paging.common)
    implementation(project(":core:common"))
    implementation(project(":core:data"))
    implementation(project(":domain:repository"))
    implementation(project(":domain:model"))
    implementation(libs.firebase.config)
    implementation(libs.androidx.biometric)
    implementation(libs.androidx.appcompat)
}
