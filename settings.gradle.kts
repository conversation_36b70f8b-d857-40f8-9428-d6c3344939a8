pluginManagement {
    includeBuild("build-logic")
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven("https://jitpack.io")
    }
}

rootProject.name = "Connectbiz"
include(":app")
include(":lint:designsystem")
include(":core:analytics")
include(":core:common")
include(":core:data")
include(":core:ui")
include(":core:testing")
include(":data")
include(":domain:model")
include(":domain:repository")
include(":domain:usecase")
include(":ui")
include(":core:analytics")
