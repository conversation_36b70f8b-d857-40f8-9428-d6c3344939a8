package jp.co.dhbk.connectbiz.core.data.utils

import android.content.Context
import androidx.datastore.core.CorruptionException
import androidx.datastore.core.DataStore
import androidx.datastore.core.Serializer
import com.google.crypto.tink.Aead
import com.google.crypto.tink.KeyTemplates
import com.google.crypto.tink.aead.AeadConfig
import com.google.crypto.tink.integration.android.AndroidKeysetManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.StringFormat
import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer
import java.io.InputStream
import java.io.OutputStream

/**
 * [DataStore] のための [Serializer]<[T]> を生成する
 *
 * @param T [Serializable] な型
 * @param defaultValue デフォルトの値
 * @param format JSON のフォーマット
 * @return [Serializer]<[T]>
 */
inline fun <reified T> buildDataStoreSerializer(
    defaultValue: T,
    format: StringFormat =
        Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
        },
): Serializer<T> = object : Serializer<T> {
    override val defaultValue: T = defaultValue

    override suspend fun readFrom(input: InputStream): T = withContext(Dispatchers.IO) {
        try {
            val bytes = input.readBytes()
            format.decodeFromString(
                deserializer = serializer<T>(),
                string = bytes.decodeToString(),
            )
        } catch (e: SerializationException) {
            throw CorruptionException("Cannot read stored data", e)
        }
    }

    override suspend fun writeTo(t: T, output: OutputStream) = withContext(Dispatchers.IO) {
        val bytes =
            format.encodeToString(serializer = serializer<T>(), value = t).encodeToByteArray()
        output.write(bytes)
    }
}

/**
 * [DataStore] のための暗号化する [Serializer]<[T]> を生成する
 *
 * @param T [Serializable] な型
 * @param defaultValue デフォルトの値
 * @param context [Context]
 * @param format JSON のフォーマット
 * @return [Serializer]<[T]>
 */
inline fun <reified T> buildDataStoreEncryptSerializer(
    defaultValue: T,
    context: Context,
    format: StringFormat =
        Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
        },
): Serializer<T> = object : Serializer<T> {
    override val defaultValue: T = defaultValue

    private val encryption: Aead by lazy {
        AeadConfig.register()
        AndroidKeysetManager.Builder()
            .withSharedPref(
                context,
                "master_keyset",
                "master_key_preference",
            )
            .withKeyTemplate(KeyTemplates.get("AES256_GCM"))
            .withMasterKeyUri("android-keystore://master_key")
            .build()
            .keysetHandle
            .getPrimitive(Aead::class.java)
    }

    override suspend fun readFrom(input: InputStream): T = withContext(Dispatchers.IO) {
        try {
            val bytes = input.readBytes()
            val decryptedBytes = encryption.decrypt(bytes, null)
            format.decodeFromString(
                deserializer = serializer<T>(),
                string = decryptedBytes.decodeToString(),
            )
        } catch (e: SerializationException) {
            throw CorruptionException("Cannot read stored data", e)
        }
    }

    override suspend fun writeTo(t: T, output: OutputStream) = withContext(Dispatchers.IO) {
        val bytes =
            format.encodeToString(serializer = serializer<T>(), value = t).encodeToByteArray()
        val encryptedBytes = encryption.encrypt(bytes, null)
        output.write(encryptedBytes)
    }
}
