package jp.co.dhbk.connectbiz.core.testing

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.test.isDialog
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onRoot
import com.github.takahirom.roborazzi.AndroidComposePreviewTester
import com.github.takahirom.roborazzi.ComposePreviewTester
import com.github.takahirom.roborazzi.DEFAULT_ROBORAZZI_OUTPUT_DIR_PATH
import com.github.takahirom.roborazzi.ExperimentalRoborazziApi
import com.github.takahirom.roborazzi.captureRoboImage
import sergio.sastre.composable.preview.scanner.android.AndroidPreviewInfo
import sergio.sastre.composable.preview.scanner.core.preview.ComposablePreview

@OptIn(ExperimentalRoborazziApi::class)
class PreviewTester() : ComposePreviewTester<AndroidPreviewInfo> by AndroidComposePreviewTester() {
    private val composeTestRule = createComposeRule()

    override fun options(): ComposePreviewTester.Options = super.options().copy(
        testLifecycleOptions =
        ComposePreviewTester.Options.JUnit4TestLifecycleOptions {
            composeTestRule
        },
    )

    override fun test(preview: ComposablePreview<AndroidPreviewInfo>) {
        val componentName = "${preview.methodName}_${preview.previewIndex ?: 0}"
        val filePath = "$DEFAULT_ROBORAZZI_OUTPUT_DIR_PATH/$componentName.png"
        composeTestRule.setContent {
            CompositionLocalProvider(LocalInspectionMode provides true) {
                Box(
                    modifier =
                    Modifier
                        .then(
                            if (preview.previewInfo.showBackground) {
                                Modifier.background(Color(preview.previewInfo.backgroundColor))
                            } else {
                                Modifier
                            },
                        ),
                ) {
                    preview()
                }
            }
        }
        when (preview.previewInfo.name) {
            "Dialog", "ModalBottomSheet" -> {
                composeTestRule.onNode(isDialog()).captureRoboImage(filePath)
            }
            else -> {
                composeTestRule.onRoot().captureRoboImage(filePath)
            }
        }
    }
}
