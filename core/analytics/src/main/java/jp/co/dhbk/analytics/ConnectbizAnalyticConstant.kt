package jp.co.dhbk.analytics

import com.google.firebase.analytics.FirebaseAnalytics

object ConnectbizAnalyticConstant {
    object Key {
        const val URI = "uri"
    }

    const val SCREEN_VIEW = FirebaseAnalytics.Param.SCREEN_NAME
    const val IMPRESSION = "impression"
    const val NAVIGATE = "navigate"
    const val EXPENDED = "expended"
    const val COLLAPSED = "collapsed"

    object Page {
        const val MAINTENANCE = "maintenance"
        const val SPLASH = "splash"
        const val TUTORIAL = "tutorial"
        const val LOGIN = "login"
        const val HOME = "home"
        const val FORGOT_PASSWORD = "forgot_password"
        const val FORCE_UPDATE = "force_update"
        const val OTPVERIFY = "otpverify"
        const val LICENSES = "licenses"
        const val SETTINGS = "setting"
        const val PDF_VIEWER = "pdf_viewer"
        const val AUTHENTICATION_SETTINGS = "authentication_settings"
        const val AUTHENTICATION_CANCEL = "authentication_cancel"
        const val ERROR = "error_screen"
    }
}
