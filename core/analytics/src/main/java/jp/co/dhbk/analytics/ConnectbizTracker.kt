package jp.co.dhbk.analytics

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizSession
import timber.log.Timber
import javax.inject.Inject

class ConnectbizTracker
@Inject
constructor(
    private val iConnectbizAnalyticProvider: IConnectbizAnalyticProvider,
    val piSession: ConnectbizSession,
) {
    fun trackUserProperty(properties: Map<String, Any>) {
        for ((key, value) in properties) {
            iConnectbizAnalyticProvider.analytics().setUserProperty(key, value.toString())
        }
    }

    fun logEvent(eventName: String, screenName: String? = null, params: Map<String, Any>? = null) {
        Timber.d("Log event is fired $eventName")
        val bundle = Bundle()
        params?.let {
            for ((key, value) in it) {
                bundle.putString(key, value.toString())
            }
        }
        screenName?.let {
            bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, it)
        }

        iConnectbizAnalyticProvider.analytics().logEvent(eventName, bundle)
    }
}
