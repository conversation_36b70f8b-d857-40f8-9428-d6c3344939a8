package jp.co.dhbk.analytics

import android.content.Context
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ConnectbizAnalyticProvider
@Inject
constructor(
    private val context: Context,
) : IConnectbizAnalyticProvider {
    private val crashlyticsInstance: FirebaseCrashlytics by lazy {
        FirebaseCrashlytics.getInstance()
    }

    private val analyticsInstance: FirebaseAnalytics by lazy {
        FirebaseAnalytics.getInstance(context)
    }

    override fun crashlytics(): FirebaseCrashlytics {
        return crashlyticsInstance
    }

    override fun analytics(): FirebaseAnalytics {
        return analyticsInstance
    }
}
