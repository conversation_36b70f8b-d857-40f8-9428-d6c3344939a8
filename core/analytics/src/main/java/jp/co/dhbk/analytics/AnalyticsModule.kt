package jp.co.dhbk.analytics

import android.app.Application
import android.content.Context
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AnalyticsModule {
    @Binds
    @Singleton
    abstract fun bindAnalyticsProvider(
        impl: ConnectbizAnalyticProvider,
    ): IConnectbizAnalyticProvider

    companion object {
        @Provides
        @Singleton
        fun provideContext(application: Application): Context {
            return application.applicationContext
        }
    }
}
