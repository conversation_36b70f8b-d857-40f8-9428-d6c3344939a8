package jp.co.dhbk.analytics

import timber.log.Timber
import javax.inject.Inject

class ConnectbizLogTree
@Inject
constructor(private val analyticProvider: IConnectbizAnalyticProvider) : Timber.Tree() {
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // super.log(priority, tag, message, t)
    }

    override fun e(message: String?, vararg args: Any?) {
        message?.let {
            analyticProvider.crashlytics().recordException(Throwable(it))
        }
    }

    override fun e(t: Throwable?) {
        t?.let {
            analyticProvider.crashlytics().recordException(t)
        }
    }
}
