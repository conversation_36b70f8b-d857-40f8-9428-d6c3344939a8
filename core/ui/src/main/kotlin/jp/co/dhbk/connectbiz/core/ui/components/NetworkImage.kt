package jp.co.dhbk.connectbiz.core.ui.components

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import java.net.URL

/**
 * ネットワーク上の画像を表示
 *
 * @param url 画像の [URL]
 * @param contentDescription 画像の `contentDescription`
 * @param placeholder 読み込み中の表示
 * @param modifier [Modifier]
 * @param error エラー時の表示
 * @param alignment 画像の配置位置
 * @param contentScale 画像のスケーリング方法
 * @param alpha 不透明度
 * @param colorFilter [ColorFilter]
 */
@SuppressLint("DesignSystem")
@Composable
fun NetworkImage(
    url: String,
    contentDescription: String?,
    placeholder: Painter?,
    modifier: Modifier = Modifier,
    error: Painter? = placeholder,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
) {
    NetworkImage(
        url = url.toString(),
        contentDescription = contentDescription,
        modifier = modifier,
        placeholder = placeholder,
        error = error,
        alignment = alignment,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = colorFilter,
    )
}
