package jp.co.dhbk.connectbiz.core.ui.extensions.savedstatehandle

import androidx.lifecycle.SavedStateHandle
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * [MutableStateFlow.value] が更新されるたびに、[key] に関連づけられた
 * [SavedStateHandle] の値を更新する
 *
 * 値が保持されている時はその値、保持されてない時は [initialValue] により
 * [MutableStateFlow]<[T]> を生成する
 *
 * @param T 対象の型
 * @param key キー
 * @param initialValue 初期値
 * @return [MutableStateFlow]<[T]>
 */
fun <T> SavedStateHandle.getMutableStateFlow(
    key: String,
    initialValue: T,
): MutableSavedStateFlowImpl<T> = MutableSavedStateFlowImpl(
    initialValue = initialValue,
    savedStateHandle = this,
    key = key,
)

class MutableSavedStateFlowImpl<T>(
    initialValue: T,
    private val savedStateHandle: SavedStateHandle,
    private val key: String,
) {
    private val delegate: MutableStateFlow<T> =
        MutableStateFlow(savedStateHandle.get<T>(key) ?: initialValue)

    init {
        savedStateHandle.set<T>(key, delegate.value)
    }

    var value: T
        get() = delegate.value
        set(value) {
            delegate.value = value
            savedStateHandle.set<T>(key, value)
        }

    suspend fun emit(value: T) {
        delegate.emit(value)
        savedStateHandle.set<T>(key, value)
    }

    fun tryEmit(value: T): Boolean {
        val ret = delegate.tryEmit(value)
        if (ret) savedStateHandle.set<T>(key, value)
        return ret
    }

    fun compareAndSet(expect: T, update: T): Boolean {
        val ret = delegate.compareAndSet(expect, update)
        if (ret) savedStateHandle.set<T>(key, update)
        return ret
    }

    // Optionally, expose other useful methods from MutableStateFlow
    val subscriptionCount: StateFlow<Int>
        get() = delegate.subscriptionCount

    // Implement other methods as needed.

    // Update function to modify the current value based on a transformation
    fun update(transform: (T) -> T) {
        value = transform(value)
    }
}
