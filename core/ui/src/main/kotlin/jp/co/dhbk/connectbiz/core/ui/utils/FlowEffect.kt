package jp.co.dhbk.connectbiz.core.ui.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch

/**
 * [flow] を [Flow.collect] したときに、非同期で [function] を実行する
 */
@Composable
fun <T> FlowEffect(flow: Flow<T>, function: suspend CoroutineScope.(T) -> Unit) {
    val latestFunction by rememberUpdatedState(function)
    LaunchedEffect(flow) {
        flow.collect {
            launch { latestFunction(it) }
        }
    }
}
