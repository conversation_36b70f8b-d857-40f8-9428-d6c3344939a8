package jp.co.dhbk.connectbiz.core.ui.extensions.navcontroller

import androidx.lifecycle.Lifecycle
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.toRoute
import jp.co.dhbk.connectbiz.core.common.utils.runCatchingOrThrow
import kotlinx.serialization.Serializable

/**
 * [Result]<[Unit]> を返す安全呼び出しの `navigate`
 *
 * - [builder] の前に `launchSingleTop = true` している
 * - [Lifecycle.State.RESUMED] に到達していないとき遷移できない
 * - 優先度の高い `destination` から遷移できない
 *
 * @param T [route] の型
 * @param route 遷移先の `route`
 * @param ignoreLifecycle ライフサイクルを無視するなら `true`
 * @param ignorePriority 優先度を無視するなら `true`
 * @param builder [NavOptions] のための DSL
 * @return [Result]<[Unit]>
 * - [Unit] - 成功時
 * - [IllegalArgumentException] - [route] が無効なとき
 * - [IllegalArgumentException] - `setGraph()` する前のとき
 * - [IllegalStateException] - [NavController.currentBackStackEntry] が空のとき
 * - [IllegalStateException] - [Lifecycle.State.RESUMED] に到達していないとき
 * - [IllegalStateException] - 優先度の高い `destination` から遷移しようとしたとき
 */
fun <T : Any> NavController.navigateSafely(
    route: T,
    ignoreLifecycle: Boolean = false,
    ignorePriority: Boolean = false,
    builder: NavOptionsBuilder.() -> Unit = {},
): Result<Unit> = runCatchingOrThrow {
    val currentEntry =
        checkNotNull(currentBackStackEntry) {
            "Back stack is empty."
        }
    check(
        ignoreLifecycle || currentEntry.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED),
    ) {
        "In order to navigate, lifecycle state needs to be greater or equal to RESUMED."
    }
    check(ignorePriority || currentEntry.isHighPriority.not()) {
        "Cannot navigate from the ${currentEntry.destination.route}."
    }
    navigate(route) {
        launchSingleTop = true
        builder()
    }
}

/**
 * [Result]<[Boolean]> を返す安全呼び出しの `popBackStack`
 *
 * - [Lifecycle.State.RESUMED] に到達していないとき遷移できない
 * - 優先度の高い `destination` から遷移できない
 *
 * @param T [route] の型
 * @param route 保持する最上位の `route`、デフォルトでは現在の `destination`
 * @param inclusive [route] を pop するなら `true`
 * @param saveState 復元可能にするなら `true`
 * @param ignoreLifecycle ライフサイクルを無視するなら `true`
 * @param ignorePriority 優先度を無視するなら `true`
 * @return [Result]<[Boolean]>
 * - `true` - 1 回以上 pop され、遷移したとき
 * - `false` - pop されず、遷移できてないとき
 * - [IllegalArgumentException] - [route] が無効なとき
 * - [IllegalArgumentException] - `setGraph()` する前のとき
 * - [IllegalStateException] - [NavController.currentBackStackEntry] が空のとき
 * - [IllegalStateException] - [Lifecycle.State.RESUMED] に到達していないとき
 * - [IllegalStateException] - 優先度の高い `destination` から遷移しようとしたとき
 */
fun <T : Any> NavController.popBackStackSafely(
    route: T? = null,
    inclusive: Boolean = true,
    saveState: Boolean = false,
    ignoreLifecycle: Boolean = false,
    ignorePriority: Boolean = false,
): Result<Boolean> = runCatchingOrThrow {
    val currentEntry =
        checkNotNull(currentBackStackEntry) {
            "Back stack is empty."
        }
    check(
        ignoreLifecycle || currentEntry.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED),
    ) {
        "In order to pop back, lifecycle state needs to be greater or equal to RESUMED."
    }
    check(ignorePriority || currentEntry.isHighPriority.not()) {
        "Cannot pop back from the ${currentEntry.destination.route}."
    }
    if (route != null) {
        popBackStack(route, inclusive, saveState)
    } else {
        popBackStack(currentEntry.destination.id, inclusive, saveState)
    }
}

/**
 * [Result]<[Boolean]> を返す安全呼び出しの `navigateUp`
 *
 * - [Lifecycle.State.RESUMED] に到達していないとき遷移できない
 * - 優先度の高い `destination` から遷移できない
 *
 * @param ignoreLifecycle ライフサイクルを無視するなら `true`
 * @param ignorePriority 優先度を無視するなら `true`
 * @return [Result]<[Boolean]>
 * - `true` - 遷移したとき
 * - `false` - 遷移できてないとき
 * - [IllegalArgumentException] - `setGraph()` する前のとき
 * - [IllegalStateException] - [NavController.currentBackStackEntry] が空のとき
 * - [IllegalStateException] - [Lifecycle.State.RESUMED] に到達していないとき
 * - [IllegalStateException] - 優先度の高い `destination` から遷移しようとしたとき
 */
fun NavController.navigateUpSafely(
    ignoreLifecycle: Boolean = false,
    ignorePriority: Boolean = false,
): Result<Boolean> = runCatchingOrThrow {
    val currentEntry =
        checkNotNull(currentBackStackEntry) {
            "Back stack is empty."
        }
    check(
        ignoreLifecycle || currentEntry.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED),
    ) {
        "In order to navigate up, lifecycle state needs to be greater or equal to RESUMED."
    }
    check(ignorePriority || currentEntry.isHighPriority.not()) {
        "Cannot navigate up from the ${currentEntry.destination.route}."
    }
    navigateUp()
}

/**
 * 優先度を指定するための `route`
 *
 * Usage:
 * ```kotlin
 * data class MaintenanceRoute(override val isHighPriority: Boolean = true) : PrioritizedRoute
 * ```
 */
interface PrioritizedRoute {
    /**
     * 優先度が高いなら `true`
     */
    val isHighPriority: Boolean
}

@Serializable
private data class SerializablePrioritizedRoute(
    override val isHighPriority: Boolean,
) : PrioritizedRoute

private val NavBackStackEntry.isHighPriority: Boolean
    get() =
        runCatchingOrThrow(loggingEnabled = false) {
            toRoute<SerializablePrioritizedRoute>().isHighPriority
        }.getOrDefault(false)
