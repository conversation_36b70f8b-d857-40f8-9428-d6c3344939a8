package jp.co.dhbk.connectbiz.core.ui.utils

import android.annotation.SuppressLint
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.SnackbarVisuals
import androidx.compose.runtime.Stable
import androidx.compose.ui.graphics.Color

/**
 * ノーマルスナックバーを表示する
 *
 * @param message メッセージ
 * @return [SnackbarResult]
 */
suspend fun SnackbarHostState.showNormalSnackbar(message: String): SnackbarResult =
    showCustomSnackbar(type = SnackbarType.Normal, message = message)

/**
 * エラースナックバーを表示する
 *
 * @param message メッセージ
 * @return [SnackbarResult]
 */
suspend fun SnackbarHostState.showErrorSnackbar(message: String): SnackbarResult =
    showCustomSnackbar(type = SnackbarType.Error, message = message)

/**
 * [CustomSnackbarVisuals] によるスナックバーを表示する
 *
 * @param type スナックバーのタイプ
 * @param message メッセージ
 * @return [SnackbarResult]
 */
@SuppressLint("DesignSystem")
private suspend fun SnackbarHostState.showCustomSnackbar(
    type: SnackbarType,
    message: String,
): SnackbarResult = showSnackbar(
    visuals =
    CustomSnackbarVisuals(
        type = type,
        message = message,
    ),
)

/**
 * [SnackbarVisuals] を拡張したクラス
 *
 * @property type スナックバーのタイプ
 * @property message メッセージ
 */
@Stable
private data class CustomSnackbarVisuals(
    val type: SnackbarType,
    override val message: String,
    val textColor: Color = when (type) {
        SnackbarType.Normal -> Color(0xFFFFFFFF)
        SnackbarType.Error -> Color(0xFFFFFFFF)
        SnackbarType.Info -> Color(0xFFFFFFFF)
    },
    val backgroundColor: Color = when (type) {
        SnackbarType.Normal -> Color(0xFF5A67D8)
        SnackbarType.Error -> Color(0xFF5A67D8)
        SnackbarType.Info -> Color(0xFF5A67D8)
    },
) : SnackbarVisuals {
    override val actionLabel: String? = null
    override val withDismissAction: Boolean = false
    override val duration: SnackbarDuration = SnackbarDuration.Short
}

/**
 * スナックバーのタイプ
 *
 * [CustomSnackbarVisuals] 以外なら `null`
 */
val SnackbarVisuals.typeOrNull: SnackbarType?
    get() = if (this is CustomSnackbarVisuals) type else null

/**
 * スナックバーのタイプ
 */
enum class SnackbarType { Normal, Error, Info }
