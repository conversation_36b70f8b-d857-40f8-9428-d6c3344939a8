package jp.co.dhbk.connectbiz.core.ui.utils

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density

/**
 * フォントスケールを制限する
 *
 * Android 13 以下で設定できる 0.85 から 1.30 倍の範囲をデフォルトとしている
 *
 * @param min フォントスケールの下限（デフォルトは 0.85）
 * @param max フォントスケールの上限（デフォルトは 1.30）
 * @param content コンテント
 */
@Composable
fun ConstrainFontScale(min: Float = 0.85f, max: Float = 1.30f, content: @Composable () -> Unit) {
    CompositionLocalProvider(
        LocalDensity provides
            object : Density {
                override val density = LocalDensity.current.density
                override val fontScale = LocalDensity.current.fontScale.coerceIn(min, max)
            },
        content = content,
    )
}

@Preview(fontScale = 0.85f)
@Preview(fontScale = 1.00f)
@Preview(fontScale = 1.15f)
@Preview(fontScale = 1.30f)
@Preview(fontScale = 1.50f)
@Preview(fontScale = 1.80f)
@Preview(fontScale = 2.00f)
@Composable
private fun ConstrainFontScalePreview() {
    Column {
        Text("制限なし")
        ConstrainFontScale {
            Text("制限あり（0.85 〜 1.30）")
        }
    }
}
