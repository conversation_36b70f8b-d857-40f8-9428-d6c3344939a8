package jp.co.dhbk.connectbiz.core.ui.utils

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density

/**
 * フォントスケールを無視する
 *
 * `1.dp` と `1.sp` が同じ長さになる
 *
 * @param content コンテント
 */
@Composable
fun IgnoreFontScale(content: @Composable () -> Unit) {
    CompositionLocalProvider(
        LocalDensity provides
            object : Density {
                override val density = LocalDensity.current.density
                override val fontScale = 1f
            },
        content = content,
    )
}

@Preview(fontScale = 0.85f)
@Preview(fontScale = 1.00f)
@Preview(fontScale = 1.15f)
@Preview(fontScale = 1.30f)
@Preview(fontScale = 1.50f)
@Preview(fontScale = 1.80f)
@Preview(fontScale = 2.00f)
@Composable
private fun IgnoreFontScalePreview() {
    Column {
        Text("無視しない")
        IgnoreFontScale {
            Text("無視する")
        }
    }
}
