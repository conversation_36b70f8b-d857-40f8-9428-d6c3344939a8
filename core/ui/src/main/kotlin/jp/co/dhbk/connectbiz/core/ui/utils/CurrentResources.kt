package jp.co.dhbk.connectbiz.core.ui.utils

import android.content.res.Configuration
import android.content.res.Resources
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext

/**
 * [Configuration] が更新されると再構成される [Resources] を返す [Composable] 関数
 *
 * @return [Resources]
 */
@Composable
@ReadOnlyComposable
fun currentResources(): Resources {
    LocalConfiguration.current
    return LocalContext.current.resources
}
