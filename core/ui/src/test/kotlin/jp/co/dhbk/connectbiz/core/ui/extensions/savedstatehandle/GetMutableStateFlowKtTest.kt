package jp.co.dhbk.connectbiz.core.ui.extensions.savedstatehandle

import androidx.lifecycle.SavedStateHandle
import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.test.runTest
import org.junit.Test

class GetMutableStateFlowKtTest {
    @Test
    fun `値が保存されていないときは初期値が initialValue になる`() {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 0)

        assertThat(flow.value).isEqualTo(0)
        assertThat(handle.get<Int>("key")).isEqualTo(0)
    }

    @Test
    fun `値が保存されていないときは初期値がその値になる`() {
        val handle = SavedStateHandle()
        handle["key"] = 1
        val flow = handle.getMutableStateFlow("key", 0)

        assertThat(flow.value).isEqualTo(1)
        assertThat(handle.get<Int>("key")).isEqualTo(1)
    }

    @Test
    fun `value に set で値が更新される`() {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 1)

        flow.value = 1
        assertThat(flow.value).isEqualTo(1)
        assertThat(handle.get<Int>("key")).isEqualTo(1)
    }

    @Test
    fun `emit で値が更新される`() = runTest {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 0)

        flow.emit(1)
        assertThat(flow.value).isEqualTo(1)
        assertThat(handle.get<Int>("key")).isEqualTo(1)
    }

    @Test
    fun `tryEmit で値が更新される`() {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 0)

        flow.tryEmit(1)
        assertThat(flow.value).isEqualTo(1)
        assertThat(handle.get<Int>("key")).isEqualTo(1)
    }

    @Test
    fun `compareAndSet で成功時に値が更新される`() {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 0)

        flow.compareAndSet(flow.value, 1)
        assertThat(flow.value).isEqualTo(1)
        assertThat(handle.get<Int>("key")).isEqualTo(1)
    }

    @Test
    fun `compareAndSet で失敗時は更新されない`() {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 0)

        flow.compareAndSet(99, 1)
        assertThat(flow.value).isEqualTo(0)
        assertThat(handle.get<Int>("key")).isEqualTo(0)
    }

    @Test
    fun `update で値が更新される`() {
        val handle = SavedStateHandle()
        val flow = handle.getMutableStateFlow("key", 0)

        flow.update { 1 }
        assertThat(flow.value).isEqualTo(1)
        assertThat(handle.get<Int>("key")).isEqualTo(1)
    }
}
