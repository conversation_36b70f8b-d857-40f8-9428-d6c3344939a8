plugins {
    alias(libs.plugins.buildlogic.android.library)
    alias(libs.plugins.kotlin.plugin.serialization)
    alias(libs.plugins.buildlogic.android.compose)
    alias(libs.plugins.buildlogic.roborazzi)
}

android {
    namespace = "jp.co.dhbk.connectbiz.core.ui"
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            all {
                it.systemProperties["robolectric.pixelCopyRenderMode"] = "hardware"
            }
        }
    }
}

roborazzi {
    @OptIn(com.github.takahirom.roborazzi.ExperimentalRoborazziApi::class)
    generateComposePreviewRobolectricTests {
        enable = true
        packages = listOf("jp.co.dhbk.connectbiz.core.ui")
        includePrivatePreviews = true
        testerQualifiedClassName = "jp.co.dhbk.connectbiz.core.testing.PreviewTester"
        robolectricConfig =
            mapOf(
                "sdk" to "[33]",
                "qualifiers" to "RobolectricDeviceQualifiers.Pixel7",
            )
    }
}

dependencies {
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.coil.compose)
    implementation(libs.androidx.lifecycle.viewmodelSavedstate)
    implementation(project(":core:common"))
    testImplementation(project(":core:testing"))
}
