package jp.co.dhbk.connectbiz.core.common.extensions.string

import jp.co.dhbk.connectbiz.core.common.model.FileExtensions
import jp.co.dhbk.connectbiz.core.common.model.MimeTypes
import org.junit.Assert.assertEquals

import org.junit.Test

class ConvertFileExtensionAndMimeTypeKtTest {

    @Test
    fun `convertFileExtensionToMimeType JPG to IMAGE`() {
        val result = "file.jpg".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.IMAGE, result)
    }

    @Test
    fun `convertFileExtensionToMimeType JPEG to IMAGE`() {
        val result = "file.jpeg".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.IMAGE, result)
    }

    @Test
    fun `convertFileExtensionToMimeType PNG to IMAGE`() {
        val result = "file.png".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.IMAGE, result)
    }

    @Test
    fun `convertFileExtensionToMimeType GIF to IMAGE`() {
        val result = "file.gif".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.IMAGE, result)
    }

    @Test
    fun `convertFileExtensionToMimeType TXT to Text`() {
        val result = "file.txt".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.text.TXT, result)
    }

    @Test
    fun `convertFileExtensionToMimeType CSV to Text`() {
        val result = "file.csv".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.text.CSV, result)
    }

    @Test
    fun `convertFileExtensionToMimeType XLS to Excel`() {
        val result = "file.xls".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.application.vnd.MS_EXCEL, result)
    }

    @Test
    fun `convertFileExtensionToMimeType XLSX to Excel`() {
        val result = "file.xlsx".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.application.vnd.MS_EXCEL, result)
    }

    @Test
    fun `convertFileExtensionToMimeType DOC to Word`() {
        val result = "file.doc".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.application.vnd.MS_WORD, result)
    }

    @Test
    fun `convertFileExtensionToMimeType DOCX to Word`() {
        val result = "file.docx".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.application.vnd.MS_WORD, result)
    }

    @Test
    fun `convertFileExtensionToMimeType PPT to Powerpoint`() {
        val result = "file.ppt".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.application.vnd.MS_POWERPOINT, result)
    }

    @Test
    fun `convertFileExtensionToMimeType PPTX to Powerpoint`() {
        val result = "file.pptx".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.application.vnd.MS_POWERPOINT, result)
    }

    @Test
    fun `convertFileExtensionToMimeType Empty to WildCard`() {
        val result = "".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.ANY, result)
    }

    @Test
    fun `convertFileExtensionToMimeType Blank to WildCard`() {
        val result = "　　".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.ANY, result)
    }

    @Test
    fun `convertFileExtensionToMimeType Any Unmatched Characters to WildCard`() {
        val result = "file.cpp".convertFileExtensionToMimeType()
        assertEquals(MimeTypes.ANY, result)
    }

    @Test
    fun `convertMimeTypeToExtension IMAGE to JPG`() {
        val result = MimeTypes.IMAGE.convertMimeTypeToExtension()
        assertEquals(FileExtensions.image.JPG, result)
    }

    @Test
    fun `convertMimeTypeToExtension TXT to TXT`() {
        val result = MimeTypes.text.TXT.convertMimeTypeToExtension()
        assertEquals(FileExtensions.text.TXT, result)
    }

    @Test
    fun `convertMimeTypeToExtension CSV to CSV`() {
        val result = MimeTypes.text.CSV.convertMimeTypeToExtension()
        assertEquals(FileExtensions.text.CSV, result)
    }

    @Test
    fun `convertMimeTypeToExtension MS_EXCEL to XLS`() {
        val result = MimeTypes.application.vnd.MS_EXCEL.convertMimeTypeToExtension()
        assertEquals(FileExtensions.excel.XLS, result)
    }

    @Test
    fun `convertMimeTypeToExtension SPREADSHEET to XLSX`() {
        val result = MimeTypes.application.vnd.SPREADSHEET.convertMimeTypeToExtension()
        assertEquals(FileExtensions.excel.XLSX, result)
    }

    @Test
    fun `convertMimeTypeToExtension DOCUMENT to DOCX`() {
        val result = MimeTypes.application.vnd.DOCUMENT.convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.word.DOCX,
            result,
        )
    }

    @Test
    fun `convertMimeTypeToExtension MS_WORD to DOC`() {
        val result = MimeTypes.application.vnd.MS_WORD.convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.word.DOC,
            result,
        )
    }

    @Test
    fun `convertMimeTypeToExtension PRESENTATION to PPTX`() {
        val result = MimeTypes.application.vnd.PRESENTATION.convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.powerpoint.PPTX,
            result,
        )
    }

    @Test
    fun `convertMimeTypeToExtension MS_POWERPOINT to PPT`() {
        val result = MimeTypes.application.vnd.MS_POWERPOINT.convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.powerpoint.PPT,
            result,
        )
    }

    @Test
    fun `convertMimeTypeToExtension Empty to BINARY`() {
        val result = "".convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.BINARY,
            result,
        )
    }

    @Test
    fun `convertMimeTypeToExtension Blank to BINARY`() {
        val result = "　　".convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.BINARY,
            result,
        )
    }

    @Test
    fun `convertMimeTypeToExtension Any Unmatched Characters to BINARY`() {
        val result = "file.cpp".convertMimeTypeToExtension()
        assertEquals(
            FileExtensions.BINARY,
            result,
        )
    }
}
