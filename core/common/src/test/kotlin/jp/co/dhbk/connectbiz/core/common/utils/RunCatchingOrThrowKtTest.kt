package jp.co.dhbk.connectbiz.core.common.utils

import com.google.common.truth.Truth.assertThat
import org.junit.Test
import kotlin.coroutines.cancellation.CancellationException

class RunCatchingOrThrowKtTest {
    @Test
    fun `runCatchingOrThrow 123`() {
        val value = 123
        val block = { value }
        assertThat(runCatching { runCatchingOrThrow { block() } })
            .isEqualTo(Result.success(Result.success(value)))
    }

    @Test
    fun `runCatchingOrThrow Exception`() {
        val throwable = Exception()
        val block = { throw throwable }
        assertThat(runCatching { runCatchingOrThrow { block() } })
            .isEqualTo(Result.success(Result.failure<Nothing>(throwable)))
    }

    @Test
    fun `runCatchingOrThrow CancellationException`() {
        val throwable = CancellationException()
        val block = { throw throwable }
        assertThat(runCatching { runCatchingOrThrow { block() } })
            .isEqualTo(Result.failure<Result<Nothing>>(throwable))
    }

    @Test
    fun `runCatchingOrThrow Error`() {
        val throwable = Error()
        val block = { throw throwable }
        assertThat(runCatching { runCatchingOrThrow { block() } })
            .isEqualTo(Result.failure<Result<Nothing>>(throwable))
    }
}
