package jp.co.dhbk.connectbiz.core.common.extensions.string

import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test

class CheckForFileExtensionMatchKtTest {

    @Test
    fun `findMatchFileExtensionForDownload CSV is True`() {
        val result = "file.csv".findMatchFileExtensionForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload XLS is True`() {
        val result = "file.xls".findMatchFileExtensionForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload XLSX is True`() {
        val result = "file.xlsx".findMatchFileExtensionForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload XLSM is True`() {
        val result = "file.xlsm".findMatchFileExtensionForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload DOC is True`() {
        val result = "file.doc".findMatchFileExtensionForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload DOCX is True`() {
        val result = "file.docx".findMatchFileExtensionForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload Empty is False`() {
        val result = "".findMatchFileExtensionForDownload()
        assertFalse(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload Blank is False`() {
        val result = "　　".findMatchFileExtensionForDownload()
        assertFalse(result)
    }

    @Test
    fun `findMatchFileExtensionForDownload Any Unmatched Characters are False`() {
        val result =
            "file.cpp".findMatchFileExtensionForDownload()
        assertFalse(result)
    }

    @Test
    fun `findMatchFileExtensionForPdf PDF is True`() {
        val result = "file.pdf".findMatchFileExtensionForPdf()
        assertTrue(result)
    }

    @Test
    fun `findMatchFileExtensionForPdf Empty is False`() {
        val result = "".findMatchFileExtensionForPdf()
        assertFalse(result)
    }

    @Test
    fun `findMatchFileExtensionForPdf Blank is False`() {
        val result = "　　".findMatchFileExtensionForPdf()
        assertFalse(result)
    }

    @Test
    fun `findMatchFileExtensionForPdf Any Unmatched Characters are False`() {
        val result =
            "file.cpp".findMatchFileExtensionForDownload()
        assertFalse(result)
    }
}
