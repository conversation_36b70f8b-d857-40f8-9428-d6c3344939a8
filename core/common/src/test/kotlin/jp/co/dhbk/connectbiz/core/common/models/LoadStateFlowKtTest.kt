package jp.co.dhbk.connectbiz.core.common.models

import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadStateFlow
import jp.co.dhbk.connectbiz.core.common.loadstate.asStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Test

class LoadStateFlowKtTest {
    @Test
    fun `LoadStateFlow Initial→Success`() = runTest {
        val target = LoadStateFlow<Unit>()
        target.test {
            assertThat(awaitItem()).isEqualTo(LoadState.Initial)
            target.load { Result.success(Unit) }
            assertThat(awaitItem()).isEqualTo(LoadState.Loading<Unit>())
            assertThat(awaitItem()).isEqualTo(LoadState.Success(Unit))
        }
    }

    @Test
    fun `LoadStateFlow Initial→Failure`() = runTest {
        val exception = Exception()
        val target = LoadStateFlow<Unit>()
        target.test {
            assertThat(awaitItem()).isEqualTo(LoadState.Initial)
            target.load { Result.failure(exception) }
            assertThat(awaitItem()).isEqualTo(LoadState.Loading<Unit>())
            assertThat(awaitItem()).isEqualTo(LoadState.Failure<Unit>(exception))
        }
    }

    @Test
    fun `LoadStateFlow Failure→Success`() = runTest {
        val exception = Exception()
        val target = LoadStateFlow<Unit>(LoadState.Failure(exception))
        target.test {
            assertThat(awaitItem()).isEqualTo(LoadState.Failure<Unit>(exception))
            target.load { Result.success(Unit) }
            assertThat(
                awaitItem(),
            ).isEqualTo(LoadState.Loading<Unit>(exceptionOrNull = exception))
            assertThat(awaitItem()).isEqualTo(LoadState.Success(Unit, exception))
        }
    }

    @Test
    fun `LoadStateFlow Failure→Failure`() = runTest {
        val exception = Exception()
        val exception2 = Exception()
        val target = LoadStateFlow<Unit>(LoadState.Failure(exception))
        target.test {
            assertThat(awaitItem()).isEqualTo(LoadState.Failure<Unit>(exception))
            target.load { Result.failure(exception2) }
            assertThat(
                awaitItem(),
            ).isEqualTo(LoadState.Loading<Unit>(exceptionOrNull = exception))
            assertThat(awaitItem()).isEqualTo(LoadState.Failure<Unit>(exception2))
        }
    }

    @Test
    fun `LoadStateFlow 実行中の場合、実行できない`() = runTest {
        val target = LoadStateFlow<Unit>()
        val result1: Boolean
        var result2: Boolean? = null
        result1 =
            target.load {
                result2 = target.load { Result.success(Unit) }
                Result.success(Unit)
            }
        assertThat(result1).isTrue()
        assertThat(result2).isFalse()
    }

    @Test
    fun `asStateFlow で StateFlow_LoadState_T に変換される`() {
        val target = LoadStateFlow<Unit>().asStateFlow()
        assertThat(target).isInstanceOf(StateFlow::class.java)
        assertThat(target).isNotInstanceOf(LoadStateFlow::class.java)
    }

    @Test
    fun `asStateFlow を使うと LoadStateFlow にキャストできない`() {
        val target: StateFlow<LoadState<Unit>> = LoadStateFlow<Unit>().asStateFlow()
        val result = runCatching { target as LoadStateFlow<Unit> }
        assertThat(result.getOrNull()).isNull()
        assertThat(result.exceptionOrNull()).isInstanceOf(ClassCastException::class.java)
    }

    @Test
    fun `asStateFlow を使わないと LoadStateFlow にキャストできる`() {
        val target: StateFlow<LoadState<Unit>> = LoadStateFlow()
        val result = runCatching { target as LoadStateFlow<Unit> }
        assertThat(result.getOrNull()).isInstanceOf(LoadStateFlow::class.java)
        assertThat(result.exceptionOrNull()).isNull()
    }
}
