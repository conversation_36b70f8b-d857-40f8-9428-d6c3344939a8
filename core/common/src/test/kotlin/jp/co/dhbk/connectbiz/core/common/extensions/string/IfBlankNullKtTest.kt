package jp.co.dhbk.connectbiz.core.common.extensions.string

import com.google.common.truth.Truth.assertThat
import org.junit.Test

class IfBlankNullKtTest {
    @Test
    fun `null のとき、null`() {
        val target: String? = null
        assertThat(target.ifBlankNull()).isEqualTo(null)
    }

    @Test
    fun `empty のとき、null`() {
        val target = ""
        assertThat(target.ifBlankNull()).isEqualTo(null)
    }

    @Test
    fun `blank のとき、null`() {
        val target = " 　"
        assertThat(target.ifBlankNull()).isEqualTo(null)
    }

    @Test
    fun `文字があるとき、同じ文字`() {
        val target = "ああああ"
        assertThat(target.ifBlankNull()).isEqualTo("ああああ")
    }
}
