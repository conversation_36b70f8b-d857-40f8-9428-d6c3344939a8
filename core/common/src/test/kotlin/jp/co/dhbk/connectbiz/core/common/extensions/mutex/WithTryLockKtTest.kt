package jp.co.dhbk.connectbiz.core.common.extensions.mutex

import com.google.common.truth.Truth.assertThat
import kotlinx.coroutines.sync.Mutex
import org.junit.Test

class WithTryLockKtTest {
    @Test
    fun `ロックされていないときは実行できる`() {
        val mutex = Mutex()
        mutex.withTryLock {}
        var flag = false
        assertThat(mutex.withTryLock { flag = true }).isTrue()
        assertThat(flag).isTrue()
    }

    @Test
    fun `ロックされているときは実行できない`() {
        val mutex = Mutex()
        mutex.tryLock()
        mutex.withTryLock {}
        var flag = false
        assertThat(mutex.withTryLock { flag = true }).isFalse()
        assertThat(flag).isFalse()
    }

    @Test
    fun `実行中はロックされる`() {
        val mutex = Mutex()
        assertThat(mutex.isLocked).isFalse()
        mutex.withTryLock {
            assertThat(mutex.isLocked).isTrue()
        }
        assertThat(mutex.isLocked).isFalse()
    }
}
