package jp.co.dhbk.connectbiz.core.common.extensions.string

import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test

class CheckForMimeTypeMatchKtTest {

    @Test
    fun `findMatchMimeTypeForDownload CSV is True`() {
        val result = "text/csv".findMatchMimeTypeForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Image is True`() {
        val result = "image".findMatchMimeTypeForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Excel is True`() {
        val result = "application/vnd.ms-excel".findMatchMimeTypeForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Spreadsheet is True`() {
        val result = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            .findMatchMimeTypeForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload MS Word is True`() {
        val result = "application/msword".findMatchMimeTypeForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Word is True`() {
        val result = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            .findMatchMimeTypeForDownload()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Empty is False`() {
        val result = "".findMatchMimeTypeForDownload()
        assertFalse(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Blank is False`() {
        val result = "　　".findMatchMimeTypeForDownload()
        assertFalse(result)
    }

    @Test
    fun `findMatchMimeTypeForDownload Any Unmatched Characters are False`() {
        val result = "application/vnd.amazon.ebook".findMatchMimeTypeForDownload()
        assertFalse(result)
    }

    @Test
    fun `findMatchMimeTypeOfApplicationPdf PDF is True`() {
        val result = "application/pdf".findMatchMimeTypeOfApplicationPdf()
        assertTrue(result)
    }

    @Test
    fun `findMatchMimeTypeOfApplicationPdf Empty is False`() {
        val result = "".findMatchMimeTypeOfApplicationPdf()
        assertFalse(result)
    }

    @Test
    fun `findMatchMimeTypeOfApplicationPdf Blank is False`() {
        val result = "　　".findMatchMimeTypeOfApplicationPdf()
        assertFalse(result)
    }

    @Test
    fun `findMatchMimeTypeOfApplicationPdf Any Unmatched Characters are False`() {
        val result = "application/vnd.amazon.ebook".findMatchMimeTypeOfApplicationPdf()
        assertFalse(result)
    }
}
