package jp.co.dhbk.connectbiz.core.common.models

import com.google.common.truth.Truth.assertThat
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.core.common.loadstate.map
import org.junit.Test

class LoadStateKtTest {
    @Test
    fun `map Initial`() {
        val target: LoadState<Int> = LoadState.Initial
        assertThat(target.map { it + 1 }).isEqualTo(LoadState.Initial)
    }

    @Test
    fun `map Loading`() {
        val target1: LoadState<Int> = LoadState.Loading()
        assertThat(target1.map { it + 1 })
            .isEqualTo(LoadState.Loading<Int>())

        val target2: LoadState<Int> = LoadState.Loading(valueOrNull = 1)
        assertThat(target2.map { it + 1 })
            .isEqualTo(LoadState.Loading(valueOrNull = 2))

        val e = Exception()
        val target3: LoadState<Int> = LoadState.Loading(valueOrNull = 1, exceptionOrNull = e)
        assertThat(target3.map { it + 1 })
            .isEqualTo(LoadState.Loading(valueOrNull = 2, exceptionOrNull = e))
    }

    @Test
    fun `map Success`() {
        val target1: LoadState<Int> = LoadState.Success(value = 1)
        assertThat(target1.map { it + 1 })
            .isEqualTo(LoadState.Success(value = 2))

        val e = Exception()
        val target2: LoadState<Int> = LoadState.Loading(valueOrNull = 1, exceptionOrNull = e)
        assertThat(target2.map { it + 1 })
            .isEqualTo(LoadState.Loading(valueOrNull = 2, exceptionOrNull = e))
    }

    @Test
    fun `map Failure`() {
        val e = Exception()
        val target1: LoadState<Int> = LoadState.Failure(exception = e)
        assertThat(target1.map { it + 1 })
            .isEqualTo(LoadState.Failure<Int>(exception = e))

        val target2: LoadState<Int> = LoadState.Failure(valueOrNull = 1, exception = e)
        assertThat(target2.map { it + 1 })
            .isEqualTo(LoadState.Failure(valueOrNull = 2, exception = e))
    }
}
