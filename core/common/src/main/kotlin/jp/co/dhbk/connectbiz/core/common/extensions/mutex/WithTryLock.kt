package jp.co.dhbk.connectbiz.core.common.extensions.mutex

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * [Mutex] で [action] を実行する
 *
 * すでに [Mutex] がロックされている場合は [action] を実行せずに `false` を返す
 *
 * @param owner 所有者のトークン
 * @param action 実行処理
 * @return 実行されたときは `true`
 * @see [Mutex.tryLock]
 * @see [Mutex.withLock]
 */
inline fun Mutex.withTryLock(owner: Any? = null, action: () -> Unit): Boolean {
    if (tryLock(owner = owner).not()) return false
    try {
        action()
    } finally {
        unlock(owner)
    }
    return true
}
