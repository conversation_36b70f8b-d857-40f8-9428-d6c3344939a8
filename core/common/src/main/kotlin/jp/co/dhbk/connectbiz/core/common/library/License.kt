package jp.co.dhbk.connectbiz.core.common.library

import jp.co.dhbk.connectbiz.core.common.extensions.string.ifBlankNull
import java.net.URL

/**
 * ライセンス
 *
 * @property name ライセンス名
 * @property content ライセンス原文
 * @property url ライセンスへのリンク
 */
data class License(
    val name: String,
    val content: String?,
    val url: URL?,
) {
    constructor(alLicense: com.mikepenz.aboutlibraries.entity.License) : this(
        name = alLicense.name,
        content =
        alLicense.licenseContent
            ?.replace(Regex("<br />|<br/>|<br>"), "\n").ifBlankNull(),
        url = alLicense.url?.toUrlOrNull(),
    )

    companion object {
        private fun String.toUrlOrNull() = try {
            URL(this)
        } catch (e: Exception) {
            null
        }
    }
}
