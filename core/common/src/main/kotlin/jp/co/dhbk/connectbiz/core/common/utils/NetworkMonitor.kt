package jp.co.dhbk.connectbiz.core.common.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.net.HttpURLConnection
import java.net.URL

class NetworkMonitor(
    context: Context,
) {

    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    private var networkCallback: ConnectivityManager.NetworkCallback? = null

    @SuppressLint("MissingPermission")
    fun startMonitoring(onNetworkChange: (Boolean) -> Unit) {
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                checkConnectivity(onNetworkChange)
                Timber.e("Network onAvailable $onNetworkChange")
            }

            override fun onLost(network: Network) {
                checkConnectivity(onNetworkChange)
                Timber.e("Network Lost $onNetworkChange")
            }
        }

        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        networkCallback?.let {
            connectivityManager.registerNetworkCallback(networkRequest, it)
        }
    }

    fun stopMonitoring() {
        networkCallback?.let {
            connectivityManager.unregisterNetworkCallback(it)
        }
        networkCallback = null
    }

    @SuppressLint("MissingPermission")
    fun checkConnectivity(onNetworkChange: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            val activeNetwork = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
            val isConnected = if (networkCapabilities
                    ?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            ) {
                isInternetReachable()
            } else {
                false
            }
            withContext(Dispatchers.Main) {
                onNetworkChange(isConnected)
            }
        }
    }

    private suspend fun isInternetReachable(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL("https://www.google.com")
                val connection = url.openConnection() as HttpURLConnection
                connection.apply {
                    requestMethod = "HEAD"
                    connectTimeout = 5000
                    readTimeout = 5000
                }
                val responseCode = connection.responseCode
                responseCode == HttpURLConnection.HTTP_OK
            } catch (e: Exception) {
                Timber.e("Ex at ${e.message}")
                false
            }
        }
    }
}
