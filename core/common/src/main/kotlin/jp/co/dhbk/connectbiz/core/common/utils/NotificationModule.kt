package jp.co.dhbk.connectbiz.core.common.utils

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object NotificationModule {

    @Provides
    fun provideDownloadNotificationHelper(
        @ApplicationContext context: Context,
    ): DownloadNotificationHelper {
        return DownloadNotificationHelper(context)
    }
}
