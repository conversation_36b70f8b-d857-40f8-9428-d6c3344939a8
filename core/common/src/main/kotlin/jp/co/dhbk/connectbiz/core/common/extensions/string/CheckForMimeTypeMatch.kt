package jp.co.dhbk.connectbiz.core.common.extensions.string

import jp.co.dhbk.connectbiz.core.common.model.MimeTypes

/**
 * Find match mime type from [MimeTypes] for download.
 *
 * Check the following items:
 * - [MimeTypes.text.TXT]
 * - [MimeTypes.text.CSV]
 * - [MimeTypes.IMAGE]
 * - [MimeTypes.application.vnd.MS_EXCEL]
 * - [MimeTypes.application.vnd.SPREADSHEET]
 * - [MimeTypes.application.vnd.WORD]
 * - [MimeTypes.application.vnd.MS_WORD]
 *
 * @return true if the mime type is matched
 */
fun String.findMatchMimeTypeForDownload(): Boolean = when {
    this.contains(
        MimeTypes.text.CSV,
        ignoreCase = true,
    ) -> true

    this.contains(
        MimeTypes.IMAGE,
        ignoreCase = true,
    ) -> true

    this.contains(
        MimeTypes.application.vnd.MS_EXCEL,
        ignoreCase = true,
    ) -> true

    this.contains(
        MimeTypes.application.vnd.SPREADSHEET,
        ignoreCase = true,
    ) -> true

    this.contains(
        MimeTypes.application.vnd.DOCUMENT,
        ignoreCase = true,
    ) -> true

    this.contains(
        MimeTypes.application.vnd.MS_WORD,
        ignoreCase = true,
    ) -> true

    this.contains(
        MimeTypes.application.APPLICATION_PDF,
        ignoreCase = true,
    ) -> true

    else -> false
}

/**
 * Find match mime type of [MimeTypes.application.APPLICATION_PDF].
 *
 * Check the following items:
 * - [MimeTypes.application.APPLICATION_PDF]
 *
 * @return true if the mime type is matched
 */
fun String.findMatchMimeTypeOfApplicationPdf(): Boolean = this.contains(
    MimeTypes.application.APPLICATION_PDF,
    ignoreCase = true,
)
