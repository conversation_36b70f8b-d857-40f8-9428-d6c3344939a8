package jp.co.dhbk.connectbiz.core.common.utils

import timber.log.Timber
import kotlin.coroutines.cancellation.CancellationException

/**
 * [CancellationException] と [Error] の場合 `throw` する [runCatching]
 *
 * @param R [Result] でラップされる型
 * @param loggingEnabled ログを出力する場合は `true`
 * @param block 呼び出される処理
 * @return [Result]<[R]>
 * @throws [CancellationException]
 * @throws [Error]
 * @see [runCatching]
 */
inline fun <R> runCatchingOrThrow(loggingEnabled: Boolean = true, block: () -> R): Result<R> {
    return try {
        Result.success(block())
    } catch (cancellationException: CancellationException) {
        throw cancellationException
    } catch (exception: Exception) {
        if (loggingEnabled) Timber.e(exception)
        Result.failure(exception)
    }
}
