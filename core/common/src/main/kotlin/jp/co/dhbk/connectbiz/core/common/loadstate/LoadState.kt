package jp.co.dhbk.connectbiz.core.common.loadstate

/**
 * 読み込み状態
 *
 * @param T 読み込み対象の型
 */
sealed interface LoadState<out T : Any> {
    /**
     * 最新の成功時の値、無い場合は `null`
     */
    val valueOrNull: T?

    /**
     * 最新の失敗時の例外、無い場合は `null`
     */
    val exceptionOrNull: Exception?

    /**
     * 初期
     */
    data object Initial : LoadState<Nothing> {
        override val valueOrNull: Nothing? get() = null
        override val exceptionOrNull: Exception? get() = null
    }

    /**
     * 読み込み中
     */
    data class Loading<T : Any>(
        override val valueOrNull: T? = null,
        override val exceptionOrNull: Exception? = null,
    ) : LoadState<T>

    /**
     * 成功
     */
    data class Success<T : Any>(
        val value: T,
        override val exceptionOrNull: Exception? = null,
    ) : LoadState<T> {
        override val valueOrNull: T get() = value
    }

    /**
     * 失敗
     */
    data class Failure<T : Any>(
        val exception: Exception,
        override val valueOrNull: T? = null,
    ) : LoadState<T> {
        override val exceptionOrNull: Exception get() = exception
    }
}

/**
 * [LoadState]<[T]> を [LoadState]<[R]> へ変換
 *
 * @param T 変換前の型
 * @param R 変換後の型
 * @param transform 変換処理
 * @return [LoadState]<[R]>
 */
inline fun <T : Any, R : Any> LoadState<T>.map(transform: (value: T) -> R): LoadState<R> =
    when (this) {
        is LoadState.Initial ->
            LoadState.Initial

        is LoadState.Loading ->
            LoadState.Loading(
                valueOrNull = valueOrNull?.let(transform),
                exceptionOrNull = exceptionOrNull,
            )

        is LoadState.Success ->
            LoadState.Success(
                value = transform(value),
                exceptionOrNull = exceptionOrNull,
            )

        is LoadState.Failure ->
            LoadState.Failure(
                valueOrNull = valueOrNull?.let(transform),
                exception = exception,
            )
    }
