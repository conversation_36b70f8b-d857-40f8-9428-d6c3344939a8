package jp.co.dhbk.connectbiz.core.common.model

/**
 * MimeType constants.
 */
object MimeTypes {

    /**
     * Mime type of the image file.
     */
    const val IMAGE = "image"

    /**
     * [Text]
     */
    val text: Text = Text

    /**
     * Mime type of the text file.
     *
     * @property TXT Mime type of the TXT file
     * @property CSV Mime type of the CSV file
     */
    object Text {
        const val TXT = "text/plain"
        const val CSV = "text/csv"
    }

    /**
     * [Application]
     */
    val application: Application = Application

    /**
     * Mime type of the file attached to the email
     *
     * @property APPLICATION_PDF Mime type of the PDF file
     * @property OCTET_STREAM Mime type of the octet stream file
     * @property JSON Mime type of the JSON file
     * @property vnd [VND]
     */
    object Application {
        const val APPLICATION_PDF = "application/pdf"
        const val OCTET_STREAM = "application/octet-stream"
        const val JSON = "application/json"
        val vnd: VND = VND

        /**
         * Mime type of vnd file.
         *
         * @property SPREADSHEET Mime type of the spreadsheet file
         * @property DOCUMENT Mime type of the Word file
         * @property PRESENTATION Mime type of the PowerPoint file
         * @property MS_WORD Mime type of the MS Word file
         * @property MS_EXCEL Mime type of the MS Excel file
         * @property MS_POWERPOINT Mime type of the MS PowerPoint file
         */
        data object VND {
            const val SPREADSHEET =
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            const val DOCUMENT =
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            const val PRESENTATION =
                "application/vnd.openxmlformats-officedocument.presentationml.presentation"
            const val MS_EXCEL = "application/vnd.ms-excel"
            const val MS_WORD = "application/msword"
            const val MS_POWERPOINT = "application/vnd.ms-powerpoint"
        }
    }

    /**
     * Mime type of the any file.
     */
    const val ANY = "*/*"
}
