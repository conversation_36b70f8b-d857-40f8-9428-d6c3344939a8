package jp.co.dhbk.connectbiz.core.common.extensions.string

import jp.co.dhbk.connectbiz.core.common.model.FileExtensions

/**
 * Find match mime type from [FileExtensions] for download.
 *
 * Check the following items:
 * - [FileExtensions.text.CSV]
 * - [FileExtensions.excel.XLS]
 * - [FileExtensions.excel.XLSX]
 * - [FileExtensions.excel.XLSM]
 * - [FileExtensions.word.DOC]
 * - [FileExtensions.word.DOCX]
 *
 * @return true if the mime type is matched
 */
fun String.findMatchFileExtensionForDownload(): Boolean = when {
    this.endsWith(
        ".${FileExtensions.text.CSV}",
        ignoreCase = true,
    ) -> true

    this.endsWith(
        ".${FileExtensions.excel.XLS}",
        ignoreCase = true,
    ) -> true

    this.endsWith(
        ".${FileExtensions.excel.XLSX}",
        ignoreCase = true,
    ) -> true

    this.endsWith(
        ".${FileExtensions.excel.XLSM}",
        ignoreCase = true,
    ) -> true

    this.endsWith(
        ".${FileExtensions.word.DOC}",
        ignoreCase = true,
    ) -> true

    this.endsWith(
        ".${FileExtensions.word.DOCX}",
        ignoreCase = true,
    ) -> true

    this.endsWith(
        ".${FileExtensions.PDF}",
        ignoreCase = true,
    ) -> true

    else -> false
}

/**
 * Find match mime type of [FileExtensions.PDF].
 *
 * Check the following items:
 * - [FileExtensions.PDF]
 *
 * @return true if the mime type is matched
 */
fun String.findMatchFileExtensionForPdf(): Boolean = this.endsWith(
    ".${FileExtensions.PDF}",
    ignoreCase = true,
)
