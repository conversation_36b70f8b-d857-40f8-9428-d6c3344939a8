package jp.co.dhbk.connectbiz.core.common.model

/**
 * File Extensions constants.
 */
object FileExtensions {
    /**
     * [Image]
     */
    val image: Image = Image

    /**
     * Image file extensions.
     *
     * @property JPG JPG file extension
     * @property JPEG JPEG file extension
     * @property PNG PNG file extension
     * @property GIF GIF file extension
     */
    object Image {
        const val JPG = "jpg"
        const val JPEG = "jpeg"
        const val PNG = "png"
        const val GIF = "gif"
    }

    /**
     * [Text]
     */
    val text: Text = Text

    /**
     * Text file extensions.
     *
     * @property TXT Text file extension
     * @property CSV CSV file extension
     */
    object Text {
        const val TXT = "txt"
        const val CSV = "csv"
    }

    /**
     * [Excel]
     */
    val excel: Excel = Excel

    /**
     * Excel file extensions.
     *
     * @property XLS XLS file extension
     * @property XLSX XLSX file extension
     * @property XLSM XLSM file extension
     */
    object Excel {
        const val XLS = "xls"
        const val XLSX = "xlsx"
        const val XLSM = "xlsm"
    }

    /**
     * [Word]
     */
    val word: Word = Word

    /**
     * Word file extensions.
     *
     * @property DOC DOC file extension
     * @property DOCX DOCX file extension
     */
    object Word {
        const val DOC = "doc"
        const val DOCX = "docx"
    }

    /**
     * [Powerpoint]
     */
    val powerpoint: Powerpoint = Powerpoint

    /**
     * Powerpoint file extensions.
     *
     * @property PPT PPT file extension
     * @property PPTX PPTX file extension
     */
    object Powerpoint {
        const val PPT = "ppt"
        const val PPTX = "pptx"
    }

    /**
     * PDF file extension.
     */
    const val PDF = "pdf"

    /**
     * Zip file extension.
     */
    const val ZIP = "zip"

    /**
     * Binary file extension.
     */
    const val BINARY = "bin"
}
