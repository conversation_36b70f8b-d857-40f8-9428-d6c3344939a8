package jp.co.dhbk.connectbiz.core.common.library

import android.content.Context
import com.mikepenz.aboutlibraries.Libs
import com.mikepenz.aboutlibraries.util.withContext
import jp.co.dhbk.connectbiz.core.common.extensions.string.ifBlankNull
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import java.net.URL

/**
 * ライブラリ
 *
 * @property id 固有の ID
 * @property name ライブラリ名
 * @property authors 著者
 * @property licenses ライセンス
 * @property url ライブラリへのリンク
 */
data class Library(
    val id: String,
    val name: String,
    val authors: ImmutableList<String>,
    val licenses: ImmutableList<License>,
    val url: URL?,
) {
    constructor(alLibrary: com.mikepenz.aboutlibraries.entity.Library) : this(
        id = alLibrary.uniqueId,
        name = alLibrary.name,
        // com.mikepenz.aboutlibraries.entity.Library.author と同等の処理
        authors =
        alLibrary.developers.mapNotNull { it.name.ifBlankNull() }
            .ifEmpty { listOf(alLibrary.organization?.name).mapNotNull { it.ifBlankNull() } }
            .toImmutableList(),
        licenses = alLibrary.licenses.map { License(it) }.toImmutableList(),
        // scm > website > organization の優先順位で表示する URL を決める
        url =
        alLibrary.scm?.url?.toUrlOrNull()
            ?: alLibrary.website?.toUrlOrNull()
            ?: alLibrary.organization?.url?.toUrlOrNull(),
    )

    companion object {
        /**
         * [context] からライブラリ一覧を取得する
         *
         * @param context [Context]
         * @return ライブラリ一覧
         */
        fun getLibraries(context: Context): ImmutableList<Library> =
            Libs.Builder().withContext(context).build()
                .libraries.map { Library(it) }.toImmutableList()

        private fun String.toUrlOrNull() = try {
            URL(this)
        } catch (e: Exception) {
            null
        }
    }
}
