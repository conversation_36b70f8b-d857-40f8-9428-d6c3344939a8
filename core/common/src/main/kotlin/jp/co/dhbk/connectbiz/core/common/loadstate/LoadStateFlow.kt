package jp.co.dhbk.connectbiz.core.common.loadstate

import jp.co.dhbk.connectbiz.core.common.extensions.mutex.withTryLock
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex

/**
 * [LoadState]<[T]> を [StateFlow] で管理するためのクラス
 *
 * [load] により、[StateFlow.value] を更新する
 *
 * @param T 読み込み対象の型
 */
interface LoadStateFlow<T : Any> : StateFlow<LoadState<T>> {
    /**
     * [function] により [StateFlow.value] を更新する
     *
     * @param function [Result]<[T]> を返す読み込み処理
     * @return 処理が実行されたときは `true`
     */
    suspend fun load(function: suspend () -> Result<T>): Boolean

    companion object {
        /**
         * @param T 読み込み対象の型
         * @param initialState 初期値
         * @return [LoadStateFlow]<[T]>
         */
        operator fun <T : Any> invoke(
            initialState: LoadState<T> = LoadState.Initial,
        ): LoadStateFlow<T> = LoadStateFlowImpl(MutableStateFlow(initialState))
    }
}

private class LoadStateFlowImpl<T : Any>(
    private val delegate: MutableStateFlow<LoadState<T>>,
    private val mutex: Mutex = Mutex(),
) : LoadStateFlow<T>, StateFlow<LoadState<T>> by delegate.asStateFlow() {
    override suspend fun load(function: suspend () -> Result<T>): Boolean = mutex.withTryLock {
        val state = delegate.value
        delegate.value =
            LoadState.Loading(
                valueOrNull = state.valueOrNull,
                exceptionOrNull = state.exceptionOrNull,
            )
        try {
            delegate.value =
                LoadState.Success(
                    value = function().getOrThrow(),
                    exceptionOrNull = state.exceptionOrNull,
                )
        } catch (cancellationException: CancellationException) {
            delegate.value = state
            throw cancellationException
        } catch (exception: Exception) {
            delegate.value =
                LoadState.Failure(
                    valueOrNull = state.valueOrNull,
                    exception = exception,
                )
        }
    }
}

/**
 * [LoadStateFlow]<[T]> を読み取り専用（読み込み不可）な [StateFlow]<[LoadState]<[T]>> に変換する
 *
 * @param T 読み込み対象の型
 * @return 読み取り専用（読み込み不可）な [StateFlow]<[LoadState]<[T]>>
 */
fun <T : Any> LoadStateFlow<T>.asStateFlow(): StateFlow<LoadState<T>> = ReadonlyStateFlow(
    delegate = this,
)

private class ReadonlyStateFlow<T>(private val delegate: StateFlow<T>) : StateFlow<T> by delegate
