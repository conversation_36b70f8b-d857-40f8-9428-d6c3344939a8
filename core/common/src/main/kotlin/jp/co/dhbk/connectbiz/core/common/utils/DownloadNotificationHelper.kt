package jp.co.dhbk.connectbiz.core.common.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import androidx.core.app.NotificationCompat
import javax.inject.Inject

class DownloadNotificationHelper @Inject constructor(
    private val context: Context,
) {
    private val notificationManager = context.getSystemService(
        Context.NOTIFICATION_SERVICE,
    ) as NotificationManager
    private val channelId = "download_channel"

    init {
        val channel = NotificationChannel(
            channelId,
            "ダウンロード通知",
            NotificationManager.IMPORTANCE_LOW,
        )
        notificationManager.createNotificationChannel(channel)
    }

    // Show progress notification
    fun showProgressNotification(progress: Int) {
        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setContentTitle("Downloading File")
            .setContentText("$progress% downloaded")
            .setProgress(100, progress, false)
            .build()

        notificationManager.notify(1, notification)
    }

    // Show completion notification
    fun showCompletionNotification() {
        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(android.R.drawable.stat_sys_download_done)
            .setContentTitle("Download Complete")
            .setContentText("File has been downloaded successfully.")
            .setAutoCancel(true)
            .build()

        notificationManager.notify(1, notification)
    }

    // Cancel the notification
    fun cancelNotification() {
        notificationManager.cancel(1)
    }

    // Show error notification
    fun showErrorNotification(errorMessage: String) {
        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(android.R.drawable.stat_notify_error)
            .setContentTitle("Download Failed")
            .setContentText(errorMessage)
            .setAutoCancel(true)
            .build()

        notificationManager.notify(1, notification)
    }
}
