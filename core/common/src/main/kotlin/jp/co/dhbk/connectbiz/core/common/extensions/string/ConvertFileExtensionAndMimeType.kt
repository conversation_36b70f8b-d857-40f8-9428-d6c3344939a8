package jp.co.dhbk.connectbiz.core.common.extensions.string

import jp.co.dhbk.connectbiz.core.common.model.FileExtensions
import jp.co.dhbk.connectbiz.core.common.model.MimeTypes

/**
 * Convert [FileExtensions] to [MimeTypes].
 *
 * @return the [MimeTypes] as [String]
 */
fun String.convertFileExtensionToMimeType(): String = when {
    this.contains(
        FileExtensions.image.JPG,
        ignoreCase = true,
    ) -> MimeTypes.IMAGE

    this.contains(
        FileExtensions.image.JPEG,
        ignoreCase = true,
    ) -> MimeTypes.IMAGE

    this.contains(
        FileExtensions.image.PNG,
        ignoreCase = true,
    ) -> MimeTypes.IMAGE

    this.contains(
        FileExtensions.image.GIF,
        ignoreCase = true,
    ) -> MimeTypes.IMAGE

    this.contains(
        FileExtensions.text.TXT,
        ignoreCase = true,
    ) -> MimeTypes.text.TXT

    this.contains(
        FileExtensions.text.CSV,
        ignoreCase = true,
    ) -> MimeTypes.text.CSV

    this.contains(
        FileExtensions.excel.XLS,
        ignoreCase = true,
    ) -> MimeTypes.application.vnd.MS_EXCEL

    this.contains(
        FileExtensions.excel.XLSX,
        ignoreCase = true,
    ) -> MimeTypes.application.vnd.SPREADSHEET

    this.contains(
        FileExtensions.word.DOC,
        ignoreCase = true,
    ) -> MimeTypes.application.vnd.MS_WORD

    this.contains(
        FileExtensions.word.DOCX,
        ignoreCase = true,
    ) -> MimeTypes.application.vnd.DOCUMENT

    this.contains(
        FileExtensions.powerpoint.PPT,
        ignoreCase = true,
    ) -> MimeTypes.application.vnd.MS_POWERPOINT

    this.contains(
        FileExtensions.powerpoint.PPTX,
        ignoreCase = true,
    ) -> MimeTypes.application.vnd.PRESENTATION

    else -> MimeTypes.ANY // Default mime type if no match
}

/**
 * Convert [MimeTypes] to [FileExtensions].
 *
 * @return the [FileExtensions] as [String]
 */
fun String.convertMimeTypeToExtension(): String = when {
    this.contains(
        MimeTypes.IMAGE,
        ignoreCase = true,
    ) -> FileExtensions.image.JPG // Default to JPG for images

    this.contains(
        MimeTypes.text.TXT,
        ignoreCase = true,
    ) -> FileExtensions.text.TXT

    this.contains(
        MimeTypes.text.CSV,
        ignoreCase = true,
    ) -> FileExtensions.text.CSV

    this.contains(
        MimeTypes.application.APPLICATION_PDF,
        ignoreCase = true,
    ) -> FileExtensions.PDF

    this.contains(
        MimeTypes.application.OCTET_STREAM,
        ignoreCase = true,
    ) -> FileExtensions.BINARY

    this.contains(
        MimeTypes.application.vnd.SPREADSHEET,
        ignoreCase = true,
    ) -> FileExtensions.excel.XLSX

    this.contains(
        MimeTypes.application.vnd.DOCUMENT,
        ignoreCase = true,
    ) -> FileExtensions.word.DOCX

    this.contains(
        MimeTypes.application.vnd.PRESENTATION,
        ignoreCase = true,
    ) -> FileExtensions.powerpoint.PPTX

    this.contains(
        MimeTypes.application.vnd.MS_EXCEL,
        ignoreCase = true,
    ) -> FileExtensions.excel.XLS

    this.contains(
        MimeTypes.application.vnd.MS_WORD,
        ignoreCase = true,
    ) -> FileExtensions.word.DOC

    this.contains(
        MimeTypes.application.vnd.MS_POWERPOINT,
        ignoreCase = true,
    ) -> FileExtensions.powerpoint.PPT

    else -> FileExtensions.BINARY // Default extension if no match
}
