package jp.co.dhbk.connectbiz.core.common.utils

import android.content.Context
import android.content.SharedPreferences
import android.webkit.CookieManager
import android.webkit.WebStorage
import android.webkit.WebView
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys
import com.auth0.jwt.JWT
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.UUID
import javax.crypto.AEADBadTagException
import javax.inject.Inject
import javax.inject.Singleton

object ConnectbizPrefKey {
    const val COOKIES = "cookies"
    const val SESSION_COOKIES = "session_cookies"
    const val IS_USER_LOGGED_IN = "is_user_logged_in"
    const val UUID = "device_id"
    const val IS_TUTORIAL_SHOWN = "is_tutorial_shown" // New key for tutorial status
    const val IS_BIOMETRIC_ENABLED = "is_biometric_enabled"
    const val IS_BIOMETRIC_SKIPPED_BY_USER = "is_biometric_skipped"
    const val IS_BIOMETRIC_DISABLE_BY_USER = "is_biometric_disable"
    const val SESSION_TOKEN = "jwt_token"
    const val PUSH_URL = "push_url"
    const val PUSH_HANDLE = "push_handle"
    const val LASTVISTED_URL = "last visted url"
    const val RETRY_TIME = "retry_Time"
}

@Singleton
class ConnectbizPreference
@Inject
constructor(
    @ApplicationContext val context: Context,
) {
    private val sharedPrefsFile: String = "aladdin"

    // Although you can define your own key generation parameter specification, it's
    // recommended that you use the value specified here.
    private val keyGenParameterSpec = MasterKeys.AES256_GCM_SPEC
    private val mainKeyAlias = MasterKeys.getOrCreate(keyGenParameterSpec)

    private val sharedPreferences: SharedPreferences =
        try {
            EncryptedSharedPreferences.create(
                sharedPrefsFile,
                mainKeyAlias,
                context,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
            )
        } catch (_: AEADBadTagException) {
            // Handle data corruption or key invalidation
            context.deleteSharedPreferences(sharedPrefsFile) // Clear corrupted data

            // Re-initialize EncryptedSharedPreferences after clearing
            EncryptedSharedPreferences.create(
                sharedPrefsFile,
                mainKeyAlias,
                context,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
            )
        } catch (e: Exception) {
            // Handle other exceptions (e.g., key generation errors)
            throw RuntimeException("Failed to initialize EncryptedSharedPreferences", e)
        }

    init {
        val uuid = getString(ConnectbizPrefKey.UUID)
        if (uuid == null) {
            save(ConnectbizPrefKey.UUID, UUID.randomUUID().toString())
        }
    }

    fun <T> save(key: String, value: T) {
        with(sharedPreferences.edit()) {
            when (value) {
                is Boolean -> {
                    putBoolean(key, value)
                }
                is Float -> {
                    putFloat(key, value)
                }
                is Long -> {
                    putLong(key, value)
                }
                is String -> {
                    putString(key, value)
                }
                is Int -> {
                    putInt(key, value)
                }
            }
            apply()
        }
    }

    fun getString(key: String): String? {
        return sharedPreferences.getString(key, null)
    }

    fun remove(key: String) {
        with(sharedPreferences.edit()) {
            remove(key)
            apply()
        }
    }

    fun isTutorialShown(): Boolean {
        return sharedPreferences.getBoolean(ConnectbizPrefKey.IS_TUTORIAL_SHOWN, false)
    }

    fun setTutorialShown(shown: Boolean) {
        save(ConnectbizPrefKey.IS_TUTORIAL_SHOWN, shown)
    }

    fun setBiometricEnabled(enabled: Boolean) {
        save(ConnectbizPrefKey.IS_BIOMETRIC_ENABLED, enabled)
    }

    fun isBiometricEnabled(): Boolean {
        return sharedPreferences.getBoolean(ConnectbizPrefKey.IS_BIOMETRIC_ENABLED, false)
    }

    fun setBiometricSkippedByUser(enabled: Boolean) {
        save(ConnectbizPrefKey.IS_BIOMETRIC_SKIPPED_BY_USER, enabled)
    }

    fun isBiometricSkippedByUser(): Boolean {
        return sharedPreferences.getBoolean(
            ConnectbizPrefKey.IS_BIOMETRIC_SKIPPED_BY_USER,
            false,
        )
    }

    fun setBiometricDisabledByUser(enabled: Boolean) {
        save(ConnectbizPrefKey.IS_BIOMETRIC_DISABLE_BY_USER, enabled)
    }

    fun setUserLoggedIn(isLogin: Boolean) {
        save(ConnectbizPrefKey.IS_USER_LOGGED_IN, isLogin)
    }

    fun getUserLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(ConnectbizPrefKey.IS_USER_LOGGED_IN, false)
    }

    fun setCookies(cookies: String) {
        save(ConnectbizPrefKey.COOKIES, cookies)
    }

    fun getCookies(): String {
        return sharedPreferences.getString(ConnectbizPrefKey.COOKIES, null).toString()
    }

    fun removeCookies() {
        remove(ConnectbizPrefKey.COOKIES)
    }

    fun setSessionCookies(sessionCookies: String) {
        save(ConnectbizPrefKey.SESSION_COOKIES, sessionCookies)
    }

    fun getSessionCookies(): String {
        return sharedPreferences.getString(ConnectbizPrefKey.SESSION_COOKIES, null).toString()
    }

    // Session management
    fun saveSessionToken(token: String) {
        save(ConnectbizPrefKey.SESSION_TOKEN, token)
    }

    fun getSessionToken(): String? = getString(ConnectbizPrefKey.SESSION_TOKEN)

    fun isSessionExpired(): Boolean {
        val token = getSessionToken() ?: return true
        return try {
            val decodedJWT = JWT.decode(token)
            val expirationTime = decodedJWT.expiresAt?.time ?: return true
            System.currentTimeMillis() > expirationTime
        } catch (_: Exception) {
            true // If there's an error decoding the token, assume it's expired
        }
    }

    fun clearSession() {
        remove(ConnectbizPrefKey.SESSION_TOKEN)
        setUserLoggedIn(false)
        clearWebViewSession(context)
        setBiometricSkippedByUser(isBiometricSkippedByUser())
    }

    private fun clearWebViewSession(context: Context) {
        // Clear cookies
        CookieManager.getInstance().apply {
            removeAllCookies(null)
            flush()
        }

        // Clear cache
        WebView(context).apply {
            clearCache(true)
            clearHistory()
        }

        // Clear web storage
        WebStorage.getInstance().deleteAllData()
    }

    fun setPushHandle(bool: Boolean) {
        save(ConnectbizPrefKey.PUSH_HANDLE, bool)
    }

    fun setPushUrl(url: String) {
        save(ConnectbizPrefKey.PUSH_URL, url)
    }

    fun getPushUrl(): String? {
        return sharedPreferences.getString(ConnectbizPrefKey.PUSH_URL, null)
    }

    fun isPushHandle(): Boolean? {
        return sharedPreferences.getBoolean(ConnectbizPrefKey.PUSH_HANDLE, false)
    }

    fun setLastVistedURL(url: String) {
        save(ConnectbizPrefKey.LASTVISTED_URL, url)
    }

    fun getLastVistedURL(): String? {
        return sharedPreferences.getString(ConnectbizPrefKey.LASTVISTED_URL, null)
    }

    fun setRetryTimestamp() {
        save(ConnectbizPrefKey.RETRY_TIME, System.currentTimeMillis())
    }

    fun getRetryTimestamp(): Long {
        return sharedPreferences.getLong(ConnectbizPrefKey.RETRY_TIME, 0)
    }
}
