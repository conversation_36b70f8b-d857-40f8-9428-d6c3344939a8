# :core:common

共通で使えるメソッド等を定義する。

ライブラリ等の標準の関数などでは物足りない場合に、便利な関数などを定義することが主な役割である。

## 依存関係

```mermaid
graph TB
    core_common[:core:common]
    core_data[:core:data]
    core_ui[:core:ui]
    app[:app]
    domain[:domain]
    data[:data]
    ui[:ui]
    core_data --> core_common
    core_ui --> core_common
    app --> core_common
    domain --> core_common
    data --> core_common
    ui --> core_common
```
