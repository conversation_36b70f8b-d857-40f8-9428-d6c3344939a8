package jp.co.dhbk.connectbiz.usecase.notificationusecase

import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import javax.inject.Inject

class NotificationUseCase @Inject constructor(
    private val preferences: ConnectbizPreference,
) {

    private var notificationUrl: String? = null
    private var notificationHandle: Boolean? = null

    // Set the notification data (will only set if not already set)
    fun setNotificationData(url: String?, handle: Boolean?) {
        if (notificationUrl == null) notificationUrl = url
        if (notificationHandle == null) notificationHandle = handle
    }

    // Get the stored notification data
    fun getNotificationData(): Pair<String?, Boolean?> {
        return Pair(notificationUrl, notificationHandle)
    }

    // Clear the notification data
    fun clearNotificationData() {
        notificationUrl = null
        notificationHandle = null
    }

    // Sync with preferences
    fun saveToPreferences() {
        preferences.setPushHandle(notificationHandle ?: false)
        preferences.setPushUrl(notificationUrl ?: "")
    }

    fun loadFromPreferences() {
        notificationUrl = preferences.getPushUrl()
        notificationHandle = preferences.isPushHandle()
    }
}
