package jp.co.dhbk.connectbiz.usecase

import jp.co.dhbk.connectbiz.domain.model.DeviceTokenRequest
import jp.co.dhbk.connectbiz.domain.model.DeviceTokenResponse
import jp.co.dhbk.connectbiz.domain.model.error.DeviceTokenError
import jp.co.dhbk.connectbiz.domain.repository.DeviceTokenRepository
import javax.inject.Inject

class SendDeviceTokenUseCase @Inject constructor(
    private val repository: DeviceTokenRepository,
) {
    suspend operator fun invoke(request: DeviceTokenRequest): Result<DeviceTokenResponse> {
        return try {
            repository.sendDeviceToken(request)
        } catch (e: Exception) {
            Result.failure(
                Exception(
                    DeviceTokenError
                        .UnknownError(e.message.toString()).toString(),
                ),
            )
        }
    }
}
