package jp.co.dhbk.connectbiz.usecase

import jp.co.dhbk.connectbiz.domain.repository.AppMaintenanceAndUpdateInfoRepository
import javax.inject.Inject

class UseCaseFactory @Inject constructor(
    private val repository: AppMaintenanceAndUpdateInfoRepository,
) {
    fun createGetMaintenanceDataUseCase(): GetAppMaintenanceAndUpdateInfoUseCase {
        return GetAppMaintenanceAndUpdateInfoUseCase(repository)
    }

    fun createRefreshRemoteConfigUseCase(): RefreshRemoteConfigUseCase {
        return RefreshRemoteConfigUseCase(repository)
    }
}
