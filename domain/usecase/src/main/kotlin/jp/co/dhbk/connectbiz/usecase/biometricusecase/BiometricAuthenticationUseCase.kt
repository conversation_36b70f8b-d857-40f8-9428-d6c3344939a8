package jp.co.dhbk.connectbiz.usecase.biometricusecase
import android.app.Activity
import android.content.Intent
import androidx.fragment.app.FragmentActivity
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.data.network.respositories.BiometricAuthRepository
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.cancellation.CancellationException
import kotlin.coroutines.coroutineContext

class BiometricAuthenticationUseCase @Inject constructor(
    private val biometricAuthRepository: BiometricAuthRepository,
) {
    private val _authResult = MutableSharedFlow<BiometricAuthResult>(replay = 0)
    val authResult: SharedFlow<BiometricAuthResult> get() = _authResult

    // Function to perform authentication
    suspend fun execute(activity: FragmentActivity, screenType: ScreenType) {
        try {
            _authResult.emit(BiometricAuthResult.Loading)
            Timber.d("Starting biometric authentication")

            biometricAuthRepository.authenticate(activity, screenType).collect { result ->
                Timber.d("Emitting result: $result")
                _authResult.emit(result)
            }
        } catch (e: CancellationException) {
            Timber.e("Authentication process was cancelled. Reason: ${e.message}")
            // Log additional details about the cancellation
            Timber.e("CancellationException cause: ${e.cause}")
            Timber.e("Coroutine context: ${coroutineContext[Job]}")
        } catch (e: Exception) {
            Timber.e("Error during biometric authentication: ${e.message}")
        }
    }

    // Function to check if authentication can be performed
    fun canAuthenticate(screenType: ScreenType): AuthenticationStatus {
        return biometricAuthRepository.canAuthenticate(screenType)
    }

    fun getFallbackAuthenticationIntent(activity: Activity, screenType: ScreenType): Intent? {
        return biometricAuthRepository.getFallbackAuthenticationIntent(activity, screenType)
    }

    fun handleFallbackResult(requestCode: Int, resultCode: Int): BiometricAuthResult {
        return biometricAuthRepository.handleActivityResult(requestCode, resultCode)
    }

    fun getFallbackResult(): SharedFlow<BiometricAuthResult> {
        return biometricAuthRepository.getFallbackResult()
    }
}
