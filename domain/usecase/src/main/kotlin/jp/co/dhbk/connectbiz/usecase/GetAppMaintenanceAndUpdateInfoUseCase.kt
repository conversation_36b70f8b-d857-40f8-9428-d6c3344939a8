package jp.co.dhbk.connectbiz.usecase

import jp.co.dhbk.connectbiz.domain.model.AppMaintenanceAndUpdateInfoData
import jp.co.dhbk.connectbiz.domain.repository.AppMaintenanceAndUpdateInfoRepository
import javax.inject.Inject

class GetAppMaintenanceAndUpdateInfoUseCase @Inject constructor(
    private val repository: AppMaintenanceAndUpdateInfoRepository,
) {
    suspend operator fun invoke(): Result<AppMaintenanceAndUpdateInfoData> {
        return repository.getMaintenanceAndUpdateData()
    }
}
