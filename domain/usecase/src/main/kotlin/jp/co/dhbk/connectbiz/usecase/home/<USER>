package jp.co.dhbk.connectbiz.usecase.home

import android.os.Build
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import javax.inject.Inject

class GetUserAgentStringUseCase @Inject constructor(
    private val apiConfig: ApiConfig,
) {

    operator fun invoke(): String {
        val developer = "fuller-inc"
        val build = apiConfig.versionCode
        val version = getVersionCode()
        val platform = "android"
        val model = Build.MODEL
        val osVersion = Build.VERSION.RELEASE

        return "developer=$developer, " +
            "build=$build; " +
            "version=\"$version\", " +
            "platform=$platform; " +
            "model=\"$model\"; " +
            "os_version=\"$osVersion\""
    }

    private fun getVersionCode(): String {
        return apiConfig.versionName.split("-").first()
    }
}
