package jp.co.dhbk.connectbiz.usecase.home

import android.annotation.SuppressLint
import android.app.DownloadManager
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.webkit.CookieManager
import jp.co.dhbk.connectbiz.core.common.extensions.string.convertMimeTypeToExtension
import jp.co.dhbk.connectbiz.core.common.utils.DownloadNotificationHelper
import jp.co.dhbk.connectbiz.core.common.model.MimeTypes
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.net.URLDecoder
import javax.inject.Inject

class HandleFileDownloadUseCase @Inject constructor(
    private val context: Context,
    private val getUserAgentJsonUseCase: GetUserAgentStringUseCase,
    private val downloadFileUseCase: DownloadFileUseCase,
    private val notificationHelper: DownloadNotificationHelper,
) {

    operator fun invoke(
        url: String,
        mimeType: String,
        contentDisposition: String,
        onProgressUpdate: (Int) -> Unit,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit,
        onDownloadFailed: (String) -> Unit,
    ) {
        Timber.e("File Download $url")
        val fileName = extractFileNameFromContentDisposition(contentDisposition, url)
        val timeSuffix = System.currentTimeMillis()

// split name and extension
        val dotIndex = fileName.lastIndexOf('.')
        val (baseName, extension) = if (dotIndex != -1) {
            fileName.substring(0, dotIndex) to fileName.substring(dotIndex) // extension includes "."
        } else {
            fileName to "" // no extension
        }

// final format: base_timestamp.ext
        val finalFileName = "${baseName}_$timeSuffix$extension"

        Timber.e("Final File Name: $finalFileName")

        if (url.contains("Log/Export")) {
            handleCustomFileDownload(
                url,
                finalFileName,
                onProgressUpdate,
                onDownloadComplete,
                onDownloadFailed,
            )
        } else {
            handleStandardFileDownload(
                url,
                mimeType,
                finalFileName,
                onProgressUpdate,
                onDownloadComplete,
                onDownloadFailed,
            )
        }
    }

    private fun handleCustomFileDownload(
        url: String,
        fileName: String,
        onProgressUpdate: (Int) -> Unit,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit,
        onDownloadFailed: (String) -> Unit,
    ) {
        val destinationPath = "${
            Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOWNLOADS,
            )
        }/$fileName"
        CoroutineScope(Dispatchers.IO).launch {
            downloadFileUseCase(url, destinationPath)
                .onStart {
                    notificationHelper.showProgressNotification(0)
                }
                .catch { e ->
                    // Handle any errors during the flow
                    Timber.e("Download failed: ${e.message}")
                    notificationHelper.showErrorNotification(e.message ?: "Unknown error")
                    withContext(Dispatchers.Main) {
                        onDownloadFailed(e.message ?: "Unknown error")
                    }
                }
                .collect { progress ->
                    // Handle progress updates
                    if (progress == 100) {
                        // Completion of download
                        notificationHelper.showCompletionNotification()
                        withContext(Dispatchers.Main) {
                            onDownloadComplete(destinationPath,fileName)
                        }
                    } else if (progress == -1) {
                        // Download failure detected in flow
                        Timber.e("Download failed at progress: -1")
                        notificationHelper.showErrorNotification("Download failed")
                        withContext(Dispatchers.Main) {
                            onDownloadFailed("Download failed")
                        }
                    } else {
                        // Normal progress update
                        notificationHelper.showProgressNotification(progress)
                        withContext(Dispatchers.Main) {
                            onProgressUpdate(progress)
                        }
                    }
                }
        }
    }

    private fun handleStandardFileDownload(
        url: String,
        mimeType: String?,
        fileName: String,
        onProgressUpdate: (Int) -> Unit,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit,
        onDownloadFailed: (String) -> Unit,
    ) {
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val request = DownloadManager.Request(Uri.parse(url))
            .setMimeType(mimeType)
            .setTitle(fileName)
            .setDescription("Downloading file: $fileName")
            .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)

        request.addRequestHeader("Cookie", getCookiesForUrl(url) ?: "")
        request.addRequestHeader("Content-Type", mimeType ?: MimeTypes.application.OCTET_STREAM)
        request.addRequestHeader("User-Agent", getUserAgentJsonUseCase.invoke())
        request.addRequestHeader("Accept", MimeTypes.ANY)
        request.addRequestHeader("Accept-Encoding", "gzip,deflate,br")

        val downloadId = downloadManager.enqueue(request)

        CoroutineScope(Dispatchers.IO).launch {
            monitorDownloadProgress(
                downloadManager,
                downloadId,
                onProgressUpdate,
                onDownloadComplete as (String, String) -> Unit,
                onDownloadFailed,
            )
        }
    }

    private fun getFileExtensionFromMimeType(mimeType: String): String =
        ".${mimeType.convertMimeTypeToExtension()}"

    private fun extractFileNameFromContentDisposition(
        contentDisposition: String?,
        defaultUrl: String,
    ): String {
        return try {
            val filenameStarRegex = "filename\\*=([A-Za-z0-9\\-\\+\\.]+)'(.*?)'(.*)".toRegex()
            val matchStar = filenameStarRegex.find(contentDisposition ?: "")
            if (matchStar != null) {
                return URLDecoder.decode(matchStar.groupValues[3], "UTF-8")
            }
            val filenameRegex = "filename=\"([^\"]+)\"".toRegex()
            val match = filenameRegex.find(contentDisposition ?: "")
            match?.groups?.get(1)?.value ?: defaultUrl.substringAfterLast("/")
        } catch (e: Exception) {
            Timber.e("Failed to extract filename from Content-Disposition: ${e.message}")
            defaultUrl.substringAfterLast("/")
        }
    }

    @SuppressLint("Range")
    private suspend fun monitorDownloadProgress(
        downloadManager: DownloadManager,
        downloadId: Long,
        onProgressUpdate: (Int) -> Unit,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit,
        onDownloadFailed: (String) -> Unit,
    ) {
        var downloading = true
        while (downloading) {
            val query = DownloadManager.Query().setFilterById(downloadId)
            val cursor = downloadManager.query(query)
            if (cursor.moveToFirst()) {
                val bytesDownloaded = cursor.getInt(
                    cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR),
                )
                val bytesTotal = cursor.getInt(
                    cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES),
                )
                if (bytesTotal > 0) {
                    val progress = (bytesDownloaded * 100L / bytesTotal).toInt()
                    withContext(Dispatchers.Main) {
                        onProgressUpdate(progress)
                    }
                }
                val status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS))
                val reason = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_REASON))
                Timber.d("DownloadManager Status: $status, Reason: $reason")
                when (status) {
                    DownloadManager.STATUS_SUCCESSFUL -> {
                        downloading = false
                        handleDownloadSuccess(cursor, context, onDownloadComplete)
                    }

                    DownloadManager.STATUS_FAILED -> {
                        val reason =
                            cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_REASON))
                        val failureReason = getFailureReason(reason)
                        downloading = false
                        withContext(Dispatchers.Main) {
                            onDownloadFailed(
                                "Download Failed! Reason: $failureReason (ID: $downloadId)",
                            )
                        }
                    }

                    DownloadManager.STATUS_PENDING -> {
                        Timber.d("Download Status: Pending")
                    }

                    DownloadManager.STATUS_RUNNING -> {
                        val bytesDownloaded =
                            cursor.getInt(
                                cursor.getColumnIndex(
                                    DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR,
                                ),
                            )
                        val totalBytes =
                            cursor.getInt(
                                cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES),
                            )
                        val progress =
                            if (totalBytes > 0) (bytesDownloaded * 100L) / totalBytes else 0
                        Timber.d("Download Status: Running - $progress% downloaded")
                    }

                    DownloadManager.STATUS_PAUSED -> {
                        val reason =
                            cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_REASON))
                        val pauseReason = when (reason) {
                            DownloadManager.PAUSED_WAITING_TO_RETRY -> {
                                onDownloadFailed("Download Paused (ID: $downloadId)")
                            }

                            DownloadManager.PAUSED_WAITING_FOR_NETWORK -> "Waiting for network"
                            DownloadManager.PAUSED_QUEUED_FOR_WIFI -> "Queued for Wi-Fi"
                            DownloadManager.PAUSED_UNKNOWN -> "Unknown reason"
                            else -> "Other reason"
                        }
                        Timber.d("Download Status: Paused - $pauseReason")
                    }
                }
            }
            cursor.close()
            delay(1000)
        }
    }

    fun getFailureReason(reason: Int): String {
        return when (reason) {
            DownloadManager.ERROR_CANNOT_RESUME -> "Cannot resume download"
            DownloadManager.ERROR_DEVICE_NOT_FOUND -> "No storage device found"
            DownloadManager.ERROR_FILE_ALREADY_EXISTS -> "File already exists"
            DownloadManager.ERROR_FILE_ERROR -> "File system error"
            DownloadManager.ERROR_HTTP_DATA_ERROR -> "HTTP data error"
            DownloadManager.ERROR_INSUFFICIENT_SPACE -> "Insufficient storage space"
            DownloadManager.ERROR_TOO_MANY_REDIRECTS -> "Too many redirects"
            DownloadManager.ERROR_UNHANDLED_HTTP_CODE -> "Unhandled HTTP error"
            DownloadManager.ERROR_UNKNOWN -> "Unknown error"
            else -> "Unknown reason code: $reason"
        }
    }

    @SuppressLint("Range")
    private suspend fun handleDownloadSuccess(
        cursor: Cursor,
        context: Context,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit,
    ) {
        val uri = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI))
        val filePath =
            if (uri.startsWith("content://")) {
                getFilePathFromUri(context, Uri.parse(uri))
            } else {
                Uri.parse(uri).path
            }

        filePath?.let { processCsvFile(it, onDownloadComplete) }
    }

    private suspend fun processCsvFile(
        filePath: String,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit
    ) {
        val file = File(filePath)
        if (file.exists()) {
            val fileName = file.name
            withContext(Dispatchers.Main) {
                onDownloadComplete(filePath, fileName)
            }
        } else {
            Timber.e("File does not exist: $filePath")
        }
    }



    private fun getFilePathFromUri(context: Context, contentUri: Uri?): String? {
        var filePath: String? = null
        val projection = arrayOf(MediaStore.MediaColumns.DATA)
        val cursor = contentUri?.let {
            context.contentResolver.query(
                it,
                projection,
                null,
                null,
                null,
            )
        }
        cursor?.use {
            if (it.moveToFirst()) {
                val columnIndex = it.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA)
                filePath = it.getString(columnIndex)
            }
        }
        return filePath
    }
}

fun getCookiesForUrl(url: String): String? {
    val cookieManager = CookieManager.getInstance()
    cookieManager.setAcceptCookie(true)
    cookieManager.getCookie(url)
    return cookieManager.getCookie(url)
}
