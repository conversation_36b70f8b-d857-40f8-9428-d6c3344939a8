package jp.co.dhbk.connectbiz.usecase.home

import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPrefKey
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import timber.log.Timber
import javax.inject.Inject

class ClearUserSessionUseCase @Inject constructor(
    private val connectbizPreference: ConnectbizPreference,
) {
    operator fun invoke() {
        connectbizPreference.clearSession()
        Timber.d("BiometricSkippedUpdate ${connectbizPreference.isBiometricSkippedByUser()}")
        Timber.e("Login session cleared ${connectbizPreference.getUserLoggedIn()}")
    }
}

class UpdateLoginSessionUseCase @Inject constructor(
    private val connectbizPreference: ConnectbizPreference,
) {
    operator fun invoke(isLoggedIn: Boolean, idToken: String) {
        connectbizPreference.save(ConnectbizPrefKey.IS_USER_LOGGED_IN, isLoggedIn)
        connectbizPreference.saveSessionToken(idToken)
        Timber.e(
            "Login session updated to: ${connectbizPreference.getUserLoggedIn() &&
                !connectbizPreference.isSessionExpired()}",
        )
        // Timber.e(("get Id token ${connectbizPreference.getSessionToken()}"))
    }
}
