package jp.co.dhbk.connectbiz.usecase.home

import jp.co.dhbk.connectbiz.domain.repository.FileDownloadRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class DownloadFileUseCase @Inject constructor(
    private val repository: FileDownloadRepository,
) {
    suspend operator fun invoke(fileUrl: String, destinationPath: String): Flow<Int> {
        return repository.downloadFile(fileUrl, destinationPath)
    }
}
