package jp.co.dhbk.connectbiz.usecase.home

import android.webkit.CookieManager
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import timber.log.Timber
import javax.inject.Inject

class SessionCookieUseCase @Inject constructor(
    private val connectbizPreference: ConnectbizPreference,
) {

    // Save session cookies to SharedPreferences
    fun saveSessionCookies(url: String) {
        val cookieManager = CookieManager.getInstance()
        val cookies = cookieManager.getCookie(url)
        if (!cookies.isNullOrEmpty()) {
            connectbizPreference.setCookies(cookies)
            validateCookies(cookies)
        }
    }

    // Load session cookies from SharedPreferences into CookieManager
    fun loadSessionCookies(url: String): String {
        val cookies = connectbizPreference.getCookies()
        val cookieManager = CookieManager.getInstance()
        cookieManager.setAcceptCookie(true)
        cookies.let {
            cookieManager.setCookie(url, it)
            cookieManager.flush() // Ensures cookies are applied before WebView loads
        }
        return cookies
    }

    // Clear cookies on logout
    fun clearSessionCookies() {
        connectbizPreference.removeCookies()
        CookieManager.getInstance().removeAllCookies(null)
        CookieManager.getInstance().flush()
    }

    // Validate cookies to check user session
    private fun validateCookies(cookies: String) {
        Timber.d("Cookies: $cookies")

        // Check for .AspNetCore.Cookies
        if (cookies.contains(".AspNetCore.Cookies")) {
            val cookiePairs = cookies.split(";").associate {
                val (key, value) = it.split("=", limit = 2)
                key.trim() to value.trim()
            }

            val sessionCookie = cookiePairs[".AspNetCore.Cookies"]
            if (sessionCookie != null) {
                Timber.d("User session is valid: $sessionCookie.")
                if (sessionCookie == connectbizPreference.getSessionCookies()) {
                    Timber.d("User session is still valid: $sessionCookie.")
                } else {
                    Timber.d("User session is invalid: $sessionCookie.")
                    connectbizPreference.setSessionCookies(sessionCookie)
                }
                // User is authenticated, load main content or handle accordingly
            } else {
                Timber.d("Session is invalid or expired.")
                // Handle session expiration, e.g., redirect to login
                // Example: loadUrl("https://cnbdb2c01.b2clogin.com/...")
            }
        } else {
            Timber.d("No session cookie found.")
            // Handle case with no session cookie
            // Example: loadUrl("https://cnbdb2c01.b2clogin.com/...")
        }
    }
}
