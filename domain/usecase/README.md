# :domain:usecase

Repository のロジック等をカプセル化した UseCase を定義する。

UseCase は 1 つの `invoke` メソッドだけを定義する。実装例を以下に示す。

```kotlin
class GetFooUseCase @Inject constructor(fooRepository: FooRepository) {
    suspend operator fun invoke(): Result<Foo> = fooRepository.getFoo()
}
```

## 依存関係

```mermaid
graph TB
    domain_usecase[:domain:usecase]
    domain_model[:domain:model]
    domain_repository[:domain:repository]
    core_common[:core:common]
    app[:app]
    ui[:ui]
    domain_usecase --> core_common
    domain_usecase --> domain_model
    domain_usecase --> domain_repository
    app --> domain_usecase
    ui --> domain_usecase
```
