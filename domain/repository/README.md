# :domain:repository

Repository のインターフェースを定義する。

Repository はワンショットのメソッド（`suspend fun`） とストリーム API（`Flow`）を提供する。ワンショットのメソッドは主に `GET` や `POST` などの処理を行う。時間経過で変化する状態や、副作用などは `Flow` により公開する。実装例を以下に示す。

```kotlin
interface FooRepository {
    val fooState: Flow<Foo>
    suspend fun setFoo(foo: Foo)
}
```

## 依存関係

```mermaid
graph TB
    domain_repository[:domain:repository]
    domain_model[:domain:model]
    domain_usecase[:domain:usecase]
    core_common[:core:common]
    app[:app]
    data[:data]
    domain_repository --> core_common
    domain_repository --> domain_model
    domain_usecase --> domain_repository
    app --> domain_repository
    data --> domain_repository
```
