# :domain:model

ドメインモデルを定義する。

## 依存関係

```mermaid
graph TB
    domain_model[:domain:model]
    domain_repository[:domain:repository]
    domain_usecase[:domain:usecase]
    core_common[:core:common]
    app[:app]
    data[:data]
    ui[:ui]
    domain_model --> core_common
    domain_repository --> domain_model
    domain_usecase --> domain_model
    app --> domain_model
    data --> domain_model
    ui --> domain_model
```
