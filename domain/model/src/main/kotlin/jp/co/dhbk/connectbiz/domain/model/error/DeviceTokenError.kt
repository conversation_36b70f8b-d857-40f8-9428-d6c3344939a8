package jp.co.dhbk.connectbiz.domain.model.error

sealed class DeviceTokenError(val message: String, val statusCode: Int) {
    object InvalidInput : DeviceTokenError("Invalid input provided.", 401)
    object TokenExpired : DeviceTokenError("Token has expired.", 400)
    object ConflictUser : <PERSON><PERSON><PERSON>okenError("Conflict User Login.", 409)
    object NetworkError : DeviceTokenError("Network error occurred.", 500)
    object UnAuthorized : DeviceTokenError("Unauthorized.", 402)
    data class UnknownError(val errorMessage: String) : DeviceTokenError(errorMessage, 503)

    override fun toString(): String = message
}

class ApiException(val statusCode: Int, message: String) : Exception(message)
