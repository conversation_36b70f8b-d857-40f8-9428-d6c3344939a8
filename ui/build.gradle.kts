

plugins {
    alias(libs.plugins.buildlogic.android.library)
    alias(libs.plugins.kotlin.plugin.serialization)
    alias(libs.plugins.buildlogic.android.compose)
    alias(libs.plugins.buildlogic.android.hilt)
    alias(libs.plugins.buildlogic.roborazzi)
    alias(libs.plugins.buildlogic.retrofit)
    id("kotlin-parcelize")
}

android {
    namespace = "jp.co.dhbk.connectbiz.ui"
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            all {
                it.systemProperties["robolectric.pixelCopyRenderMode"] = "hardware"
            }
        }
    }
}

rob<PERSON>zzi {
    @OptIn(com.github.takahirom.roborazzi.ExperimentalRoborazziApi::class)
    generateComposePreviewRobolectricTests {
        enable = true
        packages = listOf("jp.co.dhbk.connectbiz.ui")
        includePrivatePreviews = true
        testerQualifiedClassName = "jp.co.dhbk.connectbiz.core.testing.PreviewTester"
        robolectricConfig =
            mapOf(
                "sdk" to "[33]",
                "qualifiers" to "RobolectricDeviceQualifiers.Pixel7",
            )
    }
}

dependencies {
    implementation(project(":core:common"))
    implementation(project(":domain:model"))
    implementation(project(":core:ui"))
    implementation(project(":core:analytics"))
    implementation(project(":domain:usecase"))
    implementation(libs.firebase.config.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(project(":data"))
    implementation(libs.androidx.ui.android)
    testImplementation(project(":core:testing"))
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.hilt.navigation.compose)
    implementation(libs.androidx.lifecycle.runtimeCompose)
    implementation(libs.androidx.lifecycle.viewmodelCompose)
    implementation(libs.firebase.messaging.ktx)
    implementation(libs.androidx.biometric)
    implementation(libs.androidx.appcompat)
    implementation(libs.google.code.gson)
    implementation(libs.accompanist.permissions)
    implementation(libs.androidx.browser)
    implementation(libs.androidx.foundation.android)
    implementation(libs.exoplayer.core)
    implementation(libs.exoplayer.ui)
}
