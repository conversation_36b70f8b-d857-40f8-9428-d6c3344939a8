package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun GlobalImageDisplay(
    @DrawableRes imageResId: Int, // Image resource ID
    contentDescription: String, // Content description for accessibility
    modifier: Modifier = Modifier, // Optional modifier for flexibility
    imageSize: Dp = 350.dp, // Dynamic size for the image (default: 350.dp)
    contentScale: ContentScale = ContentScale.Fit,
) {
    Image(
        painter = painterResource(id = imageResId),
        contentDescription = contentDescription,
        modifier =
        modifier
            .size(imageSize) // Use the dynamic image size
            .padding(bottom = 8.dp),
        contentScale = contentScale,
    )
}
