package jp.co.dhbk.connectbiz.ui.navigation.navgraph

import android.net.Uri
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.navigation.NavOptions
import jakarta.inject.Inject
import jp.co.dhbk.connectbiz.ui.navigation.NavInfo
import jp.co.dhbk.connectbiz.ui.navigation.NavManager
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.currentRoute
import timber.log.Timber

class Navigator
@Inject
constructor(
    private val navManager: NavManager,
) {

    // Loading state for showing CustomLoadingScreen during transitions
    val isLoading: MutableState<Boolean> = mutableStateOf(false)

    /**
     * Centralized navigation logic for handling all navigations with query parameters.
     *
     * @param route The route to navigate to.
     * @param queryParams Map of query parameters to be appended to the route.
     * @param screenName The name of the screen for analytics or logging purposes.
     * @param popUpToRoute The route to pop up to (default is Splash screen).
     * @param inclusive Whether the popUpTo route should be inclusive.
     */
    fun navigateWithQueryParam(
        route: String,
        queryParams: Map<String, String> = emptyMap(),
        screenName: String,
        popUpToRoute: String,
        inclusive: Boolean,
    ) {
        // Show loading screen
        isLoading.value = true

        // Build the full route with query parameters if any
        val fullRoute = buildRouteWithQueryParams(route, queryParams)
        currentRoute = fullRoute
        // Build NavOptions
        val navOptions =
            NavOptions.Builder()
                .setPopUpTo(popUpToRoute, inclusive)
                .build()
        // Navigate using NavManager
        navManager.navigate(
            NavInfo(id = fullRoute, navOption = navOptions),
            screenName = screenName,
        )
        isLoading.value = false
    }

    /**
     * Helper function to build a route with query parameters.
     *
     * @param route The base route.
     * @param queryParams The map of query parameters.
     * @return The full route with appended query parameters.
     */
    private fun buildRouteWithQueryParams(
        route: String,
        queryParams: Map<String, String>,
    ): String {
        if (queryParams.isEmpty()) return route

        val encodedParams =
            queryParams.map { (key, value) ->
                "$key=${Uri.encode(value)}"
            }.joinToString("&")
        Timber.e("Navigator Log: Building route: $route?$encodedParams")

        return "$route?$encodedParams"
    }

    /**
     * Centralized navigation method to handle navigation to any screen.
     *
     * @param route The route to navigate to.
     * @param screenName The screen name for analytics tracking.
     * @param popUpTo The route to pop up to (default: Splash screen).
     * @param inclusive Whether the pop-up-to route should be inclusive.
     */
    fun navigateTo(route: String, screenName: String, popUpTo: String, inclusive: Boolean) {
        // Show loading screen
        isLoading.value = true
        currentRoute = route
        val navOptions =
            NavOptions.Builder()
                .setPopUpTo(popUpTo, inclusive)
                .setLaunchSingleTop(true)
                .build()

        navManager.navigate(
            NavInfo(id = route, navOption = navOptions),
            screenName = screenName,
        )

        isLoading.value = false
    }

    fun navigateToBack() {
        val navigationStack = getNavigationStack() // Assuming this retrieves the stack
        val lastKnownRoute = navigationStack.lastOrNull { it.id != null }?.id
        Timber.e("lastKnownRoute $lastKnownRoute")
        navManager.goBack()
    }

    fun navigateToSplash() {
        navManager.navigate(NavInfo(id = Route.Welcome.SPLASH))
    }

    fun getNavigationStack(): MutableList<NavInfo> {
        return navManager.navigationStack
    }

    fun navigateToLast() {
        return navManager.navigateBack()
    }

    fun navigationStackClear() {
        navManager.navigationStack.clear()
        Timber.e("Navigation Stack Clear ${navManager.navigationStack.size}")
    }

    /**
     * Navigate to the last known route in the navigation stack.
     * If the stack is empty or no valid route is found, navigate to a default route (e.g., Home).
     * Ensures the splash screen is not revisited multiple times.
     */
    fun navigateToLastKnownRoute(defaultRoute: String = Route.Home.HOME, homeUrl: String? = null) {
        val navigationStack = getNavigationStack()
        val lastKnownRoute = navigationStack.lastOrNull { it.id != Route.Welcome.SPLASH }?.id

        if (lastKnownRoute != null) {
            if (lastKnownRoute == Route.Home.HOME && homeUrl != null) {
                Timber.d("Navigating to Home with URL: $homeUrl")
                val queryParams = mapOf("url" to homeUrl)
                val fullRoute = buildRouteWithQueryParams(Route.Home.HOME, queryParams)
                navManager.navigate(
                    NavInfo(
                        id = fullRoute,
                        navOption = createNavOptions(Route.Welcome.SPLASH, true),
                    ),
                )
            } else {
                Timber.d("Navigating to last known route: $lastKnownRoute")
                navManager.navigate(
                    NavInfo(
                        id = lastKnownRoute,
                        navOption = createNavOptions(
                            Route.Welcome
                                .SPLASH,
                            false,
                        ),
                    ),
                )
            }
        } else {
            Timber.d("No last known route found, navigating to default route: $defaultRoute")
            navManager.navigate(
                NavInfo(
                    id = defaultRoute,
                    navOption = createNavOptions(Route.Welcome.SPLASH, false),
                ),
            )
        }
    }

    /**
     * Helper to create navigation options for popping up to a route.
     */
    private fun createNavOptions(popUpToRoute: String, inclusive: Boolean): NavOptions {
        return NavOptions.Builder()
            .setPopUpTo(popUpToRoute, inclusive)
            .build()
    }

    fun updateQueryParamInStack(route: String, newQueryParams: Map<String, String>) {
        val navigationStack = getNavigationStack()
        val indexToUpdate = navigationStack.indexOfFirst { it.id?.startsWith(route) == true }

        if (indexToUpdate != -1) {
            // Extract existing route without query parameters
            val baseRoute = navigationStack[indexToUpdate].id?.split("?")?.get(0) ?: route

            // Build the updated route with new query parameters
            val updatedRoute = buildRouteWithQueryParams(baseRoute, newQueryParams)

            // Create a new NavInfo with updated route
            val updatedNavInfo = navigationStack[indexToUpdate].copy(id = updatedRoute)

            // Update the stack with the new NavInfo
            navigationStack[indexToUpdate] = updatedNavInfo

            Timber.d("Updated navigation stack with new query params: $updatedRoute")
        } else {
            Timber.e("Route not found in stack for updating: $route")
        }
    }
}
