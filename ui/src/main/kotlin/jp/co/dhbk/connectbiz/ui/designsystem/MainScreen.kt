package jp.co.dhbk.connectbiz.ui.designsystem

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.material3.SnackbarHost
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import jp.co.dhbk.connectbiz.core.ui.utils.rememberSnackbarHostState
import jp.co.dhbk.connectbiz.ui.navigation.NavManager
import jp.co.dhbk.connectbiz.ui.navigation.NavStateViewModel
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.AppNavGraph
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
fun MainScreen(
    navManager: NavManager,
    navController: NavHostController,
    navigator: Navigator,
    modifier: Modifier = Modifier,
    navStateViewModel: NavStateViewModel = hiltViewModel(),
) {
    val snackbarHostState = rememberSnackbarHostState()
    GlobalSnackbarManager.init(snackbarHostState)

    // Listen for navigation events and update the current route in ViewModel
    LaunchedEffect(navController) {
        snapshotFlow { navController.currentBackStackEntry?.destination?.route }
            .collect { route ->
                navStateViewModel.updateCurrentRoute(route ?: "")
            }
    }

    TemplateTheme {
        Box(modifier = modifier.fillMaxSize()) {
            // Main App Navigation Content
            AppNavGraph(
                navController = navController,
                navigator = navigator,
                modifier = Modifier.fillMaxSize(),
            )

            // SnackbarHost layered on top without Scaffold padding
            SnackbarHost(
                hostState = snackbarHostState,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .navigationBarsPadding(),
            )
        }
    }
    // Listen for navigation changes from NavManager
    val navInfo = navManager.routeInfo.collectAsState().value // Explicitly access .value

    // React to navigation events
    LaunchedEffect(navInfo) {
        navInfo.id?.let { route ->
            if (route == Route.Control.BACK) {
                navController.navigateUp() // Navigate back
                navManager.navigate(null) // Clear navigation state in NavManager
            } else {
                // Navigate to the specified route
                navController.navigate(route, navOptions = navInfo.navOption)
                navManager.navigate(null) // Clear navigation state in NavManager
            }
        }
    }
}
