package jp.co.dhbk.connectbiz.ui.destinations.licenses

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class LicenseModule {

    @Binds
    @Singleton
    abstract fun bindLicenseRepository(
        defaultLicenseRepository: DefaultLicenseRepository,
    ): LicenseRepository
}
