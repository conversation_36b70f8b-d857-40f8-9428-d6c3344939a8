package jp.co.dhbk.connectbiz.ui.destinations.maintenance

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.analytics.ConnectbizAnalyticConstant
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.domain.model.AppMaintenanceAndUpdateInfoData
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import jp.co.dhbk.connectbiz.usecase.GetAppMaintenanceAndUpdateInfoUseCase
import jp.co.dhbk.connectbiz.usecase.RefreshRemoteConfigUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class MaintenanceViewModel
@Inject
constructor(
    val navigator: Navigator,
    private val getAppMaintenanceAndUpdateInfoUseCase: GetAppMaintenanceAndUpdateInfoUseCase,
    private val refreshRemoteConfigUseCase: RefreshRemoteConfigUseCase,
) : ViewModel() {
    private val maintenanceState =
        MutableStateFlow<LoadState<AppMaintenanceAndUpdateInfoData>>(LoadState.Initial)
    val maintenanceStateFlow: StateFlow<LoadState<AppMaintenanceAndUpdateInfoData>> =
        maintenanceState

    init {
        fetchMaintenanceData()
    }

    fun fetchMaintenanceData() {
        viewModelScope.launch {
            maintenanceState.value = LoadState.Loading() // Set loading state
            val result = getAppMaintenanceAndUpdateInfoUseCase()
            maintenanceState.value = if (result.isSuccess) {
                LoadState.Success(result.getOrNull()!!)
            } else {
                Timber.e("Data Load Failed")
                LoadState.Failure(
                    (
                        result.exceptionOrNull()
                            ?: Exception("Unknown error")
                        ) as Exception,
                )
            }
        }
    }

    fun refreshRemoteConfig() {
        viewModelScope.launch {
            refreshRemoteConfigUseCase()
            fetchMaintenanceData() // Re-fetch the maintenance data after refreshing
        }
    }

    fun navigateToSplashScreen() {
        viewModelScope.launch {
            navigator.navigationStackClear()
            navigator.navigateTo(
                route = Route.Welcome.SPLASH,
                screenName = ConnectbizAnalyticConstant.Page.SPLASH,
                popUpTo = Route.Welcome.MAINTENANCE,
                inclusive = true,
            )
        }
    }

    fun updateErrorScreen() {
        navigator.navigateTo(
            route = Route.Welcome.ERROR,
            screenName = ConnectbizAnalyticConstant.Page.ERROR,
            popUpTo = Route.Home.HOME,
            inclusive = true,
        )
    }
}
