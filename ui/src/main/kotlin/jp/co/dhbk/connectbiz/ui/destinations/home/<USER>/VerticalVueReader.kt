package jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VerticalVueReaderState
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VuePageState

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun VerticalVueReader(
    verticalVueReaderState: VerticalVueReaderState,
    modifier: Modifier = Modifier,
    contentModifier: Modifier = Modifier,
) {
    val vueRenderer = verticalVueReaderState.vueRenderer
    val currentPage = verticalVueReaderState.currentPage
    if (verticalVueReaderState.cache != 0) {
        LaunchedEffect(key1 = currentPage, block = {
            vueRenderer?.loadWithCache(currentPage)
        })
    }
    if (vueRenderer != null) {
        VerticalPager(
            modifier = modifier.fillMaxWidth(),
            userScrollEnabled = false,
            state = verticalVueReaderState.pagerState,
        ) { idx ->
            val pageContent by vueRenderer.pageLists[idx].stateFlow.collectAsState()
            if (verticalVueReaderState.cache == 0) {
                DisposableEffect(key1 = Unit) {
                    vueRenderer.pageLists[idx].load()
                    onDispose {
                        vueRenderer.pageLists[idx].recycle()
                    }
                }
            }
            AnimatedContent(targetState = pageContent, label = "") {
                when (it) {
                    is VuePageState.BlankState -> {
                        BlankPage(
                            modifier = contentModifier,
                            width = verticalVueReaderState.containerSize!!.width,
                            height = verticalVueReaderState.containerSize!!.height,
                        )
                    }

                    is VuePageState.LoadedState -> {
                        val zoomableModifier = remember { ZoomableModifier() }
                        /*Image(
                            modifier = contentModifier
                                .clipToBounds()
                                .pinchToZoomAndDrag(),
                            bitmap = it.content.asImageBitmap(),
                            contentDescription = "",
                        )*/
                        Image(
                            modifier = contentModifier
                                .clipToBounds()
                                .graphicsLayer(
                                    scaleX = zoomableModifier.zoom.floatValue,
                                    scaleY = zoomableModifier.zoom.floatValue,
                                    translationX = zoomableModifier.offsetX.floatValue,
                                    translationY = zoomableModifier.offsetY.floatValue,
                                    rotationZ = zoomableModifier.angle.floatValue,
                                )
                                .combinedClickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = null,
                                    onClick = {
                                        // Handle single tap
                                    },
                                    onDoubleClick = {
                                        // Handle double tap (e.g., toggle zoom)
                                        zoomableModifier.zoom.floatValue =
                                            if (zoomableModifier.zoom.floatValue > 1f) {
                                                1f
                                            } else {
                                                3f
                                            }
                                    },
                                )
                                .pinchToZoomAndDrag(zoomableModifier),
                            bitmap = it.content.asImageBitmap(),
                            contentDescription = "Zoomable Image",
                        )
                    }
                }
            }
        }
    }
}
