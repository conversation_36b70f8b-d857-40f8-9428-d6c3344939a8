package jp.co.dhbk.connectbiz.ui.designsystem

import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import jp.co.dhbk.connectbiz.core.ui.utils.showErrorSnackbar
import jp.co.dhbk.connectbiz.core.ui.utils.showNormalSnackbar

object GlobalSnackbarManager {
    private var snackbarHostState = SnackbarHostState()

    fun init(snackbarHostState: SnackbarHostState) {
        this.snackbarHostState = snackbarHostState
    }

    suspend fun showNormalSnackbar(message: String): SnackbarResult =
        snackbarHostState.showNormalSnackbar(message)

    suspend fun showErrorSnackbar(message: String): SnackbarResult =
        snackbarHostState.showErrorSnackbar(message)
}
