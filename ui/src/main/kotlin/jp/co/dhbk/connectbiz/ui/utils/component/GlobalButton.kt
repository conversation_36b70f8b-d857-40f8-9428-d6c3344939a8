package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.ButtonBackgroundPrimary

@Composable
fun GlobalButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColor: Color = ButtonBackgroundPrimary,
    textColor: Color = Color.White,
    enabled: Boolean = true,
    fontWeight: FontWeight = FontWeight.Bold,
    cornerRadius: Dp = 8.dp,
) {
    Button(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(backgroundColor),
        enabled = enabled,
        shape = RoundedCornerShape(cornerRadius), // Set the corner radius
    ) {
        Text(
            text = text,
            modifier = Modifier.align(Alignment.CenterVertically),
            color = textColor,
            fontWeight = fontWeight,
        )
    }
}
