package jp.co.dhbk.connectbiz.ui.utils.biometricAuth
import android.content.Context
import android.content.Intent
import android.provider.Settings
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadStateFlow
import jp.co.dhbk.connectbiz.core.common.loadstate.asStateFlow
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationStarted
import jp.co.dhbk.connectbiz.usecase.biometricusecase.BiometricAuthenticationUseCase
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class BiometricViewModel @Inject constructor(
    val connectbizPreference: ConnectbizPreference,
    private val biometricAuthenticationUseCase: BiometricAuthenticationUseCase,
) : ViewModel() {

    private val _loadStateFlow = LoadStateFlow<BiometricState>()
    val loadStateFlow: StateFlow<LoadState<BiometricState>> = _loadStateFlow.asStateFlow()
    var isInitialized = false

    val authResult: StateFlow<BiometricAuthResult> =
        biometricAuthenticationUseCase.authResult.stateIn(
            viewModelScope,
            SharingStarted.Lazily,
            BiometricAuthResult.Loading,
        )

    fun initialize() {
        if (!isInitialized) {
            isInitialized = true
            GlobalAppState.isCustomTabOpened = false
            checkBiometricStatus()
        } else {
            Timber.d("Biometric already Initalized $isInitialized")
        }
    }

    fun checkBiometricStatus() {
        viewModelScope.launch {
            val status = canAuthenticate()
            val isBiometricEnabled = connectbizPreference.isBiometricEnabled()
            val isBiometricSkipped = connectbizPreference.isBiometricSkippedByUser()
            Timber.d("Biometric Status ${isBiometricEnabled && isBiometricSkipped}")
            _loadStateFlow.load {
                // Compute the new state based on the current status
                val newState = when {
                    !isBiometricSkipped && !isBiometricEnabled -> {
                        BiometricState(
                            isPopupVisible = true,
                            isToggleEnabled = false,
                        )
                    }
                    status == AuthenticationStatus.DEVICE_NOT_SECURE -> {
                        BiometricState(
                            isPopupVisible = true,
                            isToggleEnabled = false,
                        )
                    }
                    isBiometricEnabled && !isBiometricSkipped &&
                        (
                            status == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED ||
                                status ==
                                AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
                            )
                    -> {
                        BiometricState(
                            isPopupVisible = false,
                            isToggleEnabled = true,
                        )
                    }
                    else -> {
                        BiometricState(
                            isPopupVisible = false,
                            isToggleEnabled = false,
                        )
                    }
                }
                // Update the load state with the new state
                Result.success(newState)
            }
        }
    }

    fun authenticate(activity: FragmentActivity, screenType: ScreenType) {
        viewModelScope.launch {
            viewModelScope.launch {
                isAuthenticationStarted = true
                val delayMillis: Long = 30000
                if (!canRetry(delayMillis)) {
                    val remainingDelay = getRemainingDelay(delayMillis)
                    val remainingDelaySeconds = remainingDelay / 1000
                    Timber.d(
                        "Retry not allowed yet. Remaining delay:$remainingDelaySeconds Seconds",
                    )
                    updateState {
                        it.copy(
                            isAuthenticationCancel = remainingDelaySeconds,
                            authenticationErrorMessage = "間違えた回数が上限を超えました。 " +
                                "$remainingDelaySeconds 秒後にもう一度お試しください。",
                        )
                    }
                } else {
                    Timber.e("Before update Update Popup State ${_loadStateFlow.value}")
                    updateState {
                        it.copy(
                            isAuthenticationCancel = 0,
                            isAuthenticationFailedPopup = false,
                            isAuthenticationRetryPopup = false,
                            isToggleEnabled = connectbizPreference.isBiometricEnabled(),
                        )
                    }
                    delay(300)
                    Timber.e("After update Popup State ${_loadStateFlow.value}")
                    biometricAuthenticationUseCase.execute(activity, screenType)
                    handleAuthResult()
                }
            }
        }
    }

    fun canAuthenticate(): AuthenticationStatus {
        return biometricAuthenticationUseCase.canAuthenticate(ScreenType.HOME)
    }

    // retry update
    fun retryAuthentication(isShow: Boolean) {
        viewModelScope.launch {
            updateState {
                it.copy(isAuthenticationRetryPopup = isShow)
            }
        }
    }

    // Authentication Failed update
    fun updateAuthenticationFailedStatus() {
        viewModelScope.launch {
            connectbizPreference.setRetryTimestamp()
            Timber.e("isAuthenticationFailedPopup Before ${_loadStateFlow.value}")
            updateState {
                it.copy(
                    isAuthenticationFailedPopup = true,
                    isAuthenticationRetryPopup = false,
                )
            }
            Result.success(BiometricState(isAuthenticationFailedPopup = true))
            Timber.e("isAuthenticationFailedPopup Before ${_loadStateFlow.value}")
        }
    }

    // Open settings update
    fun onUpdateUISetting(uiUpdate: Boolean) {
        viewModelScope.launch {
            updateState {
                connectbizPreference.setBiometricEnabled(false)
                connectbizPreference.setBiometricSkippedByUser(false)
                it.copy(isSettingsPopupVisible = uiUpdate)
            }
            Result.success(BiometricState(isSettingsPopupVisible = uiUpdate))
        }
    }

    fun onToggleChange(newState: Boolean) {
        viewModelScope.launch {
            updateState {
                connectbizPreference.setBiometricEnabled(newState)
                connectbizPreference.setBiometricSkippedByUser(!newState)
                it.copy(isToggleEnabled = connectbizPreference.isBiometricEnabled())
            }
        }
    }

    fun navigateToSystemSettings(context: Context) {
        onSettingsUpdate(false)
        isInitialized = false
        onToggleChange(connectbizPreference.isBiometricEnabled())
        GlobalAppState.isCustomTabOpened = true
        val intent = Intent(Settings.ACTION_SETTINGS)
        context.startActivity(intent)
    }

    fun updateBiometricStatus(isSkippedByUser: Boolean) {
        viewModelScope.launch {
            updateState {
                connectbizPreference.setBiometricSkippedByUser(isSkippedByUser)
                it.copy(
                    isPopupVisible = !isSkippedByUser,
                )
            }
        }
    }

    fun onUpdatePopUpState(isPopup: Boolean) {
        viewModelScope.launch {
            updateState {
                it.copy(
                    isAuthenticationRetryPopup = false,
                    isPopupVisible = isPopup,
                    isBiometricPopupState = isPopup,
                )
            }
        }
    }
    private fun updateState(update: (BiometricState) -> BiometricState) {
        viewModelScope.launch {
            _loadStateFlow.load {
                val currentState = (
                    _loadStateFlow.value as?
                        LoadState.Success
                    )?.value ?: BiometricState()
                Result.success(update(currentState))
            }
        }
    }

    fun updateBiometricEnableStatus(isEnable: Boolean) {
        viewModelScope.launch {
            updateState {
                connectbizPreference.setBiometricEnabled(isEnable)
                connectbizPreference.setBiometricSkippedByUser(!isEnable)
                it.copy(
                    isPopupVisible = !isEnable,
                    isToggleEnabled = isEnable,
                )
            }
        }
    }

    fun updateAuthenticationStatus() {
        isAuthenticationStarted = false
    }

    fun biometricEnableStatus(): Boolean {
        return !connectbizPreference.isBiometricEnabled() && connectbizPreference
            .isBiometricSkippedByUser()
    }

    fun onBiometricDisablePopup(uiUpdate: Boolean) {
        viewModelScope.launch {
            updateState { it.copy(isBiometricDisablePopup = true) }
        }
        Result.success(BiometricState(isSettingsPopupVisible = uiUpdate))
    }

    fun onBiometricUnAvailablePopup(uiUpdate: Boolean) {
        viewModelScope.launch {
            updateState { it.copy(isBiometricUnAvailable = true) }
        }
        Result.success(BiometricState(isBiometricUnAvailable = uiUpdate))
    }

    fun handleAuthResult() {
        viewModelScope.launch {
            authResult.collect { result ->
                when (result) {
                    is BiometricAuthResult.Success -> {
                        Timber.e("Auth result Success!")
                    }

                    is BiometricAuthResult.Failure -> {
                        Timber.e("Auth result Failure! ${result.error.biometricStatus.code}")
                    }
                    BiometricAuthResult.Loading -> {
                    }
                }
            }
        }
    }

    fun onSettingsUpdate(bool: Boolean) {
        viewModelScope.launch {
            updateState {
                it.copy(
                    isSettingsPopupVisible = bool,
                    isPopupVisible = bool,
                )
            }
        }
        Result.success(BiometricState(isSettingsPopupVisible = bool))
    }

    fun canRetry(delayMillis: Long): Boolean {
        val retryTimestamp = connectbizPreference.getRetryTimestamp()
        val currentTime = System.currentTimeMillis()
        return (currentTime - retryTimestamp) >= delayMillis
    }

    fun getRemainingDelay(delayMillis: Long): Long {
        val retryTimestamp = connectbizPreference.getRetryTimestamp()
        val currentTime = System.currentTimeMillis()
        val elapsed = currentTime - retryTimestamp
        return if (elapsed >= delayMillis) 0 else delayMillis - elapsed
    }

    fun onToggleStateRestore(attemptCount: Int) {
        onToggleChange(connectbizPreference.isBiometricEnabled())
        if (attemptCount == 3) {
            updateState {
                it.copy(
                    isAuthenticationRetryPopup = true,
                    isAuthenticationFailedPopup = false,
                )
            }
        } else if (attemptCount == 5) {
            updateState {
                it.copy(
                    isAuthenticationRetryPopup = false,
                    isAuthenticationFailedPopup = true,
                )
            }
        } else {
            Timber.e("retry popup update")
            retryAuthentication(false)
        }
    }

    fun onUpdateBiometricPopup(bool: Boolean) {
        updateState {
            it.copy(isPopupVisible = bool)
        }
    }

    data class BiometricState(
        val isPopupVisible: Boolean = false,
        val isSettingsPopupVisible: Boolean = false,
        val isToggleEnabled: Boolean = false,
        val isLoading: Boolean = false,
        val successMessage: String? = null,
        val errorMessage: String? = null,
        val isAuthenticationRetryPopup: Boolean = false,
        val isAuthenticationFailedPopup: Boolean = false,
        val isBiometricDisablePopup: Boolean = false,
        val userAction: Boolean = false,
        val isBiometricUnAvailable: Boolean = false,
        val isBiometricPopupState: Boolean = false,
        val isAuthenticationCancel: Long = 0,
        val authenticationErrorMessage: String = "",
    )
}
