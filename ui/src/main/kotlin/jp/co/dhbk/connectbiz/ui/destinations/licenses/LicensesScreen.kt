package jp.co.dhbk.connectbiz.ui.destinations.licenses

import android.content.Intent
import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import jp.co.dhbk.connectbiz.core.common.library.Library
import jp.co.dhbk.connectbiz.core.common.library.License
import jp.co.dhbk.connectbiz.core.ui.modifier.ifTrue
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTopAppBar
import java.net.URL

/**
 * ライセンス画面
 */
@Composable
internal fun LicensesScreen(licenseViewModel: LicenseViewModel = hiltViewModel()) {
    val libraries by licenseViewModel.librariesFlow.collectAsState(initial = emptyList())
    val dialogState by licenseViewModel.dialogStateFlow.collectAsState(initial = null)
    val context = LocalContext.current

    BackHandler {
        licenseViewModel.goBack()
    }

    LicensesScreen(
        libraries = libraries,
        onUrlClick = { url ->
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url.toString()))
            context.startActivity(intent)
        },
        onNavigateBack = licenseViewModel::goBack,
        dialogState = dialogState,
        onDialogDismiss = licenseViewModel::hideLicenseDialog,
        onShowDialog = licenseViewModel::showLicenseDialog,
    )
}

/**
 * ライセンス画面
 *
 * @param libraries [List]<[Library]>
 * @param onUrlClick URL クリック時に呼ばれる
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LicensesScreen(
    libraries: List<Library>,
    onUrlClick: (URL) -> Unit,
    onNavigateBack: () -> Unit,
    dialogState: LicenseContentDialogState.State?,
    onDialogDismiss: () -> Unit,
    onShowDialog: (String, String) -> Unit,
) {
    Scaffold(
        containerColor = TemplateColors.BackgroundLight,
        topBar = {
            GlobalTopAppBar(
                text = stringResource(id = R.string.licence),
                onNavigateBack = onNavigateBack,
            )
        },
    ) { padding ->
        LazyColumn(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(top = padding.calculateTopPadding()),
            contentPadding = WindowInsets.navigationBars.asPaddingValues(),
        ) {
            items(
                items = libraries,
                key = { it.id },
                contentType = { "Library + HorizontalDivider" },
            ) { library ->
                Library(
                    library = library,
                    onLicenseClick = { license ->
                        val content = license.content
                        val url = license.url
                        if (content != null) {
                            onShowDialog(license.name, content)
                        } else if (url != null) {
                            onUrlClick(url)
                        }
                    },
                    onUrlClick = onUrlClick,
                    modifier = Modifier.padding(16.dp),
                )
                HorizontalDivider(
                    color = TemplateColors.DividerColor,
                    thickness = 0.5.dp,
                )
            }
        }
    }
    dialogState?.let {
        LicenseContentDialog(
            state = LicenseContentDialogState(it),
            onDismissRequest = onDialogDismiss,
        )
    }
}

/**
 * ライブラリ表示
 *
 * @param library [Library]
 * @param onLicenseClick ライセンスクリック時に呼ばれる
 * @param onUrlClick URL クリック時に呼ばれる
 * @param modifier [Modifier]
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun Library(
    library: Library,
    onLicenseClick: (License) -> Unit,
    onUrlClick: (URL) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Text(
            text = library.name,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            lineHeight = 24.sp,
            color = TemplateColors.TextPrimaryLight,
        )
        if (library.authors.isNotEmpty()) {
            Spacer(modifier = Modifier.height(4.dp))
            val separator = stringResource(id = R.string.licenses_author_separator)
            val authors =
                remember(library.authors) {
                    library.authors.joinToString(separator = separator)
                }
            Text(
                text = authors,
                fontSize = 12.sp,
                color = TemplateColors.TextSecondary,
            )
        }
        if (library.licenses.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                library.licenses.forEach { license ->
                    val enabled = license.content != null || license.url != null
                    val shape = RoundedCornerShape(4.dp)
                    Box(
                        modifier =
                        Modifier
                            .clip(shape)
                            .background(
                                color =
                                if (enabled) {
                                    TemplateColors.ButtonBackgroundPrimary
                                } else {
                                    TemplateColors.ButtonBackgroundSecondary
                                },
                            )
                            .border(
                                width = if (enabled) 0.dp else 1.dp,
                                color = MaterialTheme.colorScheme.outline,
                                shape = shape,
                            )
                            .ifTrue(enabled) {
                                clickable(
                                    role = Role.Button,
                                    onClick = { onLicenseClick(license) },
                                )
                            }
                            .padding(horizontal = 12.dp, vertical = 8.dp),
                    ) {
                        Text(
                            text = license.name,
                            fontSize = 12.sp,
                            color =
                            if (enabled) {
                                TemplateColors.ButtonTextPrimary
                            } else {
                                TemplateColors.ButtonDisabled
                            },
                        )
                    }
                }
            }
        }
        if (library.url != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = library.url.toString(),
                fontSize = 12.sp,
                lineHeight = 18.sp,
                textDecoration = TextDecoration.Underline,
                color = TemplateColors.TextSecondary,
                modifier =
                Modifier
                    .clickable(onClick = { library.url?.let(onUrlClick) }),
            )
        }
    }
}

/**
 * ライセンス原文表示
 *
 * 内部で [state] を変更して非表示に切り替える
 *
 * スクロール対応のために、[Dialogs – Material Design 3](https://m3.material.io/components/dialogs/guidelines)
 * を参考に [Dialog] で独自実装している
 *
 * @param state [LicenseContentDialogState]
 */
@Composable
private fun LicenseContentDialog(state: LicenseContentDialogState, onDismissRequest: () -> Unit) {
    val currentState = state.currentState ?: return
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(usePlatformDefaultWidth = false),
    ) {
        Surface(
            modifier =
            Modifier
                .widthIn(max = LocalConfiguration.current.screenWidthDp.dp - 48.dp)
                .heightIn(max = LocalConfiguration.current.screenHeightDp.dp - 128.dp),
            shape = MaterialTheme.shapes.extraLarge,
            color = MaterialTheme.colorScheme.surface,
            tonalElevation = 6.dp,
        ) {
            Column {
                Spacer(modifier = Modifier.height(24.dp))
                Text(
                    text = currentState.name,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier =
                    Modifier
                        .padding(horizontal = 24.dp)
                        .align(Alignment.Start),
                )
                Spacer(modifier = Modifier.height(24.dp))
                val scrollState = rememberScrollState()
                Text(
                    text = currentState.content,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier =
                    Modifier
                        .weight(weight = 1f, fill = false)
                        .verticalScroll(scrollState)
                        .padding(horizontal = 24.dp, vertical = 16.dp)
                        .align(Alignment.Start),
                )
                Spacer(modifier = Modifier.height(24.dp))
                TextButton(
                    onClick = onDismissRequest,
                    modifier =
                    Modifier
                        .padding(horizontal = 24.dp)
                        .align(Alignment.End),
                ) {
                    Text(
                        text = stringResource(id = R.string.licenses_dialog_close),
                        style = MaterialTheme.typography.labelLarge,
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}
