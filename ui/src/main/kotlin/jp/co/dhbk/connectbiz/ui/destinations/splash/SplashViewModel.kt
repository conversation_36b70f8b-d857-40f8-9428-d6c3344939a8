package jp.co.dhbk.connectbiz.ui.destinations.splash

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import androidx.biometric.BiometricPrompt
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import jp.co.dhbk.analytics.ConnectbizAnalyticConstant
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import jp.co.dhbk.connectbiz.data.network.DynamicConfigService
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.currentRoute
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationRequired
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationStarted
import jp.co.dhbk.connectbiz.usecase.GetAppMaintenanceAndUpdateInfoUseCase
import jp.co.dhbk.connectbiz.usecase.RefreshRemoteConfigUseCase
import jp.co.dhbk.connectbiz.usecase.biometricusecase.BiometricAuthenticationUseCase
import jp.co.dhbk.connectbiz.usecase.home.ClearUserSessionUseCase
import jp.co.dhbk.connectbiz.usecase.notificationusecase.NotificationUseCase
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.toString

@HiltViewModel
class SplashViewModel @Inject constructor(
    val navigator: Navigator,
    private val connectbizPreference: ConnectbizPreference,
    @ApplicationContext private val context: Context,
    private val apiConfig: ApiConfig,
    private val dynamicConfigService: DynamicConfigService,
    private val getAppMaintenanceAndUpdateInfoUseCase: GetAppMaintenanceAndUpdateInfoUseCase,
    private val refreshRemoteConfigUseCase: RefreshRemoteConfigUseCase,
    private val biometricAuthenticationUseCase: BiometricAuthenticationUseCase,
    private val notificationUseCase: NotificationUseCase,
    private val sessionUseCase: ClearUserSessionUseCase,
) : ViewModel() {

    private val _uiState = MutableStateFlow(SplashUiState())
    val uiState: StateFlow<SplashUiState> = _uiState
    private val _navigationEvents = MutableSharedFlow<NavigationEvent>(replay = 0)
    val navigationEvents: SharedFlow<NavigationEvent> = _navigationEvents

    val authResult: StateFlow<BiometricAuthResult> =
        biometricAuthenticationUseCase.authResult.stateIn(
            viewModelScope,
            SharingStarted.Lazily,
            BiometricAuthResult.Loading,
        )

    private var notificationHandled = false

    fun initialize() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            _uiState.update { it.copy(isTutorialShows = connectbizPreference.isTutorialShown()) }
            _uiState.update { it.copy(isLogoAnimationDone = true) }
            if (!isNetworkAvailable()) {
                _uiState.update { it.copy(isLoading = false) }
                _navigationEvents.emit(NavigationEvent.NavigateToErrorScreen)
                return@launch
            }
            notificationUseCase.clearNotificationData()
        }
    }

    fun refreshRemoteConfig(activity: FragmentActivity) {
        viewModelScope.launch {
            refreshRemoteConfigUseCase()
            val result = getAppMaintenanceAndUpdateInfoUseCase()
            result.onSuccess { maintenanceConfig ->
                connectbizPreference.setLastVistedURL("")
                // Update DynamicConfig with values from Remote Config
                dynamicConfigService.updateDynamicConfig(
                    signInWithMfa = maintenanceConfig.signInWithMfa,
                    signInWithoutMfa = maintenanceConfig.signInWithoutMfa,
                    apiBaseURL = maintenanceConfig.apiBaseURL,
                )
                when {
                    maintenanceConfig.isMaintenance -> {
                        _navigationEvents.emit(
                            NavigationEvent.NavigateToMaintenanceScreen(maintenanceConfig.message),
                        )
                    }
                    maintenanceConfig.isForceUpdateRequired -> {
                        _navigationEvents.emit(
                            NavigationEvent.NavigateToForceUpdateScreen(
                                maintenanceConfig.updateMessage,
                                maintenanceConfig.destinationLink,
                            ),
                        )
                    }
                    else -> checkPushNotification(activity)
                }
            }.onFailure {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = "Failed to fetch configuration",
                    )
                }
            }
        }
    }

    private fun checkPushNotification(activity: FragmentActivity) {
        if (connectbizPreference.isTutorialShown()) {
            if (notificationHandled) return
            notificationHandled = true
            viewModelScope.launch {
                notificationUseCase.loadFromPreferences()
                val (url, handle) = notificationUseCase.getNotificationData()

                if (handle == true && url.toString().contains("Safety/Reply")) {
                    isAuthenticationRequired = true
                    loadNotifications(url.toString())
                } else {
                    checkBiometricAuthentication(activity)
                }
                notificationHandled = false // Reset after processing
            }
        } else {
            viewModelScope.launch {
                _navigationEvents.emit(NavigationEvent.NavigateToTutorialScreen)
            }
        }
    }

    fun canAuthenticate(): AuthenticationStatus {
        return biometricAuthenticationUseCase.canAuthenticate(ScreenType.SPLASH)
    }

    private fun checkBiometricAuthentication(activity: FragmentActivity) {
        val isAuthenticationAvailable = canAuthenticate()

        if (connectbizPreference.isBiometricEnabled() &&
            (
                isAuthenticationAvailable == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED ||
                    isAuthenticationAvailable == AuthenticationStatus
                        .BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
                )
        ) {
            if (isAuthenticationStarted) {
                Timber.e("Cannot start authentication: Authentication in progress")
                return
            }

            isAuthenticationStarted = true
            Timber.d("Starting biometric authentication.")
            authenticate(activity, ScreenType.SPLASH)
        } else {
            // Handle fallback if biometric authentication is not available
            handleFallbackNavigation()
        }
    }

    private fun handleFallbackNavigation() {
        notificationUseCase.loadFromPreferences()
        val (url, handle) = notificationUseCase.getNotificationData()

        if (handle == true) {
            isAuthenticationRequired = false
//            loadNotifications(url.toString())
            if (isAzureADSessionValid()) {
                navigateToHome(apiConfig.baseUrl)
            } else {
                navigateToHome(apiConfig.mfaEnable)
            }
        } else {
            val event = if (isAzureADSessionValid()) {
                NavigationEvent.NavigateToHome(apiConfig.baseUrl)
            } else {
                currentRoute = null
                NavigationEvent.NavigateToHome(apiConfig.mfaEnable)
            }
            emitNavigationEvent(event)
        }
    }

    private var hasNavigated = false
    private fun emitNavigationEvent(home: NavigationEvent.NavigateToHome) {
        viewModelScope.launch {
            if (!hasNavigated) {
                hasNavigated = true
                _navigationEvents.emit(home)
                Timber.d("Navigation event emitted: ${home.url}")
            } else {
                Timber.d("Navigation event already emitted, suppressing duplicate.")
            }
        }
    }

    fun authenticate(activity: FragmentActivity, screenType: ScreenType) {
        viewModelScope.launch {
            try {
                biometricAuthenticationUseCase.execute(activity, screenType)
            } catch (e: Exception) {
                Timber.e("Error during biometric authentication: ${e.message}")
            } finally {
                isAuthenticationStarted = false
            }
        }
    }

    fun handleAuthResult() {
        viewModelScope.launch {
            authResult.collect { result ->
                when (result) {
                    is BiometricAuthResult.Success -> {
                        isAuthenticationStarted = false
                        isAuthenticationRequired = false
                        Timber.d("Session valid status: ${isAzureADSessionValid()}")
                        Timber.d(
                            "Biometric authentication success. Session valid: " +
                                "${isAzureADSessionValid()}",
                        )
                        if (isAzureADSessionValid()) {
                            notificationUseCase.loadFromPreferences()
                            val (url, handle) = notificationUseCase.getNotificationData()
                            if (handle == true && url != null) {
//                                loadNotifications(
//                                    notificationUseCase.getNotificationData().first.toString(),
//                                )
                                navigateToHome(apiConfig.baseUrl)
                            } else {
                                navigateToHome(apiConfig.baseUrl)
                            }
                            // After successful authentication, navigate to the notification URL
                        } else {
                            navigateToHome(apiConfig.mfaDisable)
                        }
//                        if (isAzureADSessionValid()) {
//                                // If authenticated and the session is valid, navigate directly
//                                // to the Home Page.
//                                    _navigationEvents.emit(
//                                        NavigationEvent.NavigateToHome(apiConfig.baseUrl),
//                                    )
//
//                            } else {
//                                Timber.d(
//                                    "StatusCode: ${result.data.statusCode}," +
//                                        "attemptCount: ${result.data.attemptCount}",
//                                )
//                                val isAuthenticationAvailable = canAuthenticate()
//                                // If authenticated but the session is invalid, navigate
//                                // to MFA Enable mode.
//                                if (result.data.statusCode == 200 &&
//
//                                     result.data.attemptCount <= 5
//                                ) {
//                                    // biometric success  and session expired and
//                                    // attemptcount less than 5
//                                    _navigationEvents.emit(
//                                        NavigationEvent.NavigateToHome(apiConfig.mfaDisable),
//                                    )
//                                } else if (result.data.statusCode == 200 &&
//                                    result.data.attemptCount > 5
//                                ) {
//                                    // biometric failure and passcode failure
//                                    // after 30 sec biometric success.
//                                    _navigationEvents.emit(
//                                        NavigationEvent.NavigateToHome(apiConfig.mfaEnable),
//                                    )
//                                } else if (result.data.statusCode == 201 &&
//                                    result.data.attemptCount <= 5 &&
//                                    isAuthenticationAvailable ==
//                                    AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED
//                                ) {
//                                    // biometric failure and passcode success .
//                                    _navigationEvents.emit(
//                                        NavigationEvent.NavigateToHome(apiConfig.mfaDisable),
//                                    )
//                                } else if (result.data.statusCode == 201 &&
//                                    result.data.attemptCount <= 5 &&
//                                    isAuthenticationAvailable ==
//                                    AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
//                                ) {
//                                    // biometric failure and passcode success .
//                                    _navigationEvents.emit(
//                                        NavigationEvent.NavigateToHome(apiConfig.mfaDisable),
//                                    )
//                                } else if (result.data.statusCode == 201 &&
//                                    result.data.attemptCount > 5
//                                ) {
//                                    // biometric failure and passcode success after 30 sec.
//                                    _navigationEvents.emit(
//                                        NavigationEvent.NavigateToHome(apiConfig.mfaEnable),
//                                    )
//                                }
//                            }
                    }
                    is BiometricAuthResult.Failure -> {
                        Timber.e("Navigation Stack ${navigator.getNavigationStack()}")
                        isAuthenticationStarted = false
                        _uiState.update {
                            if (result.error.biometricStatus.code == 10 || result.error
                                    .biometricStatus.code == 13
                            ) {
                                if (canAuthenticate() ==
                                    AuthenticationStatus
                                        .BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
                                ) {
                                    _uiState.update { it.copy(isKeyGuardCancel = true) }
                                } else {
                                    _navigationEvents.emit(
                                        NavigationEvent.NavigateToBiometricCancelScreen,
                                    )
                                }
                            }
                            when (result.error.biometricStatus.code) {
                                BiometricPrompt.ERROR_SECURITY_UPDATE_REQUIRED,
                                -> it.copy(
                                    showAuthenticationNotAvailableDialog = true,
                                )
                                BiometricPrompt.ERROR_CANCELED -> it.copy(
                                    showAuthenticationRetryDialog = true,
                                )
                                BiometricPrompt.ERROR_LOCKOUT,
                                BiometricPrompt.ERROR_LOCKOUT_PERMANENT,
                                -> it.copy(
                                    showAuthenticationFailedDialog = true,
                                )
                                else -> it
                            }
                        }
                    }
                    BiometricAuthResult.Loading -> _uiState.update { it.copy(isLoading = true) }
                }
            }
        }
    }

    private fun loadNotifications(url: String) {
        viewModelScope.launch {
            if (url.contains("Safety/Reply") || isAzureADSessionValid()) {
                navigateToNotification(url)
            } else {
                navigateToHome(apiConfig.mfaEnable)
            }
        }
    }

    private fun isNetworkAvailable(): Boolean {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }

    private fun isAzureADSessionValid(): Boolean {
        // Check user login status and session validation
        return connectbizPreference.getUserLoggedIn() && !connectbizPreference.isSessionExpired()
    }

    fun navigateToForceUpdateScreen(message: String, destinationLink: String) {
        viewModelScope.launch {
            navigator.navigationStackClear()
            navigator.navigateWithQueryParam(
                route = Route.Welcome.FORCE_UPDATE,
                queryParams = mapOf(
                    "message" to message,
                    "destinationLink" to destinationLink,
                ),
                popUpToRoute = Route.Welcome.SPLASH,
                inclusive = true,
                screenName = ConnectbizAnalyticConstant.Page.FORCE_UPDATE,
            )
        }
    }

    fun navigateToMaintenanceScreen(message: String) {
        viewModelScope.launch {
            navigator.navigationStackClear()
            navigator.navigateWithQueryParam(
                route = Route.Welcome.MAINTENANCE,
                queryParams = mapOf("message" to message),
                popUpToRoute = Route.Welcome.SPLASH,
                inclusive = true,
                screenName = ConnectbizAnalyticConstant.Page.MAINTENANCE,
            )
        }
    }

    fun navigateToErrorScreen() {
        viewModelScope.launch {
            navigator.navigationStackClear()
            navigator.navigateTo(
                route = Route.Welcome.ERROR,
                screenName = ConnectbizAnalyticConstant.Page.ERROR,
                popUpTo = Route.Welcome.SPLASH,
                inclusive = true,
            )
        }
    }

    fun userLoginWith2FactorAuth() {
        viewModelScope.launch {
            navigator.navigationStackClear()
            sessionUseCase.invoke() // Trigger the session use case
            _navigationEvents.emit(NavigationEvent.NavigateToMFEEnableScreen(apiConfig.mfaEnable))
            Timber.d("Navigated to MFA Enable Screen.")
        }
    }

    fun navigateToLoginFlow() {
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    isLoading = false,
                    showAuthenticationNotAvailableDialog = false,
                    showAuthenticationRetryDialog = false,
                    showAuthenticationFailedDialog = false,
                    isKeyGuardCancel = false,
                    lastUpdated = System.currentTimeMillis(),
                )
            }
            navigator.navigationStackClear()
            sessionUseCase.invoke()
            _navigationEvents.emit(NavigationEvent.NavigateToMFEDisableScreen(apiConfig.mfaDisable))
        }
    }

    fun navigateToHome(baseUrl: String) {
        viewModelScope.launch {
            // Assuming this retrieves the stack
            val navigationStack = navigator.getNavigationStack()
            val lastKnownRoute = navigationStack.lastOrNull { it.id != null }?.id
            Timber.d("Last Known Route: $lastKnownRoute")
            if (lastKnownRoute == null ||
                lastKnownRoute == Route.Welcome.AUTHENTICATION_CANCEL ||
                lastKnownRoute == Route.Welcome.SPLASH ||
                lastKnownRoute == Route.Welcome.TUTORIAL ||
                lastKnownRoute == Route.Welcome.MAINTENANCE ||
                lastKnownRoute == Route.Welcome.FORCE_UPDATE
            ) {
                // If last route is one of the above, navigate to the home screen
                navigator.navigateWithQueryParam(
                    route = Route.Home.HOME,
                    queryParams = mapOf("url" to baseUrl),
                    popUpToRoute = Route.Welcome.SPLASH,
                    inclusive = true,
                    screenName = ConnectbizAnalyticConstant.Page.HOME,
                )
            } else {
                // Otherwise, navigate back to last route
                Timber.d("Before Splash Navigation ${navigator.getNavigationStack()}")
                navigator.navigateToLast()
                Timber.d("After Splash Navigation ${navigator.getNavigationStack()}")
            }
        }
    }

    fun navigateToNotification(url: String) {
        viewModelScope.launch {
            navigator.navigationStackClear() // Clear the stack to avoid pushing to the wrong screen
            navigator.navigateWithQueryParam(
                route = Route.Home.HOME,
                queryParams = mapOf("url" to url),
                popUpToRoute = Route.Welcome.SPLASH, // Make sure we pop up to the right route
                inclusive = true,
                screenName = ConnectbizAnalyticConstant.Page.HOME,
            )
        }
    }

    fun navigateToBiometricCancel() {
        viewModelScope.launch {
            navigator.navigationStackClear()
            navigator.navigateTo(
                route = Route.Welcome.AUTHENTICATION_CANCEL,
                screenName = ConnectbizAnalyticConstant.Page.AUTHENTICATION_CANCEL,
                popUpTo = Route.Welcome.SPLASH,
                inclusive = true,
            )
        }
    }

    fun navigateToTutorialScreen() {
        viewModelScope.launch {
            navigator.navigationStackClear()
            navigator.navigateTo(
                route = Route.Welcome.TUTORIAL,
                screenName = ConnectbizAnalyticConstant.Page.TUTORIAL,
                popUpTo = Route.Welcome.SPLASH,
                inclusive = false,
            )
        }
    }

    fun receiveUrl(url: String, isFromNotification: Boolean) {
        viewModelScope.launch {
            try {
                notificationUseCase.clearNotificationData()
                notificationUseCase.setNotificationData(url, true)
                notificationUseCase.saveToPreferences()
                notificationUseCase.loadFromPreferences()
                _uiState.update { it.copy(isNotification = true) }
            } catch (e: Exception) {
                Timber.e("Error processing received URL: ${e.message}")
            }
        }
    }

    data class SplashUiState(
        val isLogoAnimationDone: Boolean = false,
        val isNetworkAvailable: Boolean = true,
        val isTutorialShows: Boolean = false,
        val showAuthenticationNotAvailableDialog: Boolean = false,
        val showAuthenticationFailedDialog: Boolean = false,
        val showAuthenticationRetryDialog: Boolean = false,
        val isKeyGuardCancel: Boolean = false,
        val isLoading: Boolean = false,
        val errorMessage: String? = null,
        val isNotification: Boolean = false,
        val lastUpdated: Long = System.currentTimeMillis(), // Add this field
    )

    sealed class NavigationEvent {
        data class NavigateToHome(val url: String) : NavigationEvent()
        data class NavigateToMaintenanceScreen(val message: String) : NavigationEvent()
        data class NavigateToForceUpdateScreen(
            val message: String,
            val destinationLink: String,
        ) : NavigationEvent()
        object NavigateToTutorialScreen : NavigationEvent()
        object NavigateToErrorScreen : NavigationEvent()
        object NavigateToBiometricCancelScreen : NavigationEvent()
        data class NavigateToMFEEnableScreen(val url: String) : NavigationEvent()
        data class NavigateToMFEDisableScreen(val url: String) : NavigationEvent()
    }
}
