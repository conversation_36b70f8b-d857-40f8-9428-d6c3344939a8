package jp.co.dhbk.connectbiz.ui.utils.biometricAuth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.Dimen
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.ButtonBackgroundPrimary
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.ButtonTextPrimary
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.PrimaryLight
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.TextPrimaryLight
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography.BoldFontWeight
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTextView

@Composable
fun BiometricScreen(
    onSkipClick: () -> Unit,
    onAuthenticateClick: () -> Unit,
    authenticationStatus: AuthenticationStatus,
    modifier: Modifier = Modifier,
) {
    val (descriptionTextRes, authenticateButtonTextRes) = remember {
        when (authenticationStatus) {
            // Cases for biometric enabled
            AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED,
            AuthenticationStatus.BIOMETRIC_NOT_ENROLLED,
            -> Pair(
                R.string.biometric_popup_biometric_enabled,
                R.string.biometric_popup_button,
            )

            // Cases for keyguard enabled
            AuthenticationStatus.SECURITY_UPDATE_REQUIRED,
            AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE,
            -> Pair(
                R.string.biometric_popup_keyguard_enabled,
                R.string.biometric_popup_keyguard_button,
            )

            // Cases for device not secure
            AuthenticationStatus.DEVICE_NOT_SECURE -> Pair(
                R.string.biometric_popup_keyguard_enabled,
                R.string.biometric_popup_keyguard_button,
            )

            // Default case
            else -> Pair(
                R.string.biometric_popup_biometric_enabled,
                R.string.biometric_popup_button,
            )
        }
    }

    Box(
        modifier =
        modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.6f))
            .padding(24.dp),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            modifier =
            Modifier.Companion
                .background(
                    Color.White,
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
                )
                .padding(16.dp)
                .wrapContentSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Spacer(modifier = Modifier.height(16.dp))

            GlobalTextView(
                text = stringResource(R.string.biometric_popup_title),
                color = TextPrimaryLight,
                textAlign = TextAlign.Center,
                fontWeight = BoldFontWeight,
                fontSize = TemplateTypography.SizeXL, // Using iOS size for the title
                lineHeight = TemplateTypography.SizeXL * TemplateTypography.LineHeight160,
                modifier = Modifier.padding(all = 0.dp),
            )

            Spacer(modifier = Modifier.height(0.dp))

            GlobalTextView(
                text = stringResource(descriptionTextRes),
                lineHeight = TemplateTypography.SizeMS * TemplateTypography.LineHeight160,
                color = TextPrimaryLight,
                fontSize = TemplateTypography.SizeM, // Using the regular body size
                fontWeight = TemplateTypography.RegularFontWeight,
                textAlign = TextAlign.Center,
                modifier = Modifier.width(260.dp),
            )

            Spacer(modifier = Modifier.height(16.dp))

            Image(
                painter = painterResource(id = R.drawable.ic_biometric_auth), // Biometric icon
                contentDescription = "Biometric Icon",
                modifier =
                Modifier
                    .width(250.dp)
                    .height(163.dp),
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Button to authenticate
            Button(
                onClick = { onAuthenticateClick() },
                modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
                    .height(50.dp),
                colors = ButtonDefaults.buttonColors(ButtonBackgroundPrimary),
                shape = androidx.compose.foundation.shape.RoundedCornerShape(Dimen.space),
            ) {
                Text(
                    text = stringResource(authenticateButtonTextRes),
                    style = TemplateTypography.button1BoldStyle.copy(
                        fontSize = TemplateTypography.SizeM,
                        fontWeight = BoldFontWeight,
                        lineHeight = TemplateTypography.SizeM * TemplateTypography.LineHeight160,
                    ),
                    color = ButtonTextPrimary,
                )
            }

            Spacer(
                modifier =
                Modifier
                    .height(8.dp)
                    .padding(16.dp),
            )

            // "Skip" button
            TextButton(
                onClick = onSkipClick,
            ) {
                Text(
                    text = stringResource(R.string.biometric_popup_skip),
                    textAlign = TextAlign.Center,
                    fontSize = TemplateTypography.SizeMSS,
                    fontWeight = BoldFontWeight,
                    lineHeight = TemplateTypography.SizeMSS * TemplateTypography.LineHeight160,
                    style = TemplateTypography.body2, // Using body2 style
                    color = PrimaryLight,
                )
            }
        }
    }
}

@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
@Composable
private fun BiometricScreenPreview() {
    BiometricScreen(
        onSkipClick = { /* No-op for preview */ },
        onAuthenticateClick = { /* No-op for preview */ },
        authenticationStatus = AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED,
    )
}
