package jp.co.dhbk.connectbiz.ui.destinations.settings

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.GlobalSnackbarManager
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography
import jp.co.dhbk.connectbiz.ui.utils.biometricAuth.BiometricViewModel
import jp.co.dhbk.connectbiz.ui.utils.biometricAuth.BiometricViewModel.BiometricState
import jp.co.dhbk.connectbiz.ui.utils.component.ConnectBizDialog
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationStarted
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTopAppBar
import kotlinx.coroutines.launch
import timber.log.Timber

@Composable
internal fun AuthenticationSettingsScreen(
    biometricViewModel: BiometricViewModel = hiltViewModel(),
    settingsViewModel: SettingViewModel = hiltViewModel(),
) {
    val biometricLoadState by biometricViewModel.loadStateFlow.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val authState by biometricViewModel.authResult.collectAsStateWithLifecycle()
    var previousAuthState by remember { mutableStateOf<BiometricAuthResult?>(null) }
    // Extract the BiometricState from the LoadState
    LaunchedEffect(Unit) {
        biometricViewModel.initialize()
    }
    val biometricState = when (biometricLoadState) {
        is LoadState.Success -> (biometricLoadState as LoadState.Success).value
        is LoadState.Loading -> BiometricState(isLoading = true)
        is LoadState.Failure -> BiometricState(errorMessage = "Failed to load biometric settings")
        LoadState.Initial -> BiometricState()
    }
    val status = biometricViewModel.canAuthenticate()
    val screenType: ScreenType = ScreenType.SETTINGS
    val coroutineScope = rememberCoroutineScope()

    BackHandler {
        settingsViewModel.goBack()
    }

    AuthenticationSettingsContent(
        biometricState = biometricState,
        onBackClick = { settingsViewModel.goBack() },
        navigateToSystemSettings = {
            GlobalAppState.isCustomTabOpened = true
            biometricViewModel.navigateToSystemSettings(context)
        },
        onDisableBiometric = { newState ->
            Timber.e(
                "On Disable Clicked $newState && Biometric Status ${biometricViewModel
                    .connectbizPreference.isBiometricEnabled()} AuthStatus $status",
            )
            if (newState && biometricViewModel
                    .connectbizPreference.isBiometricEnabled()
            ) {
                GlobalAppState.isCustomTabOpened = false
                handleAuthentication(biometricViewModel = biometricViewModel)
            } else {
                if (!biometricViewModel.connectbizPreference.isBiometricEnabled() &&
                    (
                        status == AuthenticationStatus.BIOMETRIC_NOT_ENROLLED || status ==
                            AuthenticationStatus.DEVICE_NOT_SECURE
                        )
                ) {
                    biometricViewModel.onUpdateUISetting(true)
                } else if (!biometricViewModel.connectbizPreference.isBiometricEnabled() &&
                    (
                        status == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED || status ==
                            AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
                        )
                ) {
                    biometricViewModel.onToggleChange(false)
                } else {
                    handleAuthentication(biometricViewModel)
                }
            }
        },
        onShowSettingDisablePopupChange = { show ->
            val authStatus = biometricViewModel.canAuthenticate()
            if (authStatus == AuthenticationStatus.BIOMETRIC_NOT_ENROLLED &&
                !biometricViewModel.biometricEnableStatus()
            ) {
                biometricViewModel.onSettingsUpdate(show)
            } else if (authStatus == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED ||
                authStatus == AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE &&
                !biometricViewModel.biometricEnableStatus()
            ) {
                handleAuthentication(biometricViewModel)
            } else if (authStatus == AuthenticationStatus.SECURITY_UPDATE_REQUIRED || authStatus ==
                AuthenticationStatus.DEVICE_LOCKED
            ) {
                biometricViewModel.onBiometricUnAvailablePopup(true)
            } else {
                handleAuthentication(biometricViewModel)
                Timber.e("Unhandled authentication status: $status")
            }
        },
        onNavigateToSplash = {
            /*
             * Max Reach (Settings - On to Off):
             * This scenario triggers a logout, regardless of authentication or session status.
             *
             * Max Reach (Settings - Off to On):
             *If the session is invalid, the user should leave the app.
             *If the session is valid and the user is authenticated, the app does nothing.
             *
             * */
            if (biometricViewModel.connectbizPreference.isBiometricEnabled()) {
                GlobalAppState.isCustomTabOpened = false
                settingsViewModel.sessionLogout()
            } else {
                Timber.e(
                    "Authentication Failed " +
                        "${biometricViewModel.connectbizPreference.isBiometricEnabled()}",
                )
            }
        },
        onToggleStatusRestore = { status ->
            biometricViewModel.onToggleChange(
                biometricViewModel.connectbizPreference
                    .isBiometricEnabled(),
            )
        },
        onEnableBiometric = {
            biometricViewModel.authenticate(
                activity = context as FragmentActivity,
                screenType = screenType,
            )
        },
        status = status,
        modifier = Modifier,
    )

    // Observe authentication state changes
    LaunchedEffect(authState) {
        if (authState != previousAuthState) {
            when (val state = authState) {
                is BiometricAuthResult.Success -> {
                    isAuthenticationStarted = false
                    biometricViewModel.onUpdatePopUpState(false)
                    biometricViewModel.updateBiometricEnableStatus(
                        !biometricViewModel
                            .connectbizPreference.isBiometricEnabled(),
                    )
                    coroutineScope.launch {
                        if (biometricViewModel.connectbizPreference.isBiometricEnabled()) {
                            // Show success message when toggle is ON
                            GlobalSnackbarManager.showNormalSnackbar("アプリ認証の設定が完了しました")
                        } else {
                            // Show success message when toggle is OFF
                            GlobalSnackbarManager.showNormalSnackbar("アプリ認証を解除しました")
                        }
                    }
                }
                is BiometricAuthResult.Failure -> {
                    isAuthenticationStarted = false
                    if (state.error.biometricStatus == BiometricStatus.ERROR_USER_CANCELED ||
                        state.error.biometricStatus == BiometricStatus.ERROR_CANCELED
                    ) {
                        biometricViewModel.onToggleStateRestore(state.error.attemptCount)
                    } else if (state.error.biometricStatus == BiometricStatus.ERROR_LOCKOUT ||
                        state.error.biometricStatus == BiometricStatus.ERROR_LOCKOUT_PERMANENT
                    ) {
                        biometricViewModel.onToggleStateRestore(state.error.attemptCount)
                    } else if (state.error.biometricStatus ==
                        BiometricStatus.ERROR_NEGATIVE_BUTTON
                    ) {
                        biometricViewModel.onToggleChange(
                            biometricViewModel
                                .connectbizPreference.isBiometricEnabled(),
                        )
                    }

                    // Update previousAuthState to prevent duplicate snackbar triggers
                    previousAuthState = authState
                }
                else -> {}
            }
        }
    }

    LaunchedEffect(biometricState.isAuthenticationCancel) {
        coroutineScope.launch {
            if (biometricState.isAuthenticationCancel > 0) {
                GlobalSnackbarManager.showNormalSnackbar(biometricState.authenticationErrorMessage)
            }
        }
    }
}

// Consolidate authentication logic
fun handleAuthentication(biometricViewModel: BiometricViewModel) {
    val authStatus = biometricViewModel.canAuthenticate()
    when (authStatus) {
        AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED,
        AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE,
        -> {
            GlobalAppState.isCustomTabOpened = false
            if (biometricViewModel.connectbizPreference.isBiometricEnabled()) {
                biometricViewModel.onBiometricDisablePopup(true)
            } else {
                biometricViewModel.onUpdatePopUpState(true)
            }
        }
        AuthenticationStatus.BIOMETRIC_NOT_ENROLLED -> {
            biometricViewModel.onSettingsUpdate(true)
        }

        AuthenticationStatus.SECURITY_UPDATE_REQUIRED -> {
            biometricViewModel.onBiometricUnAvailablePopup(true)
        }
        else -> {
            Timber.e("Unhandled authentication status: $authStatus")
        }
    }
    //
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AuthenticationSettingsContent(
    biometricState: BiometricState,
    onBackClick: () -> Unit,
    navigateToSystemSettings: () -> Unit,
    onDisableBiometric: (Boolean) -> Unit,
    onShowSettingDisablePopupChange: (Boolean) -> Unit,
    onNavigateToSplash: () -> Unit,
    onToggleStatusRestore: (Boolean) -> Unit,
    onEnableBiometric: (Boolean) -> Unit,
    status: AuthenticationStatus,
    modifier: Modifier = Modifier,
) {
    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            GlobalTopAppBar(
                text = stringResource(R.string.authentication_setting_title),
                onNavigateBack = onBackClick,
            )
        },
        containerColor = TemplateColors.BackgroundLight,
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .background(TemplateColors.BackgroundLight),
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .background(TemplateColors.BackgroundLight),
                verticalArrangement = Arrangement.spacedBy(40.dp),
            ) {
                item {
                    SettingRowWithToggle(
                        label = stringResource(R.string.Authentication_check),
                        toggleState = biometricState.isToggleEnabled,
                        onToggleChange = { isEnabled ->
                            if (isEnabled) {
                                onDisableBiometric(!isEnabled)
                            } else {
                                onShowSettingDisablePopupChange(true)
                            }
                        },

                    )
                }
                item {
                    HeaderWithDescription(
                        headerText = stringResource(R.string.about_auth_header),
                        descriptionText = stringResource(R.string.about_authentication),
                    )
                }
            }
        }

        // Show popups based on flags
        when {
            biometricState.isSettingsPopupVisible -> {
                ConnectBizDialog(
                    title = stringResource(R.string.biometric_settings_popup_title),
                    message = stringResource(R.string.biometric_settings_popup_description),
                    enableCancel = true,
                    negativeLabel = stringResource(R.string.alert_cancel),
                    positiveLabel = stringResource(R.string.go_to_device_settings),
                    onClick = { index ->
                        if (index == 1) {
                            navigateToSystemSettings()
                        } else {
                            onToggleStatusRestore(false)
                            onShowSettingDisablePopupChange(false)
                        }
                    },
                )
            }
            biometricState.isBiometricDisablePopup -> {
                ConnectBizDialog(
                    title = null,
                    message = stringResource(R.string.biometric_failed_title),
                    enableCancel = true,
                    negativeLabel = stringResource(R.string.alert_cancel),
                    positiveLabel = stringResource(R.string.biometric_cancellation),
                    onClick = { index ->
                        if (index == 1) {
                            onEnableBiometric(false)
                        } else {
                            onToggleStatusRestore(true)
                        }
                    },
                )
            }
            biometricState.isAuthenticationRetryPopup -> {
                ConnectBizDialog(
                    title = stringResource(R.string.authentication_retry_popup_title),
                    message = stringResource(R.string.authentication_retry_popup_description),
                    enableCancel = true,
                    negativeLabel = stringResource(R.string.alert_cancel),
                    positiveLabel = stringResource(R.string.retry_btn),
                    onClick = { index ->
                        if (index == 1) {
                            onEnableBiometric(!biometricState.isToggleEnabled)
                        } else {
                            onToggleStatusRestore(true)
                        }
                    },
                )
            }
            biometricState.isAuthenticationFailedPopup -> {
                ConnectBizDialog(
                    title = stringResource(R.string.authentication_error_popup_title),
                    message = stringResource(R.string.authentication_error_popup_description),
                    enableCancel = true,
                    negativeLabel = null,
                    positiveLabel = stringResource(R.string.close),
                    onClick = { index -> if (index == 1) onNavigateToSplash() },
                )
            }
            biometricState.isBiometricUnAvailable -> {
                ConnectBizDialog(
                    title = stringResource(R.string.biometric_error_popup_title),
                    message = stringResource(R.string.biometric_error_popup_description),
                    enableCancel = true,
                    negativeLabel = null,
                    positiveLabel = stringResource(R.string.close),
                    onClick = { },
                )
            }
            biometricState.isBiometricPopupState -> {
                ConnectBizDialog(
                    title = if (status == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED ||
                        status == AuthenticationStatus.BIOMETRIC_NOT_ENROLLED
                    ) {
                        stringResource(R.string.biometric_enable_title)
                    } else {
                        stringResource(R.string.passcode_enable_title)
                    },
                    message = if (status == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED ||
                        status == AuthenticationStatus.BIOMETRIC_NOT_ENROLLED
                    ) {
                        stringResource(R.string.biometric_enable_description)
                    } else {
                        stringResource(R.string.passcode_enable_description)
                    },
                    enableCancel = true,
                    negativeLabel = stringResource(R.string.alert_cancel),
                    positiveLabel = stringResource(R.string.allow),
                    onClick = { index ->
                        if (index == 1) {
                            onEnableBiometric(true)
                        } else {
                            onToggleStatusRestore(true)
                        }
                    },
                )
            }
        }
    }
}

@Composable
fun SettingRowWithToggle(
    label: String,
    toggleState: Boolean,
    onToggleChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier, // Added modifier parameter
) {
    Column(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth() // Make it fill available width
                .height(60.dp) // Fixed height of 60dp
                .padding(start = 20.dp) // Padding around the row
                .alpha(1f), // Ensure the Row is fully visible
        ) {
            Text(
                text = label,
                modifier = Modifier.weight(1f),
                style = TemplateTypography.body1,
                color = TemplateColors.TextPrimaryLight,
            )

            Image(
                painter = painterResource(
                    id = if (toggleState) R.drawable.toggle_on else R.drawable.toggle_off,
                ),
                contentDescription = if (toggleState) "Toggle On" else "Toggle Off",
                modifier = Modifier
                    .size(73.33.dp, 44.dp) // Set the size directly for the toggle
                    .padding(
                        start = 11.dp,
                        end = 11.33.dp,
                        top = 6.dp,
                        bottom = 7.dp,
                    ) // Padding values
                    .clickable(
                        onClick = {
                            onToggleChange(toggleState)
                        },
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() },
                    ),
            )
        }

        // Divider below the toggle
        HorizontalDivider(
            color = TemplateColors.DividerColor,
            thickness = 0.5.dp,
            modifier = Modifier
                .fillMaxWidth()
                .height(0.dp)
                .alpha(1f)
                .graphicsLayer(rotationZ = 0f),
        )
    }
}

@Composable
fun HeaderWithDescription(
    headerText: String,
    descriptionText: String,
    modifier: Modifier = Modifier, // Added modifier parameter
) {
    Column(
        modifier = modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth()
            .wrapContentWidth(Alignment.Start),
    ) {
        // Header
        Text(
            text = headerText,
            style = TemplateTypography.title3Bold,
            color = TemplateColors.TextPrimaryLight,
            modifier = Modifier.align(Alignment.Start),
        )

        Spacer(modifier = Modifier.height(12.dp))

        // Description
        Text(
            text = descriptionText,
            style = TemplateTypography.body2.copy(color = TemplateColors.TextSecondary),
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview
@Composable
private fun AuthenticationSettingsContentPreview() {
    AuthenticationSettingsContent(
        biometricState = BiometricState(),
        onBackClick = {},
        navigateToSystemSettings = {},
        onDisableBiometric = {},
        onShowSettingDisablePopupChange = {},
        onNavigateToSplash = {},
        onToggleStatusRestore = {},
        onEnableBiometric = {},
        status = AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED,
    )
}
