package jp.co.dhbk.connectbiz.ui.navigation

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import javax.inject.Inject

@HiltViewModel
class NavStateViewModel @Inject constructor() : ViewModel() {

    private val currentRoute = MutableStateFlow<String?>(null)

    private val lastKnownRouteStateFlow = MutableStateFlow<String?>(null)

    fun updateCurrentRoute(route: String) {
        // Update the last known route before setting the new current route
        lastKnownRouteStateFlow.value = currentRoute.value
        currentRoute.value = route
    }
}
