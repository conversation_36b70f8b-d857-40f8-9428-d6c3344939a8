package jp.co.dhbk.connectbiz.ui.navigation.navgraph

import android.annotation.SuppressLint
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTheme
import jp.co.dhbk.connectbiz.ui.destinations.error.ErrorScreen
import jp.co.dhbk.connectbiz.ui.destinations.forceupdate.ForceUpdateScreen
import jp.co.dhbk.connectbiz.ui.destinations.home.HomeScreen
import jp.co.dhbk.connectbiz.ui.destinations.home.PdfViewerScreen
import jp.co.dhbk.connectbiz.ui.destinations.licenses.LicensesScreen
import jp.co.dhbk.connectbiz.ui.destinations.maintenance.MaintenanceScreen
import jp.co.dhbk.connectbiz.ui.destinations.settings.AuthenticationSettingsScreen
import jp.co.dhbk.connectbiz.ui.destinations.settings.SettingsScreen
import jp.co.dhbk.connectbiz.ui.destinations.splash.SplashScreen
import jp.co.dhbk.connectbiz.ui.destinations.tutorial.TutorialScreen
import jp.co.dhbk.connectbiz.ui.destinations.splash.AuthenticationCancelScreen

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
fun AppNavGraph(
    navController: NavHostController,
    navigator: Navigator,
    modifier: Modifier = Modifier,
) {
    NavHost(
        navController = navController,
        startDestination = Root.WELCOME,
        route = Root.APPROOT,
        modifier = modifier,
        enterTransition = TemplateTheme.animations.navigationEnterTransition,
        exitTransition = TemplateTheme.animations.navigationExitTransition,
        popEnterTransition = TemplateTheme.animations.navigationPopEnterTransition,
        popExitTransition = TemplateTheme.animations.navigationPopExitTransition,
    ) {
        welcomeNavGraph(navController)
        homeNavGraph(navigator)
        settingsNavGraph()
    }
}

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
fun NavGraphBuilder.welcomeNavGraph(navController: NavHostController) {
    navigation(
        startDestination = Route.Welcome.SPLASH,
        route = Root.WELCOME,
    ) {
        composable(Route.Welcome.SPLASH) { SplashScreen() }
        composable(
            route = "${Route.Welcome.MAINTENANCE}?message={message}",
            arguments = listOf(navArgument("message") { defaultValue = "No message" }),
        ) { navBackStackEntry ->
            val maintenanceMessage = navBackStackEntry.arguments?.getString("message")
                ?: "No message"
            MaintenanceScreen(maintenanceMessage = maintenanceMessage)
        }
        composable(
            route = Route.Welcome.FORCE_UPDATE +
                "?message={message}&destinationLink={destinationLink}",
            arguments = listOf(
                navArgument("message") { defaultValue = "No message" },
                navArgument("destinationLink") { defaultValue = "No link" },
            ),
        ) { navBackStackEntry ->
            val forceUpdateMessage = navBackStackEntry.arguments
                ?.getString("message") ?: "No message"
            val destinationLink = navBackStackEntry.arguments
                ?.getString("destinationLink") ?: "No link"
            ForceUpdateScreen(
                forceUpdateMessage = forceUpdateMessage,
                destinationLink = destinationLink,
            )
        }
        composable(Route.Welcome.TUTORIAL) { TutorialScreen() }
        composable(Route.Welcome.AUTHENTICATION_CANCEL) {
            AuthenticationCancelScreen(
                authenticationStatus = AuthenticationStatus.DEVICE_NOT_SECURE,
            )
        }
        composable(Route.Welcome.ERROR) { ErrorScreen(navController) }
    }
}

@SuppressLint("NewApi")
fun NavGraphBuilder.homeNavGraph(navigator: Navigator) {
    navigation(startDestination = Route.Home.HOME, route = Root.HOME) {
        composable(
            route = "${Route.Home.HOME}?url={url}",
            arguments = listOf(
                navArgument("url") { defaultValue = "No message" },
            ),
        ) { navBackStackEntry ->
            val url = navBackStackEntry.arguments?.getString("url") ?: "No message"
            HomeScreen(url = url)
        }
        composable(
            route = Route.Home.PDF_VIEWER,
            arguments = listOf(
                navArgument("url") { defaultValue = "No message" },
                navArgument("isRemote") { defaultValue = "true" },
                navArgument("fileType") { defaultValue = "auto" },
                navArgument("fileName") { defaultValue = "Auto" }
            ),
        ) { navBackStackEntry ->
            val url = navBackStackEntry.arguments?.getString("url") ?: "No message"
            val isRemote = navBackStackEntry.arguments?.getString("isRemote")?.toBoolean() ?: true
            val fileType = navBackStackEntry.arguments?.getString("fileType") ?: "auto"
            val fileName = navBackStackEntry.arguments?.getString("fileName") ?: "Auto"
            PdfViewerScreen(url = url, navigator = navigator, isRemote = isRemote, fileType =
            fileType, fileName=fileName)
        }
    }
}

fun NavGraphBuilder.settingsNavGraph() {
    navigation(startDestination = Route.Settings.SETTINGS, route = Root.SETTINGS) {
        composable(Route.Settings.SETTINGS) { SettingsScreen() }
        composable(Route.Settings.AUTHENTICATIONSETTINGS) { AuthenticationSettingsScreen() }
        composable(Route.Settings.LICENSES) { LicensesScreen() }
    }
}
