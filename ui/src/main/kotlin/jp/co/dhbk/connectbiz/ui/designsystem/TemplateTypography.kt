package jp.co.dhbk.connectbiz.ui.designsystem

import androidx.compose.runtime.Immutable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

@Immutable
internal object TemplateTypography {
    // Font family for SF Pro Text
    val sfProFontFamily = FontFamily.SansSerif

    // Font sizes for mobile (Figma & Android) and iOS
    val SizeM: TextUnit = 16.sp // Android/ Figma / Mobile Size = 16sp
    val SizeMS: TextUnit = 14.sp // Android/ Figma / Mobile Size = 14sp
    val SizeM_iOS: TextUnit = 17.sp // iOS Size = 17sp
    val SizeXL: TextUnit = 24.sp // XL Size for Title1_bold
    val SizeL: TextUnit = 22.sp // L Size for Title4_bold TODO: Provisional
    val SizeMSS: TextUnit = 12.sp // Android/ Figma / Mobile Size = 14sp

    // Line Heights (160% of the font size)
    val LineHeight160: Float = 1.6f // 160% Line Height as a multiplier (not TextUnit directly)

    // Font weights
    val RegularFontWeight: FontWeight = FontWeight.Normal
    val BoldFontWeight: FontWeight = FontWeight.Bold
    val MediumFontWeight: FontWeight = FontWeight.Medium // TODO: Provisional

    // Text styles
    val body2: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = RegularFontWeight,
        fontSize = SizeMS,
        lineHeight = SizeMS * LineHeight160, // Multiply with 1.6 to apply the 160% line height
        textAlign = TextAlign.Start,
        letterSpacing = 0.sp,
    )

    val body1: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = RegularFontWeight,
        fontSize = SizeM,
        lineHeight = SizeM * LineHeight160, // Multiply with 1.6 to apply the 160% line height
        textAlign = TextAlign.Start,
        letterSpacing = 0.sp,
    )

    val body1Bold: TextStyle = body1.copy(
        fontWeight = BoldFontWeight,
    )

    val button1Bold: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = BoldFontWeight,
        fontSize = SizeM,
        lineHeight = SizeM * LineHeight160, // Multiply with 1.6 to apply the 160% line height
        textAlign = TextAlign.Center,
        letterSpacing = 0.sp,
    )

    // Title1_bold (XL size, Bold)
    val title1Bold: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = BoldFontWeight,
        fontSize = SizeXL, // 24sp for Title1_bold
        lineHeight = SizeXL * LineHeight160, // 160% line height
        textAlign = TextAlign.Center,
        letterSpacing = 0.sp,
    )

    // Title2_bold (M size, Bold)
    val title2Bold: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = BoldFontWeight,
        fontSize = SizeM_iOS, // 16sp for Android & 17sp for iOS
        lineHeight = SizeM_iOS * LineHeight160, // 160% line height
        textAlign = TextAlign.Center,
        letterSpacing = 0.sp,
    )

    // Title3_bold (MS size, Bold)
    val title3Bold: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = BoldFontWeight,
        fontSize = SizeMS, // 14sp for Android & 15sp for iOS
        lineHeight = SizeMS * LineHeight160, // 160% line height
        textAlign = TextAlign.Center,
        letterSpacing = 0.sp,
    )

    // Title4_medium (L size, Bold) TODO: Provisional
    val title4Medium: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontSize = SizeL,
        fontWeight = MediumFontWeight,
        lineHeight = SizeL * LineHeight160,
        textAlign = TextAlign.Center,
        letterSpacing = 0.sp,
    )

    val body1Style: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = RegularFontWeight,
        fontSize = SizeM,
        lineHeight = SizeM * LineHeight160, // 160% line height for Body1
        textAlign = TextAlign.Start,
        letterSpacing = 0.sp,
    )

    val body2Style: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = RegularFontWeight,
        fontSize = SizeMS,
        lineHeight = SizeMS * LineHeight160, // 160% line height for Body2
        textAlign = TextAlign.Start,
        letterSpacing = 0.sp,
    )

    val body1BoldStyle: TextStyle = body1Style.copy(
        fontWeight = BoldFontWeight, // Body1_bold with bold weight
    )

    val button1BoldStyle: TextStyle = TextStyle(
        fontFamily = sfProFontFamily,
        fontWeight = BoldFontWeight,
        fontSize = SizeM,
        lineHeight = SizeM * LineHeight160, // 160% line height for Button1_bold
        textAlign = TextAlign.Center,
        letterSpacing = 0.sp,
    )
}
