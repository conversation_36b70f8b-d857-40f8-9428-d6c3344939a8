/** Class contains all route information */
object Route {
    // Global controls like going back
    object Control {
        const val BACK = "back"
    }

    // Welcome / Pre-login screens
    object Welcome {
        const val SPLASH = "splash"
        const val MAINTENANCE = "maintenance?message={message}"
        const val FORCE_UPDATE = "forceupdate?forceupdate=" +
            "{message}&destination_link={destination_link}"
        const val TUTORIAL = "tutorial"
        const val AUTHENTICATION_CANCEL = "AuthenticationCancelScreen"
        const val ERROR = "errorScreen"
    }

    // Home screens
    object Home {
        const val HOME = "home"
        const val PDF_VIEWER = "pdfviewer?url={url}&isRemote={isRemote}&fileType={fileType}&fileName={fileName}"
    }

    // Settings screens
    object Settings {
        const val SETTINGS = "settings"
        const val AUTHENTICATIONSETTINGS = "authentication_settings"
        const val LICENSES = "licenses"
    }
}

/** To hold the nav graph roots */
object Root {
    const val APPROOT = "approot" // Main navigation root
    const val WELCOME = "welcomeroot" // Root for the welcome flow
    const val HOME = "homeroot" // Root for home-related screens
    const val SETTINGS = "settingsroot" // Root for settings and permissions
}
