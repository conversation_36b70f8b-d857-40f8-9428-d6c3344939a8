package jp.co.dhbk.connectbiz.ui.destinations.tutorial

import androidx.annotation.DrawableRes
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.analytics.ConnectbizAnalyticConstant
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.usecase.notificationusecase.NotificationUseCase
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map

@HiltViewModel
class TutorialViewModel @Inject constructor(
    private val navigator: Navigator,
    private val apiConfig: ApiConfig,
    private val connectbizPreference: ConnectbizPreference,
    private val notificationUseCase: NotificationUseCase,
) : ViewModel() {

    private val _state = MutableStateFlow(State())

    val state = _state.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = State(),
    )

    private val tutorialPages = listOf(
        TutorialPageState(
            title = R.string.toturial_page_one_title,
            description = R.string.toturial_page_one_description,
            imageResId = R.drawable.tutorial_image_one,
            contentDescription = "Tutorial Image One",
        ),
        TutorialPageState(
            title = R.string.toturial_page_two_title,
            description = R.string.toturial_page_two_description,
            imageResId = R.drawable.tutorial_image_two,
            contentDescription = "Tutorial Image Two",
        ),
        TutorialPageState(
            title = R.string.toturial_page_three_title,
            description = R.string.toturial_page_three_description,
            imageResId = R.drawable.tutorial_image_three,
            contentDescription = "Tutorial Image Three",
        ),
        // Fourth page with empty description
        TutorialPageState(
            title = R.string.toturial_page_four_title,
            description = R.string.empty_description,
            imageResId = R.drawable.tutorial_image_four,
            contentDescription = "Tutorial Image Four",
        ),

    )

    val tutorialPageState: StateFlow<TutorialPageState> = _state.map {
        tutorialPages.getOrElse(it.currentPage) { tutorialPages.last() }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), tutorialPages.first())

    fun moveToNextPage() {
        _state.update { currentState ->
            currentState.copy(currentPage = currentState.currentPage + 1)
        }
    }

    fun setPage(page: Int) {
        _state.update { currentState ->
            currentState.copy(currentPage = page)
        }
    }

    fun navigateToLogin(isConnected: Boolean) {
        if (isConnected) {
            val url = apiConfig.mfaEnable
            navigateToHome(url)
        } else {
            navigator.navigateToBack()
        }
    }

    private fun navigateToHome(url: String) {
        connectbizPreference.setTutorialShown(true)
        notificationUseCase.saveToPreferences()
        val timestamp = System.currentTimeMillis().toString()
        val updatedUrl = "$url#timestamp=$timestamp"
        viewModelScope.launch {
            navigator.navigateWithQueryParam(
                route = Route.Home.HOME,
                queryParams = mapOf(
                    "url" to updatedUrl,
                    "push_notification_url" to "No Link",
                ),
                popUpToRoute = Route.Welcome.SPLASH,
                inclusive = false,
                screenName = ConnectbizAnalyticConstant.Page.HOME,
            )
        }
    }

    fun isTutorialShow(): Boolean {
        return connectbizPreference.isTutorialShown()
    }

    fun updateTutorialShow(bool: Boolean) {
        connectbizPreference.setTutorialShown(bool)
    }

    fun goBack() {
        navigator.navigateToBack()
    }

    data class State(
        val currentPage: Int = 0,
        val isLoading: Boolean = false,
    )

    data class TutorialPageState(
        val title: Int,
        val description: Int,
        @DrawableRes val imageResId: Int,
        val contentDescription: String,
    )
}
