package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography

@Composable
fun GlobalTextView(
    text: String,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = TemplateTypography.body2.fontSize, // Default to body2 size
    fontWeight: FontWeight? = TemplateTypography.body2.fontWeight, // Default to body2 weight
    lineHeight: TextUnit = TemplateTypography.body2.lineHeight, // Default to body2 lineHeight
    color: Color = Color.Black,
    textAlign: TextAlign = TextAlign.Start,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    onClick: (() -> Unit)? = null,
    padding: Int = 8,
) {
    Text(
        text = text,
        fontSize = fontSize,
        fontWeight = fontWeight,
        lineHeight = lineHeight,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(padding.dp)
            .let { currentModifier ->
                // Apply clickable modifier if onClick is not null
                if (onClick != null) {
                    currentModifier.clickable { onClick() }
                } else {
                    currentModifier // If onClick is null, return the original modifier
                }
            }, // Trailing comma added here to comply with the style
    )
}
