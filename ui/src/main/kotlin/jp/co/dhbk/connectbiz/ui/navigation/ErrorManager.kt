package jp.co.dhbk.connectbiz.ui.navigation

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import javax.inject.Singleton

/**
 * To maintin the global error at Main Activity
 */
@Singleton
class ErrorManager
@Inject
constructor() {
    private val _errorInfo = MutableStateFlow(ErrorInfo())
    val errorInfo: StateFlow<ErrorInfo> = _errorInfo

    fun post(errorInfo: ErrorInfo?) {
        _errorInfo.update { errorInfo ?: ErrorInfo() }
    }
}
