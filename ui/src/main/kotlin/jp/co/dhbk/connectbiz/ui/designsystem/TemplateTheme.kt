package jp.co.dhbk.connectbiz.ui.designsystem

import android.annotation.SuppressLint
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable

@SuppressLint("DesignSystem")
@Composable
internal fun TemplateTheme(content: @Composable () -> Unit) {
    MaterialTheme(
        colorScheme = TemplateColors.colorScheme,
        content = content,
    )
}

internal data object TemplateTheme {
    val colors: TemplateColors = TemplateColors
    val typography: TemplateTypography = TemplateTypography
    val animations: TemplateAnimations = TemplateAnimations
}
