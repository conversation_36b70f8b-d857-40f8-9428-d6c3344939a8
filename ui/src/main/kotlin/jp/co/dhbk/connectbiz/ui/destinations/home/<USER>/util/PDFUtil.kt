package jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.pdf.PdfDocument
import android.graphics.pdf.PdfRenderer
import android.os.ParcelFileDescriptor
import jp.co.dhbk.connectbiz.core.common.model.FileExtensions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import java.io.File
import java.io.FileOutputStream

/**
 * Util function to add page to pdf
 * page can be a file or a bitmap
 * */
internal suspend fun addImageToPdf(
    imageFilePath: String? = null,
    bitmap: Bitmap? = null,
    pdfPath: String,
) {
    withContext(Dispatchers.IO) {
        if (imageFilePath.isNullOrEmpty() && bitmap == null) {
            throw Exception("Image file or bitmap required")
        }
        val pdfFile = File.createTempFile("temp", ".${FileExtensions.PDF}")
        val actualFile = File(pdfPath)
        if (actualFile.exists() && actualFile.length() != 0L) {
            actualFile.copyTo(pdfFile, true)
        }
        val pdfDocument = PdfDocument()
        val options = BitmapFactory.Options()
        val image = if (!imageFilePath.isNullOrEmpty()) {
            val decodedBitmap = BitmapFactory.decodeFile(imageFilePath, options)
            if (decodedBitmap == null) {
                throw Exception("Failed to decode image file: $imageFilePath. File may be corrupted or not a valid image format.")
            }
            decodedBitmap
        } else {
            bitmap ?: throw Exception("Bitmap is null")
        }

        if (!actualFile.exists() || actualFile.length() == 0L) {
            val pageInfo = PdfDocument.PageInfo.Builder(image.width, image.height, 1).create()
            val page = pdfDocument.startPage(pageInfo)
            val canvas = page.canvas
            canvas.drawBitmap(image, 0f, 0f, null)
            pdfDocument.finishPage(page)
            // Save the changes to the existing or new PDF document
            val outputStream = FileOutputStream(pdfFile)
            pdfDocument.writeTo(outputStream)
            pdfDocument.close()
            image.recycle()
            if (isActive) {
                pdfFile.copyTo(actualFile, true)
            }
            return@withContext
        }
        val mrenderer = PdfRenderer(
            ParcelFileDescriptor.open(
                pdfFile,
                ParcelFileDescriptor.MODE_READ_ONLY,
            ),
        )

        for (i in 0 until mrenderer.pageCount) {
            val originalPage = mrenderer.openPage(i)

            // Create a new bitmap to draw the contents of the original page onto
            val pageBitmap = Bitmap.createBitmap(
                originalPage.width,
                originalPage.height,
                Bitmap.Config.ARGB_8888,
            )
            // Draw the contents of the original page onto the pageBitmap
            originalPage.render(pageBitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_PRINT)
            // Close the original page
            originalPage.close()

            // Create new page for pageBitmap
            val pageInfo =
                PdfDocument.PageInfo.Builder(pageBitmap.width, pageBitmap.height, i + 1)
                    .create()
            val currentPage = pdfDocument.startPage(pageInfo)

            val mCanvas = currentPage.canvas

            // Draw the pageBitmap onto the canvas of the existing page
            mCanvas.drawBitmap(pageBitmap, 0f, 0f, null)
            pageBitmap.recycle()
            pdfDocument.finishPage(currentPage)
            yield()
        }

        // Create a new page in the existing
        val pageCount = mrenderer.pageCount
        val newPage = pdfDocument.startPage(
            PdfDocument.PageInfo.Builder(
                image.width,
                image.height,
                pageCount + 1,
            ).create(),
        )
        val canvas = newPage.canvas
        // Draw the image on the canvas of the new page
        canvas.drawBitmap(image, 0f, 0f, null)

        // Finish the new page
        pdfDocument.finishPage(newPage)

        // Save the changes to the existing or new PDF document
        val outputStream = FileOutputStream(pdfFile)
        pdfDocument.writeTo(outputStream)
        if (isActive) {
            pdfFile.copyTo(actualFile, true)
        }
        // Close the PDF document and PDF renderer
        pdfDocument.close()
        mrenderer.close()
        image.recycle()
    }
}

/**
 * Util function to merge two pdf
 * imported pdf pages will get appended to old pdf
 * */
internal suspend fun mergePdf(oldPdfPath: String, importedPdfPath: String) {
    withContext(Dispatchers.IO) {
        val tempOldPdf = File.createTempFile("temp_old", ".${FileExtensions.PDF}")
        val importedPdf = File(importedPdfPath)
        File(oldPdfPath).copyTo(tempOldPdf, true)
        val pdfDocument = PdfDocument()
        var pdfDocumentPage = 1

        val oldRenderer = PdfRenderer(
            ParcelFileDescriptor.open(
                tempOldPdf,
                ParcelFileDescriptor.MODE_READ_ONLY,
            ),
        )
        val newRenderer = PdfRenderer(
            ParcelFileDescriptor.open(
                importedPdf,
                ParcelFileDescriptor.MODE_READ_ONLY,
            ),
        )

        // Load old pdf pages
        for (i in 0 until oldRenderer.pageCount) {
            val originalPage = oldRenderer.openPage(i)

            // Create a new bitmap to draw the contents of the original page onto
            val bitmap = Bitmap.createBitmap(
                originalPage.width,
                originalPage.height,
                Bitmap.Config.ARGB_8888,
            )
            // Draw the contents of the original page onto the bitmap
            originalPage.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_PRINT)
            // Close the original page
            originalPage.close()

            // Create new page for bitmap
            val pageInfo =
                PdfDocument.PageInfo.Builder(bitmap.width, bitmap.height, pdfDocumentPage)
                    .create()
            val currentPage = pdfDocument.startPage(pageInfo)

            val mCanvas = currentPage.canvas

            // Draw the bitmap onto the canvas of the existing page
            mCanvas.drawBitmap(bitmap, 0f, 0f, null)
            bitmap.recycle()
            pdfDocument.finishPage(currentPage)
            pdfDocumentPage += 1
            yield()
        }
        // Load new pdf pages
        for (i in 0 until newRenderer.pageCount) {
            val originalPage = newRenderer.openPage(i)

            // Create a new bitmap to draw the contents of the original page onto
            val bitmap = Bitmap.createBitmap(
                originalPage.width,
                originalPage.height,
                Bitmap.Config.ARGB_8888,
            )
            // Draw the contents of the original page onto the bitmap
            originalPage.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_PRINT)
            // Close the original page
            originalPage.close()

            // Create new page for bitmap
            val pageInfo =
                PdfDocument.PageInfo.Builder(bitmap.width, bitmap.height, pdfDocumentPage)
                    .create()
            val currentPage = pdfDocument.startPage(pageInfo)

            val mCanvas = currentPage.canvas

            // Draw the bitmap onto the canvas of the existing page
            mCanvas.drawBitmap(bitmap, 0f, 0f, null)
            bitmap.recycle()
            pdfDocument.finishPage(currentPage)
            pdfDocumentPage += 1
            yield()
        }
        val outputStream = FileOutputStream(tempOldPdf)
        pdfDocument.writeTo(outputStream)
        if (isActive) {
            tempOldPdf.copyTo(File(oldPdfPath), true)
        }
        // Close the PDF document and PDF renderer
        pdfDocument.close()
        oldRenderer.close()
        newRenderer.close()
    }
}

/*
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun Modifier.pinchToZoomAndDrag(): Modifier {
    // Get screen size within the composable context
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp.value * 1.2f
    val screenHeight = configuration.screenHeightDp.dp.value * 1.2f

    // Use remember to keep track of the zoom, offsetX, and offsetY
    val zoom = remember { mutableFloatStateOf(1f) }
    val offsetX = remember { mutableFloatStateOf(0f) }
    val offsetY = remember { mutableFloatStateOf(0f) }
    val angle = remember { mutableFloatStateOf(0f) }
    val pI = 3.14f

    return this.then(
        Modifier.pointerInput(Unit) {
            detectTransformGestures { _, pan, gestureZoom, _ ->
                zoom.value = (zoom.value * gestureZoom).coerceIn(1f..4f)

                if (zoom.value > 1) {
                    val x = pan.x * zoom.value
                    val y = pan.y * zoom.value
                    val angleRad = angle.value * pI / 180.0f

                    offsetX.value = (offsetX.value + (x * cos(angleRad) - y *
                            sin(angleRad)).toFloat())
                        .coerceIn(-(screenWidth * zoom.value)..(screenWidth * zoom.value))
                    offsetY.value = (offsetY.value + (x * sin(angleRad) + y *
                            cos(angleRad)).toFloat()).coerceIn(-(screenHeight * zoom.value)..
                    (screenHeight * zoom.value))
                } else {
                    offsetX.value = 0f
                    offsetY.value = 0f
                }
            }
        },
    ).then(
        Modifier.combinedClickable(
            onClick = {},
            onDoubleClick = {
                // Double-tap to zoom
                zoom.value = if (zoom.value > 1f) {
                    1f // Reset zoom to 1x
                } else {
                    3f // Zoom in to 3x
                }
            },
        ),
    ).graphicsLayer(
        scaleX = zoom.value,
        scaleY = zoom.value,
        translationX = offsetX.value,
        translationY = offsetY.value,
        rotationZ = angle.value,
    )
}
*/
