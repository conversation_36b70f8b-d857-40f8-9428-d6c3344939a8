package jp.co.dhbk.connectbiz.ui.designsystem

import androidx.compose.ui.tooling.preview.Preview

internal object PreviewInfo {
    // この命令によって、Preview テストの処理が変更される
    object Names {
        const val COMPONENT = "Component"
        const val SCREEN = "Screen"
        const val DIALOG = "Dialog"
        const val MODAL_BOTTOM_SHEET = "ModalBottomSheet"
    }

    const val DEVICE = "spec:width=411dp,height=914dp,dpi=420" // Pixel 7
    const val BACKGROUND_COLOR = 0xFFFFFFFF // TODO: 仮
    const val API_LEVEL = 33
}

@Suppress("ktlint:compose:preview-annotation-naming") // 単一なので複数形にしない
@Preview(
    name = PreviewInfo.Names.COMPONENT,
    apiLevel = PreviewInfo.API_LEVEL,
)
internal annotation class ComponentPreview

@Suppress("ktlint:compose:preview-annotation-naming") // 単一なので複数形にしない
@Preview(
    name = PreviewInfo.Names.SCREEN,
    device = PreviewInfo.DEVICE,
    backgroundColor = PreviewInfo.BACKGROUND_COLOR,
    showBackground = true,
    apiLevel = PreviewInfo.API_LEVEL,
)
internal annotation class ScreenPreview

@Suppress("ktlint:compose:preview-annotation-naming") // 単一なので複数形にしない
@Preview(
    name = PreviewInfo.Names.DIALOG,
    device = PreviewInfo.DEVICE,
    apiLevel = PreviewInfo.API_LEVEL,
)
internal annotation class DialogPreview

@Suppress("ktlint:compose:preview-annotation-naming") // 単一なので複数形にしない
@Preview(
    name = PreviewInfo.Names.MODAL_BOTTOM_SHEET,
    device = PreviewInfo.DEVICE,
    apiLevel = PreviewInfo.API_LEVEL,
)
internal annotation class ModalBottomSheetPreview
