package jp.co.dhbk.connectbiz.ui.navigation

import jp.co.dhbk.connectbiz.domain.model.error.ConnectbizError

data class ErrorInfo(
    val message: String? = null,
    val errorState: ErrorState = ErrorState.None, // Use sealed class
    val piError: ConnectbizError? = null,
    val action: (() -> Unit)? = null,
)

sealed class ErrorState {
    object None : ErrorState()

    object NetworkError : ErrorState()

    object SystemError : ErrorState()

    object GeneralError : ErrorState()

    data class ServerFail(val message: String) : ErrorState()
}
