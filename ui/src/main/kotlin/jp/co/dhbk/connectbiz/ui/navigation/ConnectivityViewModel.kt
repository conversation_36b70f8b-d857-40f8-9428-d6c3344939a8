package jp.co.dhbk.connectbiz.ui.navigation

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.net.HttpURLConnection
import java.net.URL
import javax.inject.Inject

@HiltViewModel
class ConnectivityViewModel @Inject constructor(
    @ApplicationContext private val context: Context, // Inject application context
) : ViewModel() {

    private val _isConnected = MutableStateFlow(false) // Default to connected
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            _isConnected.value = isInternetAvailable() // Check if internet is available
            Timber.e("Network Available, internet status: ${_isConnected.value}")
        }

        override fun onLost(network: Network) {
            _isConnected.value = false // No network available
            Timber.e("Network Lost, internet status: ${_isConnected.value}")
        }
    }

    init {
        Timber.e("ConnectivityViewModel initialized")
        registerNetworkCallback()
        // Initialize the connection state
        updateInitialConnectionState()
    }

    private fun updateInitialConnectionState() {
        viewModelScope.launch {
            _isConnected.value = isInternetAvailable() // Check initial internet availability
            Timber.e("Initial connectivity state: ${_isConnected.value}")
        }
    }

    private fun registerNetworkCallback() {
        val networkRequest = NetworkRequest.Builder().build()
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }

    override fun onCleared() {
        super.onCleared()
        connectivityManager.unregisterNetworkCallback(networkCallback)
        Timber.e("ConnectivityViewModel cleared")
    }

    /**
     * Checks if the device has a network connection and whether internet access is available.
     */
    fun isInternetAvailable(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        // Check for network capability
        if (!capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
            return false
        }

        var isReachable = true
        viewModelScope.launch {
            isReachable = isInternetReachable()
            Timber.e("Is internet reachable? $isReachable")
        }
        // Optional: Ping a reliable server to confirm actual internet access
        return isReachable
    }

    /**
     * Helper function to check if the internet is reachable by pinging a known server.
     */
    private suspend fun isInternetReachable(): Boolean {
        return withContext(Dispatchers.IO) { // Run in background (IO thread)
            try {
                val url = URL("https://www.google.com")
                val connection = url.openConnection() as HttpURLConnection
                connection.apply {
                    requestMethod = "GET"
                    connectTimeout = 1500
                    readTimeout = 1500
                }
                connection.connect()
                connection.responseCode == HttpURLConnection.HTTP_OK
            } catch (e: Exception) {
                Timber.e("Error checking network availability: ${e.message}")
                false
            }
        }
    }
}
