package jp.co.dhbk.connectbiz.ui.destinations.home.webview
import android.annotation.SuppressLint
import android.content.Context
import android.os.Message
import android.view.ViewGroup
import android.webkit.JsPromptResult
import android.webkit.JsResult
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient

/**
 * SSO画面で表示するWebViewのWebChromeClient
 *
 * @param context [Context]
 * @param onCreateWindow 新規ウィンドウでページ作成された
 * @param onCloseWindow 新規ウィンドウが閉じられた（JSの"window.close"が呼ばれた時も）
 * @param onDialogCalled ダイアログ表示が呼ばれた
 * @param accessToken アクセストークン
 */
class MediaWebChromeClient(
    private val context: Context,
    private val onCreateWindow: (WebView) -> Unit,
    private val onCloseWindow: () -> Unit,
    private val onDialogCalled: () -> Unit,
    private val accessToken: String,
) : WebChromeClient() {
    override fun onJsPrompt(
        view: WebView?,
        url: String?,
        message: String?,
        defaultValue: String?,
        result: JsPromptResult?,
    ): Boolean {
        return false
    }

    override fun onJsAlert(
        view: WebView?,
        url: String?,
        message: String?,
        result: JsResult?,
    ): Boolean {
        return false
    }

    override fun onJsConfirm(
        view: WebView?,
        url: String?,
        message: String?,
        result: JsResult?,
    ): Boolean {
        return false
    }

    /**
     * 新規ウィンドウでのページ遷移時に呼ばれる
     */
    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreateWindow(
        view: WebView?,
        isDialog: Boolean,
        isUserGesture: Boolean,
        resultMsg: Message?,
    ): Boolean {
        if (resultMsg?.obj == null || resultMsg.obj !is WebView.WebViewTransport) return true
        // 新規ウィンドウを新しいWebViewとして作成
        val transport = resultMsg.obj as WebView.WebViewTransport
        val windowWebView = WebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
            )

            settings.apply {
                settings.javaScriptEnabled = true
                setSupportMultipleWindows(true)
                javaScriptCanOpenWindowsAutomatically = true
                mediaPlaybackRequiresUserGesture = false
            }
            webViewClient = WebViewClient()
            webChromeClient = object : WebChromeClient() {
                // 新規ウィンドウが閉じられた時
                override fun onCloseWindow(window: WebView?) {
                    onCloseWindow()
                    // nullを設定しないとonCloseWindowが繰り返し呼ばれてしまう
                    webChromeClient = null
                }
            }
            // PDFページは新規ウィンドウで呼ばれるのでここで定義する
            setDownloadListener(
                DownloadListener(
                    context = context,
                    accessToken = accessToken,
                    onPdfFailed = onDialogCalled,
                    onCloseWindow = onCloseWindow,
                ),
            )
        }
        transport.webView = windowWebView
        resultMsg.sendToTarget()
        onCreateWindow(windowWebView)
        return true
    }
}
