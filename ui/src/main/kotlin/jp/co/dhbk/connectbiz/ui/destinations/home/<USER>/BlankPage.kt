package jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun BlankPage(width: Int, height: Int, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .size(
                width = width.dp,
                height = height.dp,
            )
            .background(color = Color.White),
    )
}
