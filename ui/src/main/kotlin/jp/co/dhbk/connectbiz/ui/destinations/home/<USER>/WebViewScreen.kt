package jp.co.dhbk.connectbiz.ui.destinations.home.webview

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.util.Base64
import android.view.View
import android.view.ViewGroup
import android.webkit.ConsoleMessage
import android.webkit.CookieManager
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebChromeClient.FileChooserParams
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.FileProvider
import androidx.hilt.navigation.compose.hiltViewModel
import jp.co.dhbk.connectbiz.ui.destinations.home.HomeViewModel
import jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview.RequestInspectorJavaScriptInterface
import jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview.RequestInspectorWebViewClient
import jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview.WebViewRequest
import jp.co.dhbk.connectbiz.ui.destinations.home.utils.FileDownloadHelper
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState
import org.json.JSONObject
import timber.log.Timber
import java.io.File
import kotlin.collections.component1
import kotlin.collections.component2

/**
 * SSO表示用のWebView
 */
@Deprecated("Use AndroidWebViewScreen instead of WebView.")
@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewScreen(
    firstViewUrl: String,
    accessToken: String,
    navigateUp: () -> Unit,
    onLogout: () -> Unit,
    onSettings: () -> Unit,
    onReceiveIdToken: (String) -> Unit,
    onUrlChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    homeViewModel: HomeViewModel = hiltViewModel(),
) {
    var isBackPressed by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(true) }
    var onTrailingButtonClick: () -> Unit by remember { mutableStateOf({}) }
    var windowWebView: WebView? by remember { mutableStateOf(null) }
    var isWindowWebViewVisible: Boolean by remember { mutableStateOf(false) }
    val currentUrl = remember { mutableStateOf(firstViewUrl) }
    var isPageLoading by remember { mutableStateOf(false) }
    var filePathCallback by remember { mutableStateOf<ValueCallback<Array<Uri>>?>(null) }
    val activityLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data?.data
            filePathCallback?.onReceiveValue(arrayOf(data!!)) // Pass the selected file
        } else {
            filePathCallback?.onReceiveValue(null)
        }
        filePathCallback = null // Clear after handling
    }
    var downloadProgress by remember { mutableIntStateOf(0) }
    var isDownloading by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val webView = remember { WebView(context) }

    Box(
        modifier = modifier
            .fillMaxSize(),
    ) {
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { context ->
                webView.apply {
                    settings.apply {
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        javaScriptCanOpenWindowsAutomatically = true
                        mediaPlaybackRequiresUserGesture = false
                        useWideViewPort = true
                        allowFileAccess = true
                        safeBrowsingEnabled = true
                        allowContentAccess = true
                        loadWithOverviewMode = true
                        mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        userAgentString = homeViewModel.getUserAgentJson()
                        setSupportZoom(false)
                        builtInZoomControls = true
                        setSupportMultipleWindows(false)
                        setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    }

                    WebView.setWebContentsDebuggingEnabled(true)
                    // Load saved session cookies before loading URL
                    webView.addJavascriptInterface(
                        RequestInspectorJavaScriptInterface(webView, onReceiveIdToken),
                        "RequestInspection",
                    )

                    homeViewModel.saveCookies(homeViewModel.apiConfig.baseUrl)
                    // Handle file uploads
                    val fileUploadWebChromeClient = object : WebChromeClient() {
                        override fun onShowFileChooser(
                            webView: WebView?,
                            newFilePathCallback: ValueCallback<Array<Uri>>?,
                            fileChooserParams: FileChooserParams?,
                        ): Boolean {
                            filePathCallback?.onReceiveValue(null)
                            filePathCallback = newFilePathCallback
                            GlobalAppState.isFileSelectionInProgress = true
                            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                                addCategory(Intent.CATEGORY_OPENABLE)
                                type = "*/*"
                            }
                            activityLauncher.launch(intent)
                            return true
                        }

                        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                            Timber.d(
                                "%snull",
                                "JS Console: ${consoleMessage?.message()} -- From line " +
                                    "${consoleMessage?.lineNumber()} of ",
                            )
                            return true
                        }
                    }

                    val mediaPlayBackWebChromeClient = MediaWebChromeClient(
                        context = context,
                        onCreateWindow = { webView ->
                            windowWebView = webView
                            isWindowWebViewVisible = true
                        },
                        onCloseWindow = {
                            isWindowWebViewVisible = false
                            windowWebView = null
                        },
                        onDialogCalled = {},
                        accessToken = "",
                    )

                    setDownloadListener { url, _, contentDisposition, mimeType, _ ->

                        isDownloading = true
                        try {
                            fun handleDownloadAndOpen(
                                url: String,
                                mimeType: String,
                                contentDisposition: String,
                            ) {
                                FileDownloadHelper.handleFileDownload(
                                    url,
                                    mimeType,
                                    contentDisposition,
                                    homeViewModel,
                                    onProgressUpdate = { progress ->
                                        downloadProgress = progress
                                        isDownloading = progress < 100
                                    },
                                    onDownloadComplete = { filePath , fileName->
                                        isDownloading = false
                                        // Determine file type and use PDF viewer for PDFs and images
                                        val fileType = when {
                                            mimeType.contains("image", ignoreCase = true) -> "image"
                                            mimeType.contains("pdf", ignoreCase = true) -> "pdf"
                                            else -> {
                                                // Fallback to file extension detection
                                                val extension = filePath.substringAfterLast('.', "").lowercase()
                                                when (extension) {
                                                    "jpg", "jpeg", "png", "gif", "bmp", "webp" -> "image"
                                                    "pdf" -> "pdf"
                                                    else -> "auto"
                                                }
                                            }
                                        }

                                        if (fileType == "image" || fileType == "pdf") {
                                            // Use PDF viewer for images and PDFs
                                            homeViewModel.navigateToPdfViewer(filePath, isRemote = false, fileType = fileType)
                                        } else {
                                            // Use external app for other file types
                                            openFileInApp(context, filePath)
                                        }
                                    },
                                    onDownloadFailed = { message ->
                                        isDownloading = false
                                        Timber.e(message)
                                    },
                                )
                            }

                            when {
                                url.contains(
                                    "MessageFileDownload",
                                    ignoreCase = true,
                                ) || url.contains(
                                    "Download",
                                    ignoreCase = true,
                                ) || url.endsWith(
                                    ".csv",
                                    ignoreCase = true,
                                ) || mimeType.contains(
                                    "text/csv",
                                    ignoreCase = true,
                                ) || mimeType.contains(
                                    "image",
                                    ignoreCase = true,
                                ) || mimeType.contains(
                                    "application/vnd.ms-excel",
                                    ignoreCase = true,
                                ) || mimeType.contains(
                                    "application/vnd.openxmlformats-officedocument" +
                                        ".spreadsheetml.sheet",
                                    ignoreCase = true,
                                ) || url.endsWith(
                                    ".xls",
                                    ignoreCase = true,
                                ) || url.endsWith(
                                    ".xlsx",
                                    ignoreCase = true,
                                ) || url.endsWith(
                                    ".xlsm",
                                    ignoreCase = true,
                                ) || url.endsWith(
                                    ".doc",
                                    ignoreCase = true,
                                ) || url.endsWith(".docx", ignoreCase = true) || mimeType.contains(
                                    "application/msword",
                                    ignoreCase = true,
                                ) || mimeType.contains(
                                    "application/vnd.openxmlformats-officedocument." +
                                        "wordprocessingml.document",
                                    ignoreCase = true,
                                ) -> {
                                    handleDownloadAndOpen(url, mimeType, contentDisposition)
                                }

                                url.contains(
                                    "Base/OpenTerms",
                                    ignoreCase = true,
                                ) && mimeType.contains("application/pdf", ignoreCase = true) -> {
                                    homeViewModel.navigateToPdfViewer(url)
                                }
                                url.contains(
                                    "operationGuide",
                                    ignoreCase = true,
                                ) && mimeType.contains("application/pdf", ignoreCase = true) -> {
                                    homeViewModel.navigateToPdfViewer(url)
                                }

                                else -> {
                                    Timber.e("Unsupported File Mimetype: $mimeType")
                                }
                            }
                        } catch (e: Exception) {
                            isDownloading = false
                            Timber.e("Error handling download: ${e.message}")
                        }
                    }

                    webViewClient = object : RequestInspectorWebViewClient(
                        this@apply,
                        onReceiveIdToken,
                    ) {
                        override fun shouldInterceptRequest(
                            view: WebView,
                            webViewRequest: WebViewRequest,
                        ): WebResourceResponse? {
                            isPageLoading = false
                            webViewRequest.url.let {
                                currentUrl.value = it
                                onUrlChange(it)
                                if (it.contains(
                                        homeViewModel.apiConfig.baseUrl +
                                            "MicrosoftIdentity/MyAccount/SignOut",
                                    )
                                ) {
                                    homeViewModel.handleLogout()
                                }
                            }

                            if (webViewRequest.url.startsWith(
                                    homeViewModel.apiConfig.baseUrl + "signin-oidc",
                                )
                            ) {
                                webViewRequest.formParameters.forEach { (key, value) ->
                                    if (key == "id_token") {
                                        onReceiveIdToken(value)
                                        val jwtPayload = value.split(".").getOrElse(1) { "" }
                                        val json = String(Base64.decode(jwtPayload, Base64.DEFAULT))
                                        val jsonObject = JSONObject(json)
                                        val userId = jsonObject.getString("sub")
                                        Timber.e("userId $userId")
                                    }
                                }
                            } else if (webViewRequest.url.contains(
                                    homeViewModel.apiConfig.mfaEnable,
                                )
                            ) {
                                Timber.e(
                                    "Login Session out Please try login ${
                                        webViewRequest.url
                                    }",
                                )
                                onLogout
                            }

                            webViewRequest.headers.forEach { (key, value) ->
                                // Timber.d("Header: $key = $value")
                            }

                            // Check cookies
                            val cookieManager = CookieManager.getInstance()
                            val cookies = cookieManager.getCookie(webViewRequest.url)
                            // Timber.d("Cookies for URL ${webViewRequest.url}: $cookies")

                            return super.shouldInterceptRequest(view, webViewRequest)
                        }

                        override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                            super.onPageStarted(view, url, favicon)
                            isPageLoading = true
                        }

                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            homeViewModel.saveLastVisitedUrl(url.toString())
                            isPageLoading = false
                            webView.evaluateJavascript(
                                """
            document.querySelectorAll('video').forEach(video => {
                video.muted = false;
                video.play();
            });
                                """.trimIndent(),
                                null,
                            )
                        }

                        override fun shouldOverrideUrlLoading(
                            view: WebView?,
                            request: WebResourceRequest?,
                        ): Boolean {
                            request?.url?.let { url ->
                                Timber.e("Loading url $url")
                                val internalDomains = listOf(
                                    homeViewModel.apiConfig.baseUrl,
                                    homeViewModel.apiConfig.mfaEnable,
                                    "https://www.payslip-test.lifeing-board.com/payslip/index",
                                    "https://www.payslip.dhbk-connect-biz.com/payslip/index",
                                )
                                when {
                                    url.toString().contains("exclude-this-resource") -> {
                                        Timber.d("WebView Skipping URL: $url")
                                        return true
                                    }
                                    url.toString().contains(
                                        "app/settings",
                                        ignoreCase = true,
                                    ) -> {
                                        onSettings()
                                        return true
                                    }
                                   /* // Handling Payslip
                                    url.toString().contains("payslip/index") || url.toString()
                                        .contains("manager/home") -> {
                                        return true
                                    }*/
                                    // Handle telephone links
                                    url.scheme == "tel" -> {
                                        val intent = Intent(Intent.ACTION_DIAL, url)
                                        context.startActivity(intent)
                                        return true
                                    }

                                    url.toString().contains(
                                        "Board/Register",
                                        ignoreCase = true,
                                    ) -> {
                                        view?.post {
                                            Timber.e("Webview Settings applied for Board URL")
                                            enableMultipleWindows(view, false)
                                        }
                                        webView.webChromeClient = fileUploadWebChromeClient
                                        view?.loadUrl(url.toString())
                                        return true
                                    }

                                    url.toString().contains("video", ignoreCase = true) -> {
                                        view?.post {
                                            Timber.e("Webview Settings applied for Video URL")
                                            enableMultipleWindows(view, true)
                                        }
                                        webView.webChromeClient = mediaPlayBackWebChromeClient
                                        webView.loadUrl(url.toString())
                                        return false
                                    }

                                    url.toString().endsWith(".pdf", ignoreCase = true) -> {
                                        val browserIntent = Intent(
                                            Intent.ACTION_VIEW,
                                            Uri.parse(url.toString()),
                                        ).apply {
                                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        }
                                        view?.context?.startActivity(browserIntent)
                                        return true
                                    }

                                    url.toString()
                                        .contains(homeViewModel.apiConfig.baseUrl) ||
                                        url.toString().contains(
                                            homeViewModel
                                                .apiConfig.mfaEnable,
                                        ) || url.toString()
                                            .contains(
                                                "policy/privacy_policy_1.html",
                                            ) -> {
                                        view?.loadUrl(url.toString())
                                        return false
                                    }

                                    url.toString().contains("about:blank") -> {
                                        homeViewModel.receiveUrl(url.toString())
                                        return true
                                    }

                                    url.toString().contains(
                                        "MicrosoftIdentity/" + "MyAccount/SignOut",
                                        ignoreCase = true,
                                    ) -> {
                                        homeViewModel.handleLogout()
                                        onLogout() // Trigger logout
                                        return true
                                    }
                                    // Handle external URLs
                                    isExternalUrl(url, internalDomains) -> {
                                        openUrlInExternalBrowser(
                                            context, url.toString(),
                                            homeViewModel,
                                        )
                                        return true
                                    }

                                    else -> {
                                        // Call the method to check content type and handle accordingly
                                        return false
                                    }
                                }
                            }
                            return false
                        }

                        override fun onReceivedHttpError(
                            view: WebView?,
                            request: WebResourceRequest?,
                            errorResponse: WebResourceResponse?,
                        ) {
                            super.onReceivedHttpError(view, request, errorResponse)
                            Timber.e("HTTP error: ${errorResponse?.statusCode}")
                        }
                    }

                    // レイアウトサイズを指定しないと、WebView内で表示させるモーダルのサイズが小さくなる
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT,
                    )
                }.also {
                    // カスタムヘッダーをセット
                    val extraHeaders: MutableMap<String, String> = HashMap()
                    extraHeaders["access_token"] = accessToken
                    it.loadUrl(firstViewUrl, extraHeaders)
                }
            },
            update = { webView ->
                if (isBackPressed) {
                    isBackPressed = false
                    if (webView.canGoBack()) {
                        webView.goBack()
                    } else {
                        navigateUp()
                    }
                }
                // ヘッダー右上のボタン押下時の処理を、ローディング状態に応じてセットする
                onTrailingButtonClick = if (isLoading) {
                    webView::stopLoading
                } else {
                    {
                        isLoading = true
                        // 最初に流入した画面（forward_idのクエリ付きURL）のみJSの更新メソッドが効かないので分岐させる
                        if (webView.url == firstViewUrl) {
                            webView.reload()
                        } else {
                            webView.loadUrl("javascript:location.reload(false)")
                        }
                    }
                }
            },
        )
        WindowWebView(
            webView = windowWebView,
            isVisible = isWindowWebViewVisible,
        )
    }

    BackHandler(enabled = true) {
        isBackPressed = true
    }

    if (isDownloading || isPageLoading) {
        CustomLoadingScreen(
            modifier = Modifier.fillMaxSize()
                .background(Color.White.copy(alpha = 0.7f)),
        )
    }

    DisposableEffect(webView) {
        onDispose {
            webView.removeJavascriptInterface("RequestInspection")
        }
    }
}

/**
 * 新規ウィンドウ遷移で表示させるWebView
 *
 * @param webView [WebView]
 * @param isVisible 表示/非表示
 */
@Composable
private fun WindowWebView(webView: WebView?, isVisible: Boolean) {
    if (isVisible.not() || webView == null) return
    // スワイプバック or 端末バックキーが押されたかどうか
    var isBackPressed by remember { mutableStateOf(false) }
    AndroidView(
        modifier = Modifier.fillMaxSize(),
        factory = {
            webView
        },
        update = {
            if (isBackPressed) {
                isBackPressed = false
                if (it.canGoBack()) {
                    it.goBack()
                } else {
                    it.loadUrl("javascript:window.close()")
                }
            }
        },
    )
    BackHandler(enabled = true) {
        isBackPressed = true
    }
}

@SuppressLint("QueryPermissionsNeeded")
fun openFileInApp(context: Context, filePath: String) {
    val file = File(filePath)
    if (!file.exists()) {
        Timber.e("File does not exist: $filePath")
        return
    }

    val fileUri: Uri = FileProvider.getUriForFile(
        context,
        "${context.packageName}.fileprovider",
        file,
    )

    // Determine MIME type based on file extension
    val mimeType = when (file.extension.lowercase()) {
        "jpg", "jpeg", "png", "gif" -> "image/*"
        "pdf" -> "application/pdf"
        "csv" -> "text/csv"
        "xls", "xlsx" -> "application/vnd.ms-excel"
        "doc", "docx" -> "application/msword"
        "ppt", "pptx" -> "application/vnd.ms-powerpoint"
        "txt" -> "text/plain"
        else -> "*/*" // Fallback for other file types
    }

    val intent = Intent(Intent.ACTION_VIEW).apply {
        setDataAndType(fileUri, mimeType)
        flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
    }

    if (intent.resolveActivity(context.packageManager) != null) {
        context.startActivity(intent)
    } else {
        Timber.e("No application found to open files of type: $mimeType")
    }
}

private fun enableMultipleWindows(webView: WebView?, enable: Boolean) {
    webView?.apply {
        settings.apply {
            setSupportMultipleWindows(enable)
            javaScriptCanOpenWindowsAutomatically = enable
            mediaPlaybackRequiresUserGesture = false
        }
        // Enable hardware acceleration for smoother performance
        setLayerType(View.LAYER_TYPE_HARDWARE, null)
    }
    // Log for debugging
    Timber.d("Multiple windows enabled: $enable")
}

private fun isExternalUrl(url: Uri, internalDomains: List<String>): Boolean {
    val host = url.host
    if (host == null) return true
    return internalDomains.none { internalDomain ->
        val internalDomainHost = Uri.parse(internalDomain).host
        internalDomainHost != null && host.contains(internalDomainHost)
    }
}

private fun openUrlInExternalBrowser(context: Context, url: String, homeViewModel: HomeViewModel) {
    Timber.e(url)

    // Check if it's a PDF or image file
    val extension = url.substringAfterLast('.', "").lowercase()
    when (extension) {
        "pdf" -> {
            Timber.e("Opening PDF: ${Uri.parse(url)}")
            homeViewModel.navigateToPdfViewer(url, isRemote = true, fileType = "pdf")
        }
        "jpg", "jpeg", "png", "gif" -> {
            Timber.e("Opening image: ${Uri.parse(url)}")
            homeViewModel.navigateToPdfViewer(url, isRemote = true, fileType = "image")
        }
        else -> {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            context.startActivity(intent)
        }
    }
}
