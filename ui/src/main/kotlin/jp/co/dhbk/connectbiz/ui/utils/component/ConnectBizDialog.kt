package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import jp.co.dhbk.connectbiz.ui.designsystem.Dimen.doubleSpace
import jp.co.dhbk.connectbiz.ui.designsystem.Dimen.halfSpace
import jp.co.dhbk.connectbiz.ui.designsystem.Dimen.space
import jp.co.dhbk.connectbiz.ui.designsystem.Dimen.tripleSpace
import jp.co.dhbk.connectbiz.ui.designsystem.ScreenPreview
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.PrimaryLight
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography

@Composable
fun PositiveButton(
    text: String,
    onClick: () -> Unit,
    padding: Dp,
    modifier: Modifier = Modifier,
    buttonColor: Color = MaterialTheme.colorScheme.primary,
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        colors = ButtonDefaults.buttonColors(containerColor = buttonColor),
    ) {
        Text(text = text, Modifier.padding(padding), color = Color.White)
    }
}

@Composable
fun NegativeButton(
    text: String,
    onClick: () -> Unit,
    padding: Dp,
    textColor: Color,
    modifier: Modifier = Modifier,
) {
    TextButton(
        onClick = onClick,
        modifier = modifier,
    ) {
        Text(text = text, modifier = Modifier.padding(padding), color = textColor)
    }
}

fun Modifier.connectBizShadow(
    elevation: Dp = doubleSpace,
    shape: Shape = RoundedCornerShape(elevation),
): Modifier = this
    .shadow(elevation = elevation, shape = shape, clip = false)
    .clip(shape)

@Composable
fun ConnectBizDialog(
    message: String,
    title: String? = null,
    enableCancel: Boolean = false,
    negativeLabel: String? = null,
    positiveLabel: String? = null,
    onClick: (index: Int) -> Unit,
) {
    val isDisplay = remember { mutableStateOf(true) }
    if (isDisplay.value) {
        Dialog(
            onDismissRequest = {
                if (enableCancel) {
                    isDisplay.value = false
                }
            },
        ) {
            Surface(modifier = Modifier.connectBizShadow()) {
                Column(
                    modifier = Modifier.padding(tripleSpace),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    title?.let {
                        Text(
                            text = it,
                            style = TextStyle(
                                fontSize = TemplateTypography.SizeM_iOS,
                                fontWeight = FontWeight.SemiBold,
                                lineHeight = TemplateTypography.SizeM_iOS *
                                    TemplateTypography.LineHeight160,
                                textAlign = TextAlign.Center,
                            ),
                        )
                        Spacer(modifier = Modifier.height(space))
                    }

                    Text(
                        text = message,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                    )
                    Spacer(modifier = Modifier.height(doubleSpace))

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        if (negativeLabel != null) {
                            NegativeButton(
                                onClick = {
                                    isDisplay.value = false
                                    onClick.invoke(0)
                                },
                                text = negativeLabel,
                                textColor = PrimaryLight,
                                padding = halfSpace,
                                modifier =
                                Modifier
                                    .weight(1f)
                                    .padding(
                                        top = halfSpace,
                                        end = if (positiveLabel != null) {
                                            doubleSpace
                                        } else {
                                            0.dp
                                        },
                                    ),
                            )
                        }

                        if (positiveLabel != null) {
                            PositiveButton(
                                onClick = {
                                    isDisplay.value = false
                                    onClick.invoke(1)
                                },
                                text = positiveLabel,
                                padding = halfSpace,
                                buttonColor = PrimaryLight,
                                modifier =
                                Modifier
                                    .weight(1f)
                                    .padding(top = halfSpace),
                            )
                        }
                    }
                }
            }
        }
    }
}

@ScreenPreview
@Composable
private fun ConnectBizDialogPreview() {
    ConnectBizDialog(
        title = "Sample Dialog Title",
        message = "This is a sample dialog message to showcase the dialog preview.",
        enableCancel = true,
        negativeLabel = "Cancel",
        positiveLabel = "OK",
        onClick = { index ->
            // Handle click events for positive (1) and negative (0) buttons in the preview
            if (index == 0) {
                println("Negative button clicked")
            } else if (index == 1) {
                println("Positive button clicked")
            }
        },
    )
}
