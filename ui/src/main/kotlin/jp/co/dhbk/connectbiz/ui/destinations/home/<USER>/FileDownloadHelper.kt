package jp.co.dhbk.connectbiz.ui.destinations.home.utils

import jp.co.dhbk.connectbiz.ui.destinations.home.HomeViewModel

/**
 * This object provides helper functions for handling file downloads.
 * It simplifies the process of downloading files by encapsulating
 * the logic in a reusable manner. It uses the HomeViewModel
 * to manage the download process and report progress.
 */

object FileDownloadHelper {
    fun handleFileDownload(
        url: String,
        mimeType: String,
        contentDisposition: String,
        homeViewModel: HomeViewModel,
        onProgressUpdate: (Int) -> Unit,
        onDownloadComplete: (filePath: String, fileName: String) -> Unit,
        onDownloadFailed: (String) -> Unit,
    ) {
        homeViewModel.handleFileDownload(url, mimeType, contentDisposition, {
                progress ->
            onProgressUpdate(progress) // Update progress in the calling context
        }, onDownloadComplete, onDownloadFailed)
    }
}
