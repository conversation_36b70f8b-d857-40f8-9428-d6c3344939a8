package jp.co.dhbk.connectbiz.ui.destinations.settings

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.analytics.ConnectbizAnalyticConstant
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import jp.co.dhbk.connectbiz.usecase.home.ClearUserSessionUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SettingViewModel
@Inject
constructor(
    private val navigator: Navigator,
    private val connectbizPreference: ConnectbizPreference,
    private val clearUserSessionUseCase: ClearUserSessionUseCase,
) : ViewModel() {

    // Combine states into one data class
    data class SettingState(
        val isBiometricEnabled: Boolean = false,
        val isLoading: Boolean = false,
    )

    // Expose state as a mutable state flow
    private val _state = MutableStateFlow(SettingState())
    val state: StateFlow<SettingState> = _state

    // Navigation Actions
    fun goBack() {
        Timber.e(
            "navigationStack after backpress in current screen ${
                navigator.getNavigationStack
                    ()
            }",
        )
        if (navigator.getNavigationStack().isNotEmpty()) {
            navigator.navigateToBack()
        } else {
            Timber.d("Navigation stack is empty, navigating to Home screen.")
            navigator.navigateToSplash()
        }
    }

    fun navigateToLicence() {
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true)
            navigator.navigateTo(
                route = Route.Settings.LICENSES,
                screenName = ConnectbizAnalyticConstant.Page.LICENSES,
                popUpTo = Route.Settings.SETTINGS,
                inclusive = false,
            )
            _state.value = _state.value.copy(isLoading = false)
        }
    }

    fun navigateAuthScreen() {
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true)
            navigator.navigateTo(
                route = Route.Settings.AUTHENTICATIONSETTINGS,
                screenName = ConnectbizAnalyticConstant.Page.AUTHENTICATION_SETTINGS,
                popUpTo = Route.Settings.SETTINGS,
                inclusive = false,
            )
            _state.value = _state.value.copy(isLoading = false)
        }
    }

    fun navigateToSettings(context: Context) {
        try {
            val intent = Intent().apply {
                action = android.provider.Settings.ACTION_APP_NOTIFICATION_SETTINGS
                putExtra(android.provider.Settings.EXTRA_APP_PACKAGE, context.packageName)
            }
            context.startActivity(intent)
            Timber.e("Navigated to Notification Settings")
        } catch (e: Exception) {
            Timber.e("Failed to navigate to Notification Settings: ${e.localizedMessage}")
            // Fallback: Open general app settings if specific notification settings aren't accessible
            val fallbackIntent = Intent().apply {
                action = android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                data = Uri.parse("package:${context.packageName}")
            }
            context.startActivity(fallbackIntent)
        }
    }

    fun sessionLogout() {
        viewModelScope.launch {
            clearUserSessionUseCase.invoke()
        }
        navigator.navigateToSplash()
        navigator.navigationStackClear()
    }
}
