package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.foundation.Image
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography

// TODO: Provisional Implementation
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GlobalTopAppBar(text: String, onNavigateBack: () -> Unit, modifier: Modifier = Modifier) {
    TopAppBar(
        title = {
            Text(
                text = text,
                color = TemplateColors.TextPrimaryLight,
                style = TemplateTypography.title4Medium,
            )
        },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Image(
                    painter = painterResource(R.drawable.arrow_back),
                    contentDescription = "Back",
                    colorFilter = ColorFilter.tint(TemplateColors.TextPrimaryLight),
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors().copy(
            containerColor = TemplateColors.BackgroundLight,
        ),
    )
}
