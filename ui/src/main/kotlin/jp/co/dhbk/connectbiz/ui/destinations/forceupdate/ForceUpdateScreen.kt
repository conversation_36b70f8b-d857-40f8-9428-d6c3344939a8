package jp.co.dhbk.connectbiz.ui.destinations.forceupdate

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.union
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.ButtonTextPrimary
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.TextPrimaryLight
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTheme
import jp.co.dhbk.connectbiz.ui.destinations.maintenance.MaintenanceViewModel
import jp.co.dhbk.connectbiz.ui.navigation.ConnectivityViewModel
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalButton
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalImageDisplay
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTextView
import timber.log.Timber

@Composable
fun ForceUpdateScreen(
    forceUpdateMessage: String,
    destinationLink: String,
    modifier: Modifier = Modifier,
    maintenanceViewModel: MaintenanceViewModel = hiltViewModel(),
    connectivityViewModel: ConnectivityViewModel = hiltViewModel(),
) {
    // Main Box to hold everything

    val maintenanceState = maintenanceViewModel.maintenanceStateFlow.collectAsState().value

    if (maintenanceState is LoadState.Success && !maintenanceState.value.isForceUpdateRequired) {
        LaunchedEffect(Unit) {
            Timber.d("forceUpdateMessage $forceUpdateMessage")
            maintenanceViewModel.navigateToSplashScreen()
        }
    }

    val isConnected = connectivityViewModel.isConnected.collectAsState()
    Timber.e("Connectivity checking MaintenanceScreen: ${isConnected.value} ")

    LaunchedEffect(isConnected.value) {
        if (!isConnected.value) {
            maintenanceViewModel.updateErrorScreen()
        } else {
            Timber.e("Connectivity available at  MaintenanceScreen: ${isConnected.value}")
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(
                WindowInsets.statusBars
                    .union(WindowInsets.navigationBars)
                    .asPaddingValues(),
            )
            .background(TemplateColors.BackgroundLight),
    ) {
        // Column for vertical arrangement in the center
        when (maintenanceState) {
            is LoadState.Loading -> {
                CustomLoadingScreen(
                    modifier = Modifier.fillMaxSize()
                        .background(Color.White.copy(alpha = 0.7f)),
                )
            }
            is LoadState.Success -> {
                val maintenanceMessage = maintenanceState.value.updateMessage
                Timber.e("LoadState Success: $maintenanceMessage")
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.align(Alignment.Center),
                ) {
                    GlobalImageDisplay(
                        imageResId = R.drawable.manitanence,
                        contentDescription = "Maintenance Image",
                        imageSize = 50.dp,
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    GlobalTextView(
                        text = stringResource(R.string.app_update_title),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center,
                        color = TextPrimaryLight,
                    )
                    GlobalTextView(
                        text = maintenanceMessage,
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center,
                        color = TextPrimaryLight,
                        modifier = Modifier.padding(
                            top = 4.dp,
                            start = 16.dp,
                            end = 16.dp,
                            bottom = 4.dp,
                        ),
                    )
                }
            }
            is LoadState.Failure -> {
                val errorMessage = maintenanceState.exception.localizedMessage ?: "Unknown error."
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.align(Alignment.Center),
                ) {
                    GlobalImageDisplay(
                        imageResId = R.drawable.manitanence, // Error image
                        contentDescription = "Error Image",
                        imageSize = 50.dp,
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    GlobalTextView(
                        text = "Failed to load maintenance data.",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Red, // Error color
                    )
                    GlobalTextView(
                        text = errorMessage,
                        fontSize = 16.sp,
                        color = TextPrimaryLight,
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }
            }
            is LoadState.Initial -> {
                Timber.e("LoadState Initial")
            }
        }

        // Button at the bottom, outside the Column
        val uriHandler = LocalUriHandler.current
        GlobalButton(
            text = stringResource(id = R.string.app_update_button),
            fontWeight = FontWeight.Bold,
            onClick = {
                if (isConnected.value) {
                    uriHandler.openUri(destinationLink)
                } else {
                    maintenanceViewModel.updateErrorScreen()
                }
            },
            cornerRadius = 8.dp,
            textColor = ButtonTextPrimary,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 48.dp, start = 16.dp, end = 16.dp, top = 16.dp)
                .height(50.dp),
        )

        if (maintenanceViewModel.navigator.isLoading.value) {
            CustomLoadingScreen(
                modifier = Modifier.fillMaxSize()
                    .background(Color.White.copy(alpha = 0.7f)),
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
private fun ForceUpdateScreenPreview() {
    TemplateTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(TemplateColors.BackgroundLight),
        ) {
            val maintenanceMessage = stringResource(R.string.app_update_description)
            Timber.e("LoadState Success: $maintenanceMessage")
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.align(Alignment.Center),
            ) {
                GlobalImageDisplay(
                    imageResId = R.drawable.manitanence,
                    contentDescription = "Maintenance Image",
                    imageSize = 50.dp,
                )
                Spacer(modifier = Modifier.height(8.dp))
                GlobalTextView(
                    text = stringResource(R.string.app_update_title),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = TextPrimaryLight,
                )
                GlobalTextView(
                    text = maintenanceMessage,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    color = TextPrimaryLight,
                    modifier = Modifier.padding(
                        top = 4.dp,
                        start = 16.dp,
                        end = 16.dp,
                        bottom = 4.dp,
                    ),
                )
            }

            GlobalButton(
                text = stringResource(id = R.string.app_update_button),
                fontWeight = FontWeight.Bold,
                onClick = {
                    /* viewModel.refreshRemoteConfig()*/
                },
                cornerRadius = 8.dp,
                textColor = ButtonTextPrimary,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 48.dp, start = 16.dp, end = 16.dp, top = 16.dp)
                    .height(50.dp),
            )
        }
    }
}
