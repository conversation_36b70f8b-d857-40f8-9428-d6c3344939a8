package jp.co.dhbk.connectbiz.ui.destinations.licenses
import jp.co.dhbk.connectbiz.core.common.library.Library
import android.content.Context
import javax.inject.Inject

interface LicenseRepository {
    suspend fun getLibraries(): List<Library>
}

class DefaultLicenseRepository @Inject constructor(
    private val context: Context,
) : LicenseRepository {
    override suspend fun getLibraries(): List<Library> {
        return Library.getLibraries(context)
    }
}
