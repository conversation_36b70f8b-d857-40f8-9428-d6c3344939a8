package jp.co.dhbk.connectbiz.ui.designsystem

import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Color

@Immutable
internal data object TemplateColors {
    val BrandColor = Color(0xFF0C68AE) // Not in use currently
    val PrimaryLight = Color(0xFF5A67D8) // ThemeColor primary
    val PrimaryDark = Color(0xFF0C68AE) // Another shade
    val SecondaryLight = Color(0xFFEBA514)
    val SecondaryDark = Color(0xFFCBD1FF)

    val BackgroundLight = Color(0xFFFFFFFF)
    val BackgroundDark = Color(0xFF232323)

    val TextPrimaryLight = Color(0xFF232323)
    val TextPrimaryDark = Color(0xFFFFFFFF)

    val ButtonBackgroundPrimary = Color(0xFF5A67D8)
    val ButtonBackgroundSecondary = Color(0xFFF8F8F8)

    val ButtonTextPrimary = Color(0xFFFFFFFF)
    val ButtonTextSecondary = Color(0xFF434343)
    val ButtonDisabled = Color(0xFFC4C4C4)
    val TextLink = Color(0xFF5A67D8)
    val BackgroundOverlay = Color(0xFFFFFFFF)
    val TextTertiary = Color(0xFF999999)
    val TextSecondary = Color(0xFF424242)
    val DividerColor = Color(0xFFD9D9D9)
    val colorScheme = lightColorScheme()
}
