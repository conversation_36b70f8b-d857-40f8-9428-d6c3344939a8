package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.LinearEasing
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.unit.dp

@Composable
fun CustomLoadingScreen(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.wrapContentSize(Alignment.Center),
        contentAlignment = Alignment.Center,
    ) {
        CustomCircularSpinner()
    }
}

@Composable
fun CustomCircularSpinner(modifier: Modifier = Modifier) {
    val infiniteTransition = rememberInfiniteTransition()
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1200, easing = LinearEasing),
        ),
        label = "Loading",
    )

    Canvas(
        modifier = modifier.size(40.dp), // Adjust size if needed
    ) {
        val radius = size.minDimension / 2
        val lineLength = radius / 2 // Adjust length of the dashes here
        val strokeWidth = 4.dp.toPx()
        val color = Color.Gray // Customize color if needed

        for (i in 0..11) {
            val alpha = i / 12f // Adjust transparency of each dash
            rotate(degrees = i * 30f + rotation) {
                drawLine(
                    color = color.copy(alpha = alpha),
                    start = center.copy(y = center.y - radius + lineLength),
                    end = center.copy(y = center.y - radius),
                    strokeWidth = strokeWidth,
                )
            }
        }
    }
}
