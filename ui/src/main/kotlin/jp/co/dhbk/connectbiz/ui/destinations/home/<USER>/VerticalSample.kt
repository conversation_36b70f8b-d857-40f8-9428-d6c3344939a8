package jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.union
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VerticalVueReaderState
import kotlinx.coroutines.launch

@Composable
fun VerticalSample(
    verticalVueReaderState: VerticalVueReaderState,
    fileName: String,
    onCloseButtonClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(
                WindowInsets.statusBars
                    .union(WindowInsets.navigationBars)
                    .asPaddingValues(),
            ),
    ) {
        val scope = rememberCoroutineScope()

        val background = Modifier.background(
            MaterialTheme.colorScheme.background.copy(alpha = 0.75f),
            MaterialTheme.shapes.small,
        )
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.outline,
                shape = MaterialTheme.shapes.small,
            )
            .clip(MaterialTheme.shapes.small)
        val iconTint = MaterialTheme.colorScheme.onBackground

        VerticalVueReader(
            modifier = Modifier.fillMaxSize(),
            contentModifier = Modifier.fillMaxSize(),
            verticalVueReaderState = verticalVueReaderState,
        )
        Row(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(horizontal = 8.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = "${verticalVueReaderState.currentPage} of " +
                    "${verticalVueReaderState.pdfPageCount}",
                modifier = Modifier
                    .then(background)
                    .padding(10.dp),
            )
            // Filename (center)
            Text(
                text = fileName,
                color = MaterialTheme.colorScheme.onBackground,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .weight(1f) // takes the middle space
            )
            IconButton(
                modifier = background,
                onClick = onCloseButtonClick,
            ) {
                Icon(
                    imageVector = Icons.Filled.Close,
                    contentDescription = "Close",
                    tint = iconTint,
                )
            }
        }
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(horizontal = 8.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            val showPrevious by remember {
                derivedStateOf { verticalVueReaderState.currentPage != 1 }
            }
            val showNext by remember {
                derivedStateOf {
                    verticalVueReaderState.currentPage != verticalVueReaderState
                        .pdfPageCount
                }
            }
            if (showPrevious) {
                IconButton(
                    modifier = background,
                    onClick = {
                        // Prev
                        scope.launch {
                            verticalVueReaderState.prevPage()
                        }
                    },
                ) {
                    Icon(
                        imageVector = Icons.Filled.KeyboardArrowUp,
                        contentDescription = "Previous",
                        tint = iconTint,
                    )
                }
            } else {
                Spacer(
                    modifier = Modifier
                        .size(48.dp)
                        .background(Color.Transparent),
                )
            }
            Spacer(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
            )
            if (showNext) {
                IconButton(
                    modifier = background,
                    onClick = {
                        // Next
                        scope.launch {
                            verticalVueReaderState.nextPage()
                        }
                    },
                ) {
                    Icon(
                        imageVector = Icons.Filled.KeyboardArrowDown,
                        contentDescription = "Next",
                        tint = iconTint,
                    )
                }
            } else {
                Spacer(
                    modifier = Modifier
                        .size(48.dp)
                        .background(Color.Transparent),
                )
            }
        }
    }
}
