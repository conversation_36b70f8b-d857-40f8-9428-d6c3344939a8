package jp.co.dhbk.connectbiz.ui.designsystem

import androidx.compose.ui.unit.dp

object Dimen {
    val zero = 0.dp
    val doubleSpace = 16.dp
    val splashSize = 100.dp
    val smallLottieIcon = 150.dp
    val productPeekHeight = 300.dp
    val imageTop = (-16).dp
    val height = 70.dp
    val sliderHeight = 332.dp
    val videoHeight = 150.dp
    val videoWidth = 140.dp
    val halfSpace = 4.dp
    val twoSpace = 2.dp
    val space = 8.dp
    val personImage = 60.dp
    val tripleSpace = 24.dp
    var fourthSpace = 32.dp
    var fifthSpace = 40.dp
}
