package jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.pointerInput
import kotlin.math.cos
import kotlin.math.sin

// Step 1: Define a custom Modifier.Node
class ZoomableModifier : Modifier.Node() {
    var zoom = mutableFloatStateOf(1f)
    var offsetX = mutableFloatStateOf(0f)
    var offsetY = mutableFloatStateOf(0f)
    var angle = mutableFloatStateOf(0f)
    private val pI = 3.14f
    private val screenWidth = 1080f * 1.2f // Example screen width; adjust accordingly
    private val screenHeight = 1920f * 1.2f // Example screen height; adjust accordingly

    /**
     * Function to handle gestures for zooming and dragging.
     *
     * @param pan The pan offset from the gesture.
     * @param gestureZoom The zoom factor from the gesture.
     */
    fun onGesture(pan: Offset, gestureZoom: Float) {
        zoom.floatValue = (zoom.floatValue * gestureZoom).coerceIn(1f..4f)

        if (zoom.floatValue > 1) {
            val x = pan.x * zoom.floatValue
            val y = pan.y * zoom.floatValue
            val angleRad = angle.floatValue * pI / 180f

            offsetX.floatValue = (offsetX.floatValue + (x * cos(angleRad) - y * sin(angleRad)))
                .coerceIn(
                    -(screenWidth * zoom.floatValue)..(screenWidth * zoom.floatValue),
                )
            offsetY.floatValue = (offsetY.floatValue + (x * sin(angleRad) + y * cos(angleRad)))
                .coerceIn(
                    -(screenHeight * zoom.floatValue)..(screenHeight * zoom.floatValue),
                )
        } else {
            offsetX.floatValue = 0f
            offsetY.floatValue = 0f
        }
    }
}

// Step 2: Modifier extension to detect gestures and apply zoom
fun Modifier.pinchToZoomAndDrag(zoomableModifier: ZoomableModifier): Modifier = this.then(
    Modifier.pointerInput(Unit) {
        detectTransformGestures { _, pan, gestureZoom, _ ->
            // Apply gesture to ZoomableModifier
            zoomableModifier.onGesture(pan, gestureZoom)
        }
    },
)

// Step 3: Usage in a composable
/*@Composable
fun ZoomableImage(modifier: Modifier = Modifier) {
    // Step 4: Create a ZoomableModifier instance
    val zoomableModifier = remember { ZoomableModifier() }

    // Step 5: Apply modifier and state to the Image
    Image(
        modifier = modifier
            .graphicsLayer(
                scaleX = zoomableModifier.zoom,
                scaleY = zoomableModifier.zoom,
                translationX = zoomableModifier.offsetX,
                translationY = zoomableModifier.offsetY,
                rotationZ = zoomableModifier.angle
            )
            .pinchToZoomAndDrag(zoomableModifier),  // Apply the pinch zoom and drag modifier
        bitmap = yourImageBitmap, // Replace with actual image
        contentDescription = "Zoomable Image"
    )
}*/
