package jp.co.dhbk.connectbiz.ui.designsystem

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.navigation.NavBackStackEntry
import kotlin.math.roundToInt

/**
 * アプリ全体で利用するアニメーションに関するものをまとめる
 */
@Immutable
internal data object TemplateAnimations {
    /**
     * 画面遷移時の `enterTransition`
     */
    val navigationEnterTransition:
        AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition
        @Composable get() {
            val offsetPx = with(LocalDensity.current) { NAVIGATION_SLIDE_DISTANCE.roundToPx() }
            return {
                slideIntoContainer(
                    animationSpec =
                    tween(
                        durationMillis = NAVIGATION_DURATION,
                        easing = FastOutSlowInEasing,
                    ),
                    initialOffset = { offsetPx },
                    towards = AnimatedContentTransitionScope.SlideDirection.Start,
                ) +
                    fadeIn(
                        animationSpec =
                        tween(
                            durationMillis = MAIN_INCOMING_DURATION,
                            delayMillis = MAIN_OUTGOING_DURATION,
                            easing = LinearOutSlowInEasing,
                        ),
                    )
            }
        }

    /**
     * 画面遷移時の `exitTransition`
     */
    val navigationExitTransition:
        AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition
        @Composable get() {
            val offsetPx = with(LocalDensity.current) { NAVIGATION_SLIDE_DISTANCE.roundToPx() }
            return {
                slideOutOfContainer(
                    animationSpec =
                    tween(
                        durationMillis = NAVIGATION_DURATION,
                        easing = FastOutSlowInEasing,
                    ),
                    targetOffset = { -offsetPx },
                    towards = AnimatedContentTransitionScope.SlideDirection.Start,
                ) +
                    fadeOut(
                        animationSpec =
                        tween(
                            durationMillis = MAIN_OUTGOING_DURATION,
                            delayMillis = 0,
                            easing = FastOutLinearInEasing,
                        ),
                    )
            }
        }

    /**
     * 画面遷移時の `popEnterTransition`
     */
    val navigationPopEnterTransition:
        AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition
        @Composable get() {
            val offsetPx = with(LocalDensity.current) { NAVIGATION_SLIDE_DISTANCE.roundToPx() }
            return {
                slideIntoContainer(
                    animationSpec =
                    tween(
                        durationMillis = NAVIGATION_DURATION,
                        easing = FastOutSlowInEasing,
                    ),
                    initialOffset = { -offsetPx },
                    towards = AnimatedContentTransitionScope.SlideDirection.End,
                ) +
                    fadeIn(
                        animationSpec =
                        tween(
                            durationMillis = MAIN_INCOMING_DURATION,
                            delayMillis = MAIN_OUTGOING_DURATION,
                            easing = LinearOutSlowInEasing,
                        ),
                    )
            }
        }

    /**
     * 画面遷移時の `popExitTransition`
     */
    val navigationPopExitTransition:
        AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition
        @Composable get() {
            val offsetPx = with(LocalDensity.current) { NAVIGATION_SLIDE_DISTANCE.roundToPx() }
            return {
                slideOutOfContainer(
                    animationSpec =
                    tween(
                        durationMillis = NAVIGATION_DURATION,
                        easing = FastOutSlowInEasing,
                    ),
                    targetOffset = { offsetPx },
                    towards = AnimatedContentTransitionScope.SlideDirection.End,
                ) +
                    fadeOut(
                        animationSpec =
                        tween(
                            durationMillis = MAIN_OUTGOING_DURATION,
                            delayMillis = 0,
                            easing = FastOutLinearInEasing,
                        ),
                    )
            }
        }

    private const val NAVIGATION_DURATION: Int = 300
    private val NAVIGATION_SLIDE_DISTANCE: Dp = 30.dp
    private val MAIN_OUTGOING_DURATION: Int = (NAVIGATION_DURATION * 0.35f).roundToInt()
    private val MAIN_INCOMING_DURATION: Int = NAVIGATION_DURATION - MAIN_OUTGOING_DURATION
}
