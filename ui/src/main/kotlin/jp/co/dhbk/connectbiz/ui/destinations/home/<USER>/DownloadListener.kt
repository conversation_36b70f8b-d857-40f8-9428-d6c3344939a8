package jp.co.dhbk.connectbiz.ui.destinations.home.webview

import android.app.DownloadManager
import android.content.ActivityNotFoundException
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Environment.DIRECTORY_DOWNLOADS
import android.webkit.CookieManager
import android.webkit.DownloadListener
import timber.log.Timber

/**
 * WebViewのDownloadListener
 * WebView内でpdf表示が発生した際に、カスタムヘッダを付与して端末内にダウンロード後、該当pdfを表示させる
 */
class DownloadListener(
    private val context: Context,
    private val accessToken: String,
    private val onPdfFailed: () -> Unit,
    private val onCloseWindow: () -> Unit,
) : DownloadListener {

    override fun onDownloadStart(
        url: String?,
        userAgent: String?,
        contentDescription: String?,
        mimetype: String?,
        contentLength: Long,
    ) {
        runCatching {
            // ファイル名の取得
            Timber.e("inside Download! $url")
            url ?: return
            val uri = Uri.parse(url)
            val fileName = uri.lastPathSegment

            // DownloadManagerの設定
            val manager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            val request = DownloadManager.Request(Uri.parse(url)).apply {
                setTitle(fileName)
                setDescription("$fileName Downloading")
                setNotificationVisibility(
                    DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED,
                )
                setDestinationInExternalPublicDir(DIRECTORY_DOWNLOADS, fileName)
                setMimeType(mimetype)
                // ヘッダーを付与する
                val cookies = CookieManager.getInstance().getCookie(url)
                addRequestHeader("cookie", cookies)
                addRequestHeader("access_token", accessToken)
            }

            // ダウンロード
            val downloadId = manager.enqueue(request)

            val receiver = object : BroadcastReceiver() {
                // ダウンロード完了後の処理
                override fun onReceive(context: Context, intent: Intent) {
                    val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                    if (id == downloadId) {
                        // intentによりファイルを開く
                        val openFileIntent = Intent(Intent.ACTION_VIEW)
                        val fileUri = manager.getUriForDownloadedFile(id)
                        // POSTリクエストでのPDF表示はnullになる
                        if (fileUri == null) {
                            onPdfFailed()
                            return
                        }
                        openFileIntent.setDataAndType(
                            fileUri,
                            context.contentResolver.getType(fileUri),
                        )
                        openFileIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        try {
                            context.startActivity(openFileIntent)
                        } catch (e: ActivityNotFoundException) {
                            onPdfFailed()
                        }
                    }
                }
            }
            context.registerReceiver(
                receiver,
                IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE),
                Context.RECEIVER_NOT_EXPORTED,
            )
        }.onFailure {
            onPdfFailed()
        }.also {
            onCloseWindow()
        }
    }
}
