package jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import timber.log.Timber

/**
 * Android-Request-Inspector-WebViewをコピーしたもの
 * https://github.com/acsbendi/Android-Request-Inspector-WebView
 */
@SuppressLint("SetJavaScriptEnabled")
open class RequestInspectorWebViewClient @JvmOverloads constructor(
    webView: WebView,
    onReceiveIdToken: (String) -> Unit,
    private val options: RequestInspectorOptions = RequestInspectorOptions(),
) : WebViewClient() {

    private val interceptionJavascriptInterface =
        RequestInspectorJavaScriptInterface(webView, onReceiveIdToken)

    init {
        val webSettings = webView.settings
        webSettings.javaScriptEnabled = true
        webSettings.domStorageEnabled = true
    }

    final override fun shouldInterceptRequest(
        view: WebView,
        request: WebResourceRequest,
    ): WebResourceResponse? {
        val recordedRequest = interceptionJavascriptInterface.findRecordedRequestForUrl(
            request.url.toString(),
        )
        val webViewRequest = WebViewRequest.Companion.create(request, recordedRequest)
        return shouldInterceptRequest(view, webViewRequest)
    }

    open fun shouldInterceptRequest(
        view: WebView,
        webViewRequest: WebViewRequest,
    ): WebResourceResponse? {
        logWebViewRequest(webViewRequest)
        return null
    }

    @Suppress("MemberVisibilityCanBePrivate")
    protected fun logWebViewRequest(webViewRequest: WebViewRequest) {
        // Timber.i("Sending request from WebView: $webViewRequest")
    }

    override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
        // Timber.i("Page started loading, enabling request inspection. URL: $url")
        RequestInspectorJavaScriptInterface.Companion.enabledRequestInspection(
            view,
            options.extraJavaScriptToInject,
        )
        super.onPageStarted(view, url, favicon)
    }

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
        return super.shouldOverrideUrlLoading(view, request)
    }

    override fun onReceivedError(
        view: WebView?,
        request: WebResourceRequest?,
        error: WebResourceError?,
    ) {
        super.onReceivedError(view, request, error)
    }

    override fun onReceivedHttpError(
        view: WebView?,
        request: WebResourceRequest?,
        errorResponse: WebResourceResponse?,
    ) {
        super.onReceivedHttpError(view, request, errorResponse)
        Timber.e("HTTP error: ${errorResponse?.statusCode}")
    }

    override fun onPageFinished(view: WebView?, url: String?) {
        // Timber.i("Page Finished $url")
        RequestInspectorJavaScriptInterface.Companion.enabledRequestInspection(
            view,
            options.extraJavaScriptToInject,
        )
        super.onPageFinished(view, url)
    }
}
