package jp.co.dhbk.connectbiz.ui.destinations.home

import android.Manifest
import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.biometric.BiometricPrompt
import androidx.browser.customtabs.CustomTabsIntent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import jp.co.dhbk.connectbiz.core.common.loadstate.LoadState
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricAuthResult
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.GlobalSnackbarManager
import jp.co.dhbk.connectbiz.ui.navigation.ConnectivityViewModel
import jp.co.dhbk.connectbiz.ui.utils.biometricAuth.BiometricViewModel.BiometricState
import jp.co.dhbk.connectbiz.ui.utils.biometricAuth.BiometricScreen
import jp.co.dhbk.connectbiz.ui.utils.biometricAuth.BiometricViewModel
import jp.co.dhbk.connectbiz.ui.utils.component.ConnectBizDialog
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState
import kotlinx.coroutines.launch
import timber.log.Timber

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun HomeScreen(
    url: String,
    modifier: Modifier = Modifier,
    homeViewModel: HomeViewModel = hiltViewModel(),
    biometricViewModel: BiometricViewModel = hiltViewModel(),
    connectivityViewModel: ConnectivityViewModel = hiltViewModel(),
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    // States from ViewModels
    val homeState by homeViewModel.state.collectAsStateWithLifecycle()
    val biometricLoadState by biometricViewModel.loadStateFlow.collectAsStateWithLifecycle()
    val authState by biometricViewModel.authResult.collectAsStateWithLifecycle()
    // Local variables and helpers
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    var isBiometricInitialized = remember { mutableStateOf(false) }
    val internalDomains = listOf(
        homeViewModel.apiConfig.baseUrl,
        homeViewModel.apiConfig.mfaEnable,
    )
    val biometricState = when (biometricLoadState) {
        is LoadState.Success -> (biometricLoadState as LoadState.Success).value
        is LoadState.Loading -> BiometricState(isLoading = true)
        is LoadState.Failure -> BiometricState(errorMessage = "Failed to load biometric settings")
        LoadState.Initial -> BiometricState()
    }
    val permissionState = rememberPermissionState(Manifest.permission.POST_NOTIFICATIONS)
    val isPermissionDialogDisplay =
        !permissionState.status.isGranted && !permissionState.status.shouldShowRationale

    val isConnected = connectivityViewModel.isConnected.collectAsState()
    LaunchedEffect(isConnected.value) {
        if (!isConnected.value) {
            homeViewModel.updateErrorScreen()
        }
    }

    // Handle push notification URL
    fun openCustomTab(url: String, context: Context) {
        // Set the isCustomTabOpened flag in the Application class
        GlobalAppState.isCustomTabOpened = true
        // Launch the Custom Tab
        val customTabsIntent = CustomTabsIntent.Builder().build()
        customTabsIntent.launchUrl(context, Uri.parse(url))
    }

    // Handle push notification URL and open custom tab
    val handledUrls = remember { mutableStateOf(mutableSetOf<String>()) }

    fun handleUrlOnce(url: String) {
        if (!handledUrls.value.contains(url)) {
            handledUrls.value.add(url)
            Timber.e("Opening external URL in Custom Tab: $url")
            openCustomTab(url, context)
            homeViewModel.clearNotification()
        } else {
            Timber.e("URL already handled: $url")
        }
    }

    try {
        val uri = Uri.parse(url)
        if (isExternalUrl(uri, internalDomains)) {
            handleUrlOnce(url)
        }
    } catch (e: Exception) {
        Timber.e("Failed to open URL: $url, Error: ${e.message}")
    }

    // Lifecycle observer for biometric status
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                if (homeState.isHomeUrl) {
                    biometricViewModel.initialize()
                    isBiometricInitialized.value = true
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    fun handleAuthentication(authStatus: AuthenticationStatus) {
        val screenType: ScreenType = ScreenType.HOME
        if (authStatus == AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED ||
            authStatus == AuthenticationStatus.BIOMETRIC_UNAVAILABLE_BUT_KEYGUARD_SECURE
        ) {
            biometricViewModel.authenticate(context as FragmentActivity, screenType)
        } else if (authStatus == AuthenticationStatus.SECURITY_UPDATE_REQUIRED || authStatus ==
            AuthenticationStatus.DEVICE_LOCKED
        ) {
            biometricViewModel.onBiometricUnAvailablePopup(true)
        } else {
            biometricViewModel.onUpdateUISetting(true)
        }
    }

    LaunchedEffect(authState) {
        biometricViewModel.updateAuthenticationStatus()
        when (val authState = authState) {
            is BiometricAuthResult.Success -> {
                biometricViewModel.updateBiometricEnableStatus(true)
                coroutineScope.launch {
                    GlobalSnackbarManager.showNormalSnackbar(
                        authState.data.biometricStatus.message.toString(),
                    )
                }
            }
            is BiometricAuthResult.Failure -> {
                when (authState.error.biometricStatus.code) {
                    BiometricPrompt.ERROR_LOCKOUT,
                    BiometricPrompt.ERROR_CANCELED,
                    BiometricPrompt.ERROR_USER_CANCELED,
                    -> {
                        when (authState.error.attemptCount) {
                            3 -> biometricViewModel.retryAuthentication(true)
                            5 -> biometricViewModel.updateAuthenticationFailedStatus()
                        }
                    }
                    BiometricPrompt.ERROR_SECURITY_UPDATE_REQUIRED -> {
                        biometricViewModel.onUpdateUISetting(true)
                    }
                    BiometricPrompt.ERROR_NEGATIVE_BUTTON -> {
                        biometricViewModel.onUpdatePopUpState(false)
                    }
                    BiometricPrompt.ERROR_LOCKOUT_PERMANENT -> {
                        biometricViewModel.updateAuthenticationFailedStatus()
                    }
                    else -> {
                        // Handle other codes if needed
                    }
                }
            }
            BiometricAuthResult.Loading -> {
                // Optionally handle loading state
            }
            else -> {}
        }
    }

    LaunchedEffect(biometricState.isAuthenticationCancel) {
        coroutineScope.launch {
            if (biometricState.isAuthenticationCancel > 0) {
                GlobalSnackbarManager.showNormalSnackbar(biometricState.authenticationErrorMessage)
            }
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize(),
    ) {
        AndroidWebView(
            viewModelUrl = if (!isExternalUrl(Uri.parse(url), internalDomains)) {
                url
            } else {
                homeViewModel.apiConfig.baseUrl
            },
            onBaseUrlStart = {
//                SDK version 14 and later should be added in condition
                if (isPermissionDialogDisplay) {
                    permissionState.launchPermissionRequest()
                }
            },
            onLogout = {
                homeViewModel.handleLogout()
                biometricViewModel.isInitialized = false
            },
            onSettings = { homeViewModel.navigateToSettings() },
            onReceiveIdToken = { idToken ->
                homeViewModel.updateLoginSession(true, idToken)
                homeViewModel.getFCMToken { fcmToken ->
                    if (fcmToken != null) {
                        Timber.d("Id Token ---> $idToken")
                        Timber.d("FCM Token Retrieved: $fcmToken")
                        homeViewModel.sendIdTokenToServer(fcmToken, idToken)
                    } else {
                        Timber.e("Failed to retrieve FCM token.")
                    }
                }
            },
            onUrlChange = { currentUrl ->
                if (homeState.isHomeUrl && permissionState.status.isGranted ||
                    (
                        !permissionState.status.isGranted &&
                            permissionState.status.shouldShowRationale
                        )
                ) {
                    Timber.d("URL changed: $currentUrl")
                    biometricViewModel.initialize()
                    isBiometricInitialized.value = true
                }
            },
        )
        when {
            biometricState.isPopupVisible -> {
                BiometricScreen(
                    onSkipClick = {
                        biometricViewModel.updateBiometricStatus(true)
                    },
                    onAuthenticateClick = {
                        handleAuthentication(biometricViewModel.canAuthenticate())
                    },
                    authenticationStatus = biometricViewModel.canAuthenticate(),
                )
            }
            biometricState.isSettingsPopupVisible -> {
                val authenticationStatus = biometricViewModel.canAuthenticate()
                ConnectBizDialog(
                    title = when (authenticationStatus) {
                        AuthenticationStatus.BIOMETRIC_NOT_ENROLLED -> stringResource(
                            R.string.biometric_settings_popup_title,
                        )
                        AuthenticationStatus.DEVICE_NOT_SECURE -> stringResource(
                            R.string.device_not_secure_popup_title,
                        )
                        else -> stringResource(R.string.biometric_settings_popup_title)
                    },
                    message = when (authenticationStatus) {
                        AuthenticationStatus.BIOMETRIC_NOT_ENROLLED ->
                            stringResource(
                                R.string.biometric_settings_popup_description,
                            )
                        AuthenticationStatus.DEVICE_NOT_SECURE -> stringResource(
                            R.string.device_not_secure_popup_description,
                        )
                        else -> stringResource(R.string.biometric_settings_popup_description)
                    },
                    enableCancel = true,
                    negativeLabel = stringResource(R.string.alert_cancel),
                    positiveLabel = stringResource(R.string.go_to_device_settings),
                    onClick = { index ->
                        when (index) {
                            1 -> biometricViewModel.navigateToSystemSettings(context as Activity)
                            0 -> biometricViewModel.updateBiometricStatus(true)
                        }
                    },
                )
            }
            biometricState.isAuthenticationRetryPopup -> {
                val authenticationStatus = biometricViewModel.canAuthenticate()
                ConnectBizDialog(
                    title = when (authenticationStatus) {
                        AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED -> stringResource(
                            R.string.biometric_retry_title,
                        )
                        AuthenticationStatus.KEYGUARD_SECURE_ENABLED -> stringResource(
                            R.string.pin_pattern_settings_popup_title,
                        )
                        AuthenticationStatus.DEVICE_NOT_SECURE -> stringResource(
                            R.string.device_not_secure_popup_title,
                        )
                        else -> stringResource(R.string.biometric_retry_title)
                    },
                    message = when (authenticationStatus) {
                        AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED -> stringResource(
                            R.string.biometric_retry_description,
                        )
                        AuthenticationStatus.KEYGUARD_SECURE_ENABLED -> stringResource(
                            R.string.pin_pattern_settings_popup_description,
                        )
                        AuthenticationStatus.DEVICE_NOT_SECURE -> stringResource(
                            R.string.device_not_secure_popup_description,
                        )
                        else -> stringResource(R.string.biometric_retry_description)
                    },
                    enableCancel = true,
                    negativeLabel = stringResource(R.string.alert_cancel),
                    positiveLabel = stringResource(R.string.retry_btn),
                    onClick = { index ->
                        when (index) {
//                            1 -> biometricViewModel.onUpdatePopUpState(true)
                            1 -> handleAuthentication(authenticationStatus)
                            0 -> biometricViewModel.updateBiometricStatus(true)
                        }
                    },
                )
            }
            biometricState.isAuthenticationFailedPopup -> {
                ConnectBizDialog(
                    title = stringResource(R.string.authentication_failed_title),
                    message = stringResource(R.string.authentication_failed_description),
                    enableCancel = true,
                    positiveLabel = stringResource(R.string.close),
                    negativeLabel = null,
                    onClick = {
                        biometricViewModel.updateBiometricStatus(true)
                    },
                )
            }
            biometricState.isBiometricUnAvailable -> {
                ConnectBizDialog(
                    title = stringResource(R.string.biometric_error_popup_title),
                    message = stringResource(R.string.biometric_error_popup_description),
                    enableCancel = true,
                    negativeLabel = null,
                    positiveLabel = stringResource(R.string.close),
                    onClick = { },
                )
            }
        }

        if (homeState.showConflictErrorPopup) {
            ConnectBizDialog(
                title = stringResource(R.string.conflict_error_popup_title),
                message = stringResource(R.string.conflict_error_popup_description),
                enableCancel = false,
                negativeLabel = null,
                positiveLabel = stringResource(R.string.close), // Close button
                onClick = { index ->
                    // Dismiss the dialog and handle any additional actions
                    if (index == 1) {
                        // Perform any clean-up if necessary after dialog is closed
                        biometricViewModel.onUpdateBiometricPopup(false)
                        homeViewModel.invalidUserLogout()
                    }
                },
            )
        }

        if (homeViewModel.navigator.isLoading.value) {
            CustomLoadingScreen(
                modifier = Modifier.fillMaxSize()
                    .background(Color.White.copy(alpha = 0.7f)),
            )
        }
    }
}
