package jp.co.dhbk.connectbiz.ui.destinations.home

import android.content.Context
import android.graphics.Bitmap
import android.graphics.pdf.PdfRenderer
import android.os.ParcelFileDescriptor
import android.webkit.CookieManager
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.usecase.home.GetUserAgentStringUseCase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

@HiltViewModel
class PdfViewerViewModel
@Inject constructor(
    @ApplicationContext private val context: Context,
    private val getUserAgentJsonUseCase: GetUserAgentStringUseCase,
    private val connectbizPreference: ConnectbizPreference,
) : ViewModel() {

    private val _isLoading = mutableStateOf(true)
    val isLoading: State<Boolean> = _isLoading

    private val _pageCount = mutableIntStateOf(0)
    val pageCount: State<Int> = _pageCount

    private val _pageBitmaps = mutableStateListOf<Bitmap>()
    val pageBitmaps: List<Bitmap> = _pageBitmaps

    private var pdfRenderer: PdfRenderer? = null


    override fun onCleared() {
        super.onCleared()
        pdfRenderer?.close()
    }
}
