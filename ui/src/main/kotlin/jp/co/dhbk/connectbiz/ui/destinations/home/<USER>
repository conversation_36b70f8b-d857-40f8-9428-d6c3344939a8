package jp.co.dhbk.connectbiz.ui.destinations.home

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.PlayerView
import java.io.File
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.VerticalSample
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VerticalVueReaderState
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VueFileType
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VueLoadState
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.VueResourceType
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.state.rememberVerticalVueReaderState
import jp.co.dhbk.connectbiz.ui.destinations.home.pdfviewer.util.compressImageToThreshold
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import kotlinx.coroutines.launch

@Composable
fun PdfViewerScreen(
    url: String,
    navigator: Navigator,
    isRemote: Boolean = true,
    fileType: String = "auto",
    fileName: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val vueFileType = determineVueFileType(url, fileType)

    // If it's a video, render the player and bail out.
    if (vueFileType == VueFileType.VIDEO) {
        val uri = if (isRemote) Uri.parse(url) else createLocalUri(url, context)
        VideoPlayer(
            uri = uri,
            onClose = navigator::navigateToBack,
            onBack = navigator::navigateToBack,
            modifier = modifier
        )
        return
    }

    // Existing PDF/Image path
    val pdfReaderState = rememberVerticalVueReaderState(
        resource = if (isRemote) {
            VueResourceType.Remote(
                url = url,
                fileType = vueFileType,
            )
        } else {
            VueResourceType.Local(
                uri = createLocalUri(url, context),
                fileType = vueFileType,
            )
        }
    )
    VerticalPdfViewer(
        verticalVueReaderState = pdfReaderState,
        onCloseButtonClick = navigator::navigateToBack,
        fileName = fileName,
        modifier = modifier,
    )
}

private fun determineVueFileType(url: String, fileType: String): VueFileType {
    val ext = url.substringAfterLast('.', "").lowercase()

    return when (fileType.lowercase()) {
        "pdf" -> VueFileType.PDF
        "image" -> VueFileType.IMAGE
        "video" -> VueFileType.VIDEO
        "auto" -> {
            when {
                ext in listOf("pdf") -> VueFileType.PDF
                ext in listOf("jpg","jpeg","png","gif","bmp","webp") -> VueFileType.IMAGE
                ext in listOf("mp4","mkv","avi","mov","flv","3gp","webm") -> VueFileType.VIDEO
                // last-ditch sniffing from URL text
                url.contains("pdf", ignoreCase = true) -> VueFileType.PDF
                url.contains("image", ignoreCase = true) ||
                        url.contains("jpg", ignoreCase = true) ||
                        url.contains("jpeg", ignoreCase = true) ||
                        url.contains("png", ignoreCase = true) -> VueFileType.IMAGE
                url.contains("video", ignoreCase = true) ||
                        url.contains("mp4", ignoreCase = true) ||
                        url.contains("mkv", ignoreCase = true) ||
                        url.contains("webm", ignoreCase = true) -> VueFileType.VIDEO
                else -> VueFileType.PDF // safe default
            }
        }
        else -> VueFileType.PDF // fallback
    }
}

private fun createLocalUri(url: String, context: Context): Uri {
    return when {
        url.startsWith("content://") || url.startsWith("file://") -> Uri.parse(url)
        url.startsWith("/") -> {
            val file = File(url)
            if (file.exists()) {
                try {
                    FileProvider.getUriForFile(
                        context,
                        "${context.packageName}.fileprovider",
                        file
                    )
                } catch (_: Exception) {
                    file.toUri()
                }
            } else {
                Uri.parse("file://$url")
            }
        }
        else -> {
            val file = File(url)
            if (file.exists()) {
                try {
                    FileProvider.getUriForFile(
                        context,
                        "${context.packageName}.fileprovider",
                        file
                    )
                } catch (_: Exception) {
                    file.toUri()
                }
            } else {
                Uri.parse("file://$url")
            }
        }
    }
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun VerticalPdfViewer(
    verticalVueReaderState: VerticalVueReaderState,
    fileName: String,
    onCloseButtonClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val launcher = verticalVueReaderState.getImportLauncher(interceptResult = {
        it.compressImageToThreshold(2)
    })

    BoxWithConstraints(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        val configuration = LocalConfiguration.current
        val containerSize = remember {
            IntSize(constraints.maxWidth, constraints.maxHeight)
        }

        LaunchedEffect(Unit) {
            verticalVueReaderState.load(
                context = context,
                coroutineScope = scope,
                containerSize = containerSize,
                isPortrait = configuration.orientation == Configuration.ORIENTATION_PORTRAIT,
                customResource = null,
            )
        }
        when (verticalVueReaderState.vueLoadState) {
            is VueLoadState.NoDocument -> {
                Button(onClick = {
                    verticalVueReaderState.launchImportIntent(
                        context = context,
                        launcher = launcher,
                    )
                }) {
                    Text(text = "Import Document")
                }
            }
            is VueLoadState.DocumentError -> {
                Column {
                    Text(text = "Error:  ${verticalVueReaderState.vueLoadState.getErrorMessage}")
                    Button(onClick = {
                        scope.launch {
                            verticalVueReaderState.load(
                                context = context,
                                coroutineScope = scope,
                                containerSize = containerSize,
                                isPortrait = configuration.orientation == Configuration.ORIENTATION_PORTRAIT,
                                customResource = null,
                            )
                        }
                    }) {
                        Text(text = "Retry")
                    }
                }
            }
            is VueLoadState.DocumentImporting -> {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    CustomLoadingScreen(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.White.copy(alpha = 0.7f)),
                    )
                    Text(text = "Importing...")
                }
            }
            is VueLoadState.DocumentLoaded -> {
                VerticalSample(
                    modifier = Modifier.fillMaxHeight(),
                    verticalVueReaderState = verticalVueReaderState,
                    onCloseButtonClick = onCloseButtonClick,
                    fileName = fileName,
                )
            }
            is VueLoadState.DocumentLoading -> {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    CustomLoadingScreen(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.White.copy(alpha = 0.7f)),
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(text = "読み込み中 ${verticalVueReaderState.loadPercent}")
                }
            }
        }
    }
}

/* ----------------------------- VIDEO PLAYER ----------------------------- */

@Composable
fun VideoPlayer(
    uri: Uri,
    onClose: () -> Unit,
    onBack: () -> Unit ,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val fileName = remember(uri) { uri.lastPathSegment ?: "Video" }

    val exoPlayer: Player = remember {
        ExoPlayer.Builder(context).build().apply {
            setMediaItem(MediaItem.fromUri(uri))
            prepare()
            playWhenReady = false
        }
    }

    DisposableEffect(Unit) {
        onDispose { exoPlayer.release() }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // actual video surface
        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { ctx ->
                PlayerView(ctx).apply {
                    player = exoPlayer
                    useController = true
                }
            }
        )

        // top bar overlay
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0x80000000)) // semi-transparent black
                .align(Alignment.TopCenter)
                .padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White
                )
            }

            // Filename centered
            Text(
                text = fileName,
                color = Color.White,
                modifier = Modifier.weight(1f),
                maxLines = 1
            )

            // Close button
            IconButton(onClick = onClose) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                    tint = Color.White
                )
            }
        }
    }
}
