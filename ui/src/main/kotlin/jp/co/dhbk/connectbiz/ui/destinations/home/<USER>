package jp.co.dhbk.connectbiz.ui.destinations.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.analytics.ConnectbizAnalyticConstant
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import jp.co.dhbk.connectbiz.domain.model.DeviceTokenRequest
import jp.co.dhbk.connectbiz.domain.model.error.ApiException
import jp.co.dhbk.connectbiz.ui.navigation.ErrorState
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationRequired
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isSafetyReplyCompleted
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.targetURL
import jp.co.dhbk.connectbiz.usecase.SendDeviceTokenUseCase
import jp.co.dhbk.connectbiz.usecase.home.ClearUserSessionUseCase
import jp.co.dhbk.connectbiz.usecase.home.GetUserAgentStringUseCase
import jp.co.dhbk.connectbiz.usecase.home.HandleFileDownloadUseCase
import jp.co.dhbk.connectbiz.usecase.home.SessionCookieUseCase
import jp.co.dhbk.connectbiz.usecase.home.UpdateLoginSessionUseCase
import jp.co.dhbk.connectbiz.usecase.notificationusecase.NotificationUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    val navigator: Navigator,
    val apiConfig: ApiConfig,
    private val connectbizPreference: ConnectbizPreference,
    private val handleFileDownloadUseCase: HandleFileDownloadUseCase,
    private val updateLoginSessionUseCase: UpdateLoginSessionUseCase,
    private val clearUserSessionUseCase: ClearUserSessionUseCase,
    private val getUserAgentJsonUseCase: GetUserAgentStringUseCase,
    private val sessionCookieUseCase: SessionCookieUseCase,
    private val sendDeviceTokenUseCase: SendDeviceTokenUseCase,
    private val notificationUseCase: NotificationUseCase,
) : ViewModel() {

    // State encapsulating all UI-related data
    data class State(
        val isLoading: Boolean = false,
        val errorState: ErrorState = ErrorState.None,
        val errorMessage: String? = null,
        val lastVisitedUrl: String? = null,
        val currentPdfViewerUrl: String? = null,
        val showFab: Boolean = false,
        val isValidUrl: Boolean = false,
        val isHomeUrl: Boolean = false,
        val isWebViewReload: Boolean = false,
        val showConflictErrorPopup: Boolean = false,
        val conflictErrorMessage: String? = null,
    )

    private val _state = MutableStateFlow(State())
    val state: StateFlow<State> = _state.asStateFlow()

    /**
     * Handles the logout process by clearing the user's session.
     */
    fun handleLogout() {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }
            clearUserSessionUseCase()
            _state.update { it.copy(isHomeUrl = false) }
            _state.update { it.copy(isLoading = false) }
        }
    }

    /**
     * Handles the file download process.
     */
    fun handleFileDownload(
        url: String,
        mimeType: String,
        contentDisposition: String,
        onProgressUpdate: (Int) -> Unit,
        onDownloadComplete: (String, String) -> Unit,
        onDownloadFailed: (String) -> Unit,
    ) {
        handleFileDownloadUseCase(
            url,
            mimeType,
            contentDisposition,
            onProgressUpdate,
            onDownloadComplete,
            onDownloadFailed,
        )
    }

    /**
     * Navigates the user to the settings screen.
     */
    fun navigateToSettings() {
        viewModelScope.launch {
            navigator.navigateTo(
                route = Route.Settings.SETTINGS,
                screenName = ConnectbizAnalyticConstant.Page.SETTINGS,
                popUpTo = Route.Home.HOME,
                inclusive = true,
            )
        }
    }

    /**
     * Retrieves the user agent JSON string by executing a use case.
     */
    fun getUserAgentJson(): String {
        return getUserAgentJsonUseCase()
    }

    /**
     * Saves cookies for a specific URL.
     */
    fun saveCookies(url: String) {
        sessionCookieUseCase.saveSessionCookies(url)
        _state.update { it.copy(errorMessage = null) }
    }

    /**
     * Sends the device token and ID token to the server.
     */
    fun sendIdTokenToServer(deviceToken: String, idToken: String) {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }
            val request = DeviceTokenRequest(deviceToken, idToken)
            sendDeviceTokenUseCase(request)
                .onSuccess {
                    Timber.e("onSuccess $sendDeviceTokenUseCase")
                    _state.update { it.copy(isLoading = false) }
                }
                .onFailure { exception ->
                    if (exception is ApiException) {
                        Timber.e(
                            "API Exception - Code: ${exception.statusCode}, " +
                                "Message: ${exception.message}",
                        )
                        if (exception.statusCode == 409) {
                            handleServerError(exception.statusCode, apiConfig.baseUrl)
                        }
                        _state.update {
                            it.copy(
                                isLoading = false,
                                errorMessage = "Error " +
                                    "${exception.statusCode}: ${exception.message}",
                            )
                        }
                    } else {
                        Timber.e("Exception: ${exception.message}")

                        _state.update {
                            it.copy(isLoading = false, errorMessage = exception.message)
                        }
                    }
                }
        }
    }

    /**
     * Updates the login session.
     */
    fun updateLoginSession(isLogin: Boolean, idToken: String) {
        viewModelScope.launch {
            updateLoginSessionUseCase(isLogin, idToken)
        }
    }

    /**
     * Navigates the user to the PDF viewer screen.
     * @param url The URL or file path of the document
     * @param isRemote Whether the file is remote (true) or local (false)
     * @param fileType The type of file: "pdf", "image", or "auto" to detect automatically
     */
    fun navigateToPdfViewer(url: String, isRemote: Boolean = true, fileType: String = "auto",
                            fileName: String ="auto") {
        val timestamp = System.currentTimeMillis().toString()
        val updatedUrl = if (isRemote) "$url?timestamp=$timestamp" else url
        _state.update { it.copy(currentPdfViewerUrl = updatedUrl) }

        viewModelScope.launch {
            navigator.navigateWithQueryParam(
                route = Route.Home.PDF_VIEWER,
                queryParams = mapOf(
                    "url" to updatedUrl,
                    "fileName" to fileName,
                    "isRemote" to isRemote.toString(),
                    "fileType" to fileType
                ),
                popUpToRoute = Route.Home.HOME,
                inclusive = false,
                screenName = ConnectbizAnalyticConstant.Page.PDF_VIEWER,
            )
        }
    }

    /**
     * Saves the last visited URL.
     */
    fun saveLastVisitedUrl(url: String) {
        connectbizPreference.setLastVistedURL(url)
        _state.update { it.copy(lastVisitedUrl = url) }
    }

    /**
     * Navigates to the error screen.
     */
    fun updateErrorScreen() {
        navigator.navigateTo(
            route = Route.Welcome.ERROR,
            screenName = ConnectbizAnalyticConstant.Page.ERROR,
            popUpTo = Route.Home.HOME,
            inclusive = false,
        )
    }

    /**
     * Fetches the Firebase Cloud Messaging (FCM) token.
     */
    fun getFCMToken(onTokenReceived: (String?) -> Unit) {
        viewModelScope.launch {
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    onTokenReceived(task.result)
                } else {
                    onTokenReceived(null)
                }
            }
        }
    }

    fun updateTargetUrlStatus(url: String) {
        val isTargetDomain = url.contains(apiConfig.baseUrl)
        _state.update { it.copy(isHomeUrl = isTargetDomain) }
    }

    fun receiveUrl(url: String) {
        Timber.e("receiveUrl:: $url")
        if (isAuthenticationRequired && url == apiConfig.baseUrl ||
            url.contains("policy/privacy_policy_1.html") ||
            url.contains("Base/OpenTerms")
        ) {
            isSafetyReplyCompleted = true
            targetURL = url
            notificationUseCase.clearNotificationData()
            notificationUseCase.saveToPreferences()
            navigator.navigateToSplash()
            navigator.navigationStackClear()
            Timber.e("Authentication required $isAuthenticationRequired")
            isAuthenticationRequired = false
        } else {
            Timber.e("Authentication required $isAuthenticationRequired")
        }
    }

    fun clearNotification() {
        notificationUseCase.loadFromPreferences()
        notificationUseCase.clearNotificationData()
        notificationUseCase.saveToPreferences()
        navigator.navigationStackClear()
        Timber.e("Notification Cleared!")
    }

    fun loadNotification(): String? {
        notificationUseCase.loadFromPreferences()
        return notificationUseCase.getNotificationData().first.toString()
    }

    fun handleServerError(statusCode: Int, url: String) {
        when (statusCode) {
            409 -> {
                Timber.d("Conflict Error: $statusCode at URL: $url")
                val errorMessage = "Conflict Error (409) occurred at URL: $url"
                _state.update { currentUiState ->
                    currentUiState.copy(
                        isLoading = false, // Stop the loading
                        isWebViewReload = false,
                        showConflictErrorPopup = true, // Show the conflict popup
                        conflictErrorMessage = errorMessage, // Set the error message
                    )
                }
            }
            500 -> {
                val errorMessage = "Internal Server Error (500) occurred at URL: $url"
                _state.value = _state.value.copy(
                    errorState = ErrorState.SystemError,
                    errorMessage = errorMessage,
                )
            }
            else -> {
                Timber.d("Server Error: $statusCode at URL: $url")
            }
        }
    }

    fun invalidUserLogout() {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }
            // Step 1: Clear the user's session and any associated data
            handleLogout()
            // Step 2: Clear biometric-related data
            connectbizPreference.setBiometricSkippedByUser(false) // Mark biometric as skipped
            connectbizPreference.setBiometricEnabled(false) // Disable biometric authentication
            connectbizPreference.setBiometricDisabledByUser(false) // Reset biometric disable flag
            // Step 5: Update UI state to reflect that logout has been completed
            _state.update {
                it.copy(
                    isHomeUrl = false,
                    isWebViewReload = true,
                    isLoading = false,
                    showConflictErrorPopup = false,
                )
            }
        }
    }
}
