package jp.co.dhbk.connectbiz.ui.destinations.home

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Message
import android.util.Base64
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.webkit.ConsoleMessage
import android.webkit.CookieManager
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebStorage
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.FileProvider
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import jp.co.dhbk.connectbiz.core.common.extensions.string.convertFileExtensionToMimeType
import jp.co.dhbk.connectbiz.core.common.extensions.string.findMatchFileExtensionForPdf
import jp.co.dhbk.connectbiz.core.common.extensions.string.findMatchFileExtensionForDownload
import jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview.RequestInspectorJavaScriptInterface
import jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview.RequestInspectorWebViewClient
import jp.co.dhbk.connectbiz.ui.destinations.home.requestinspectorwebview.WebViewRequest
import jp.co.dhbk.connectbiz.ui.destinations.home.utils.FileDownloadHelper
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationRequired
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isFileSelectionInProgress
import jp.co.dhbk.connectbiz.core.common.extensions.string.findMatchMimeTypeForDownload
import jp.co.dhbk.connectbiz.core.common.extensions.string.findMatchMimeTypeOfApplicationPdf
import jp.co.dhbk.connectbiz.core.common.model.FileExtensions
import jp.co.dhbk.connectbiz.core.common.model.MimeTypes
import jp.co.dhbk.connectbiz.core.common.utils.runCatchingOrThrow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber
import java.io.File

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun AndroidWebView(
    viewModelUrl: String,
    onBaseUrlStart: () -> Unit,
    onLogout: () -> Unit,
    onSettings: () -> Unit,
    onReceiveIdToken: (String) -> Unit,
    onUrlChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    homeViewModel: HomeViewModel = hiltViewModel(),
) {
    var isPageLoading by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val primaryWebView = remember { WebView(context) }
    var secondaryWebView: WebView? by remember { mutableStateOf(null) }
    var isSecondaryWindowActive by remember { mutableStateOf(false) }
    var filePathCallback by remember { mutableStateOf<ValueCallback<Array<Uri>>?>(null) }
    var downloadProgress by remember { mutableIntStateOf(0) }
    var isDownloading by remember { mutableStateOf(false) }
    val homeState by homeViewModel.state.collectAsStateWithLifecycle()
    val activityLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK && result.data != null) {
            val dataUri = result.data?.data // Get the selected file URI
            if (dataUri != null) {
                filePathCallback?.onReceiveValue(arrayOf(dataUri)) // Pass the file URI to WebView
            } else {
                filePathCallback?.onReceiveValue(null) // Notify WebView of no selection
            }
        } else {
            filePathCallback?.onReceiveValue(null) // Notify WebView of cancellation
        }
        filePathCallback = null // Clear callback
        isFileSelectionInProgress = false
    }

    LaunchedEffect(homeState.isWebViewReload) {
        if (homeState.isWebViewReload) {
            clearWebViewSession(primaryWebView) {
                Timber.e("Webview Cleared!")
                primaryWebView.loadUrl(homeViewModel.apiConfig.mfaEnable)
            }
        }
    }

    // Function to close and clean up the secondary WebView
    fun closeSecondaryWebView() {
        secondaryWebView?.let {
            (it.parent as? ViewGroup)?.removeView(it)
            it.destroy()
        }
        secondaryWebView = null
        isSecondaryWindowActive = false
    }

    // Function to open the secondary WebView
    fun openSecondaryWebView(url: String) {
        Timber.d("openSecondaryWebView $url")
        if (secondaryWebView == null) {
            secondaryWebView = WebView(context).apply {
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    mediaPlaybackRequiresUserGesture = false
                    javaScriptCanOpenWindowsAutomatically = true
                    setSupportMultipleWindows(true)
                }

                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                )

                webViewClient = object : WebViewClient() {
                    override fun shouldOverrideUrlLoading(
                        view: WebView?,
                        request: WebResourceRequest?,
                    ): Boolean {
                        val newUrl = request?.url.toString()
                        if (!newUrl.contains("Video/Detail", ignoreCase = true)) {
                            closeSecondaryWebView()
                        }
                        return false
                    }
                }

                webChromeClient = object : WebChromeClient() {
                    private var customView: View? = null
                    private var customViewCallback: CustomViewCallback? = null
                    private var fullScreenContainer: FrameLayout? = null
                    private var originalSystemUiVisibility: Int = 0

                    @RequiresApi(Build.VERSION_CODES.R)
                    override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
                        if (customView != null) {
                            onHideCustomView()
                            return
                        }
                        customView = view
                        customViewCallback = callback

                        val activity = secondaryWebView?.context as? Activity ?: return

                        // Save original system UI visibility state
                        originalSystemUiVisibility = activity.window.decorView.systemUiVisibility

                        // Hide status bar and navigation bar using WindowInsetsController
                        activity.window.insetsController?.let { controller ->
                            controller.hide(WindowInsets.Type.systemBars())
                            controller.systemBarsBehavior =
                                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                        }

                        // Enable full-screen mode
                        enterFullScreen(activity)
                        // Create a full-screen container
                        fullScreenContainer = FrameLayout(activity).apply {
                            layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT,
                            )
                            addView(customView)
                        }

                        // Add full-screen view to activity
                        activity.window.decorView.findViewById<ViewGroup>(android.R.id.content)
                            .addView(fullScreenContainer)

                        // Hide WebView
                        secondaryWebView?.visibility = View.GONE
                    }

                    @RequiresApi(Build.VERSION_CODES.R)
                    override fun onHideCustomView() {
                        val activity = secondaryWebView?.context as? Activity ?: return
                        // Restore original system UI visibility
                        activity.window.decorView.systemUiVisibility = originalSystemUiVisibility
                        // Show system bars again
                        activity.window.insetsController?.show(WindowInsets.Type.systemBars())
                        // Exit full-screen mode
                        exitFullScreen(activity)
                        // Remove full-screen view
                        fullScreenContainer?.let {
                            activity.window.decorView.findViewById<ViewGroup>(android.R.id.content)
                                .removeView(it)
                        }
                        fullScreenContainer = null
                        customView = null
                        customViewCallback?.onCustomViewHidden()
                        // Restore WebView visibility
                        secondaryWebView?.visibility = View.VISIBLE
                    }

                    // Enable full-screen mode
                    @RequiresApi(Build.VERSION_CODES.R)
                    private fun enterFullScreen(activity: Activity) {
                        activity.window.insetsController?.let { controller ->
                            controller.hide(WindowInsets.Type.systemBars())
                            controller.systemBarsBehavior =
                                WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                        }
                        // Force landscape mode
                        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                    }

                    // Exit full-screen mode
                    @RequiresApi(Build.VERSION_CODES.R)
                    private fun exitFullScreen(activity: Activity) {
                        activity.window.insetsController?.show(WindowInsets.Type.systemBars())
                        // Restore portrait mode
                        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                    }

                    override fun onCloseWindow(window: WebView?) {
                        closeSecondaryWebView()
                    }
                }
            }
        }
        secondaryWebView?.loadUrl(url)
        isSecondaryWindowActive = true
    }

    // Function to create the secondary WebView during multi-window requests
    fun createSecondaryWebView(resultMsg: Message?): Boolean {
        if (resultMsg?.obj is WebView.WebViewTransport) {
            val transport = resultMsg.obj as WebView.WebViewTransport
            if (secondaryWebView == null) {
                secondaryWebView = WebView(context).apply {
                    settings.apply {
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        mediaPlaybackRequiresUserGesture = false
                        javaScriptCanOpenWindowsAutomatically = true
                        setSupportMultipleWindows(true)
                    }
                }
            }
            transport.webView = secondaryWebView
            resultMsg.sendToTarget()
            isSecondaryWindowActive = true
            return true
        }
        return false
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .imePadding()
            .statusBarsPadding()
            .navigationBarsPadding(),
    ) {
        // Primary WebView
        AndroidView(
            factory = { context ->
                primaryWebView.apply {
                    settings.apply {
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        useWideViewPort = true
                        builtInZoomControls = true
                        javaScriptCanOpenWindowsAutomatically = true
                        mediaPlaybackRequiresUserGesture = false
                        allowFileAccess = true
                        cacheMode = WebSettings.LOAD_NO_CACHE
                        safeBrowsingEnabled = true
                        allowContentAccess = true
                        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        userAgentString = homeViewModel.getUserAgentJson()
                        setSupportZoom(false)
                        setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                        setSupportMultipleWindows(false)
                    }

                    primaryWebView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    primaryWebView.addJavascriptInterface(
                        RequestInspectorJavaScriptInterface(primaryWebView, onReceiveIdToken),
                        "RequestInspection",
                    )
                    // Main Webview Alignment
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT,
                    )
                    Timber.d("JavaScript interface added: RequestInspection")
                    // Check for session cookies
                    homeViewModel.saveCookies(homeViewModel.apiConfig.baseUrl)
                    Timber.d(
                        "Cookies saved for base URL: %s",
                        homeViewModel.apiConfig.baseUrl,
                    )
                    val webViewClient: WebViewClient = object : RequestInspectorWebViewClient(
                        this@apply,
                        onReceiveIdToken,
                    ) {
                        override fun shouldInterceptRequest(
                            view: WebView,
                            webViewRequest: WebViewRequest,
                        ): WebResourceResponse? {
                            isPageLoading = false
                            val url = webViewRequest.url
                            when {
                                url.startsWith(
                                    "${homeViewModel.apiConfig.baseUrl}signin-oidc",
                                    ignoreCase = true,
                                ) -> {
                                    Timber.e("Interceptor Loading url $url")
                                    if (webViewRequest.formParameters.size > 0) {
                                        webViewRequest.formParameters.forEach { (key, value) ->
                                            if (key == "id_token") {
                                                onReceiveIdToken(value)
                                                val jwtPayload = value.split(
                                                    ".",
                                                ).getOrElse(1) { "" }
                                                val json = String(
                                                    Base64.decode(
                                                        jwtPayload,
                                                        Base64.URL_SAFE,
                                                    ),
                                                )
                                                try {
                                                    val jsonObject = JSONObject(json)
                                                    val userId = jsonObject.optString(
                                                        "sub",
                                                        "Unknown",
                                                    )
                                                    Timber.e("Decoded User ID: $userId")
                                                } catch (e: Exception) {
                                                    Timber.e(
                                                        "Failed to decode JWT: " +
                                                            "${e.message}",
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }

//                                url.contains(
//                                    homeViewModel.apiConfig.mfaEnable,
//                                    ignoreCase = true,
//                                ) -> {
//                                    Timber.e("Login session expired. URL: $url")
//                                    onLogout()
//                                }
                            }
                            return super.shouldInterceptRequest(view, webViewRequest)
                        }

                        override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                            super.onPageStarted(view, url, favicon)
                            isPageLoading = true
                            Timber.e("On page Load $url")
                            if (url == homeViewModel.apiConfig.baseUrl) {
                                onBaseUrlStart()

                                // Handling push notification after sign_in DAHL-85
                                val notificationUrl = homeViewModel.loadNotification()
                                if (notificationUrl != null && notificationUrl != "" &&
                                    !notificationUrl.contains("Safety/Reply")
                                ) {
                                    homeViewModel.clearNotification()
                                    view.loadUrl(
                                        notificationUrl.toString(),
                                    ) // Change the URL to the notification URL
                                } else {
                                    homeViewModel.updateTargetUrlStatus(
                                        homeViewModel.apiConfig.baseUrl,
                                    )
                                }
                            } else if (url.contains("about:blank")) {
                                view.loadUrl(homeViewModel.apiConfig.baseUrl)
                            } else if (url == homeViewModel.apiConfig.baseUrl + "/signin-oidc") {
                                view.loadUrl(homeViewModel.apiConfig.baseUrl)
                            }
                        }

                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            Timber.e("Page finished loading: $url")
                            onUrlChange(url.toString())
//                            if (url.toString().contains("about:blank") ) {
//                                // Redirecting immediately to avoid showing the blank page
//                                view?.loadUrl(homeViewModel.apiConfig.baseUrl)
//                                return
//                            }

//                            primaryWebView.evaluateJavascript(
//                                "(function() { return document.body.getAttribute('data-refreshed'); })();"
//                            ) { result ->
//                                if (result != "true" && primaryWebView.canGoBack().not() && !isFirstPageRefreshed) {
//                                    isFirstPageRefreshed = true
//                                    primaryWebView.evaluateJavascript(
//                                        "document.body.setAttribute('data-refreshed', 'true');",
//                                        null
//                                    )
//                                    Timber.e("First page refreshed: $url")
//                                }
//                            }

                            homeViewModel.saveLastVisitedUrl(url.toString())

                            // Clear notification data if the notification URL is loaded
                            if (url == homeViewModel.loadNotification()) {
                                homeViewModel.clearNotification()
                            }

                            // Handling ID token extraction from specific URL
                            if (url.toString().startsWith(
                                    "${homeViewModel.apiConfig.baseUrl}signin-oidc",
                                    ignoreCase = true,
                                )
                            ) {
                                val idToken = extractIdTokenFromUrl(url.toString())
                                if (idToken == null) {
                                    view?.loadUrl(homeViewModel.apiConfig.baseUrl)
                                } else {
                                    idToken?.let {
                                        Timber.d("Extracted ID Token: $it")
                                        onReceiveIdToken(idToken)
                                    }
                                }
                            }

                            isPageLoading = false
                        }

                        private fun extractIdTokenFromUrl(url: String): String? {
                            return Uri.parse(url).fragment
                                ?.split("#")
                                ?.firstOrNull { it.startsWith("id_token=") }
                                ?.substringAfter("id_token=")
                        }

                        override fun shouldOverrideUrlLoading(
                            view: WebView?,
                            request: WebResourceRequest?,
                        ): Boolean {
                            request?.url?.let { url ->
                                Timber.e("Loading url $url")
                                if (isAuthenticationRequired && (
                                        url.toString() == homeViewModel.apiConfig.baseUrl ||
                                            url.toString().contains(
                                                "policy/privacy_policy_1.html",
                                            ) || url.toString().contains("Base/OpenTerms")
                                        )
                                ) {
                                    view?.stopLoading()
                                    homeViewModel.receiveUrl(url.toString())
                                } else {
                                    val internalDomains = listOf(
                                        homeViewModel.apiConfig.baseUrl,
                                        homeViewModel.apiConfig.mfaEnable,
                                        "https://www.payslip-test.lifeing-board.com/payslip/index",
                                        "https://www.payslip.dhbk-connect-biz.com/payslip/index",
                                        "https://pl.dhbk.co.jp/loan/confirmConsent.html",
                                    )
                                    if (url.toString().contains(
                                            "Video/Detail",
                                            ignoreCase = true,
                                        )
                                    ) {
                                        Timber.d("Detected video URL: $url")
                                        openSecondaryWebView(url.toString())
                                        return true
                                    }
                                    when {
                                        url.toString().contains(
                                            "MicrosoftIdentity/MyAccount/SignOut",
                                            ignoreCase = true,
                                        ) -> {
                                            Timber.e("Logout called!")
                                            clearWebViewSession(primaryWebView) {
                                                onLogout() // Trigger logout
                                                view?.loadUrl(homeViewModel.apiConfig.mfaEnable)
                                            }
                                            return true
                                        }

                                        url.toString().contains("exclude-this-resource") -> {
                                            Timber.d("WebView Skipping URL: $url")
                                            return true
                                        }

                                        url.toString().contains(
                                            "app/settings",
                                            ignoreCase = true,
                                        ) -> {
                                            onSettings()
                                            return true
                                        }

                                        url.toString().contains("payslip/index") ||
                                            url.toString()
                                                .contains("manager/home") -> {
                                            return false
                                        }

                                        url.scheme == "tel" -> {
                                            val intent = Intent(Intent.ACTION_DIAL, url)
                                            context.startActivity(intent)
                                            return true
                                        }
// For Download file exclude
                                        url.toString().findMatchFileExtensionForPdf() && !url
                                            .toString().contains("MessageFileDownload") && !url
                                            .toString().contains("Download")
                                        -> {
                                            val browserIntent = Intent(
                                                Intent.ACTION_VIEW,
                                                Uri.parse(url.toString()),
                                            ).apply {
                                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                            }
                                            view?.context?.startActivity(browserIntent)
                                            return true
                                        }

                                        url.toString().contains(homeViewModel.apiConfig.baseUrl) ||
                                            url.toString().contains(
                                                homeViewModel.apiConfig.mfaEnable,
                                            ) && !isAuthenticationRequired -> {
                                            view?.loadUrl(url.toString())
                                            return false
                                        }

                                        url.toString().contains("policy/privacy_policy_1.html") -> {
                                            openUrlInExternalBrowser(
                                                context,
                                                url.toString(),
                                                homeViewModel,
                                            ) {
                                                view?.loadUrl(url.toString())
                                            }
                                            return true
                                        }

                                        url.toString().contains("about:blank") -> {
                                            homeViewModel.receiveUrl(url.toString())
                                            return true
                                        }

                                        isExternalUrl(url, internalDomains) -> {
                                            openUrlInExternalBrowser(
                                                context,
                                                url.toString(),
                                                homeViewModel,
                                            ) {
                                                view?.loadUrl(url.toString())
                                            }
                                            return true
                                        }

                                        else -> {
                                            return false
                                        }
                                    }
                                }
                            }
                            return false
                        }

                        override fun onReceivedHttpError(
                            view: WebView?,
                            request: WebResourceRequest?,
                            errorResponse: WebResourceResponse?,
                        ) {
                            super.onReceivedHttpError(view, request, errorResponse)
                            Timber.e("HTTP error: ${errorResponse?.statusCode}")
                            val statusCode = errorResponse?.statusCode
                            val url = request?.url.toString()
                            // Log the error for debugging purposes
                            Timber.e("HTTP error: Status code: $statusCode, URL: $url")

                            // Pass the status code directly to the ViewModel
                            if (statusCode != null) {
                                homeViewModel.handleServerError(statusCode, url)
                            } else {
                                Timber.e(
                                    "Error response or status code is null for URL:" +
                                        " $url",
                                )
                            }
                        }
                    }

                    primaryWebView.webViewClient = webViewClient

                    webChromeClient = object : WebChromeClient() {
                        override fun onCreateWindow(
                            view: WebView?,
                            isDialog: Boolean,
                            isUserGesture: Boolean,
                            resultMsg: Message?,
                        ): Boolean {
                            // Handle new window creation in the secondary WebView
                            return createSecondaryWebView(resultMsg)
                        }

                        override fun onCloseWindow(window: WebView?) {
                            // Handle closure of the secondary WebView
                            closeSecondaryWebView()
                        }

                        override fun onShowFileChooser(
                            webView: WebView?,
                            newFilePathCallback: ValueCallback<Array<Uri>>?,
                            fileChooserParams: FileChooserParams?,
                        ): Boolean {
                            filePathCallback?.onReceiveValue(null)
                            filePathCallback = newFilePathCallback
                            isFileSelectionInProgress = true
                            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                                addCategory(Intent.CATEGORY_OPENABLE)
                                type = MimeTypes.ANY
                            }
                            activityLauncher.launch(intent)
                            return true
                        }

                        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                            consoleMessage?.message()?.let { message ->
                                Timber.d("Console message: $message")
                            }
                            return true
                        }

                        override fun onProgressChanged(view: WebView?, newProgress: Int) {
                            Timber.d("Loading progress: $newProgress%")
                            super.onProgressChanged(view, newProgress)
                            isPageLoading = newProgress < 100
                        }
                    }

                    setDownloadListener { url, _, contentDisposition, mimeType, _ ->

                        isDownloading = true
                        try {
                            fun handleDownloadAndOpen(
                                url: String,
                                mimeType: String,
                                contentDisposition: String,
                            ) {
                                FileDownloadHelper.handleFileDownload(
                                    url,
                                    mimeType,
                                    contentDisposition,
                                    homeViewModel,
                                    onProgressUpdate = { progress ->
                                        downloadProgress = progress
                                        isDownloading = progress < 100
                                    },
                                    onDownloadComplete = { filePath ,fileName->
                                        isDownloading = false

                                        Timber.d("File name from download $fileName")

                                        val ext = filePath.substringAfterLast('.', "").lowercase()

// fallbacks if mimeType is garbage (application/mp4, application/image, etc.)
                                        val guessedMime = when (ext) {
                                            "jpg","jpeg","png","gif","bmp","webp" -> "image/*"
                                            "pdf" -> "application/pdf"
                                            "mp4","mkv","avi","mov","flv","3gp","webm" -> "video/*"
                                            else -> "*/*"
                                        }

// normalize actual mimeType (server can lie)
                                        val normalizedMime = mimeType?.lowercase() ?: guessedMime

                                        val fileType = when {
                                            // fix: handle cases like application/mp4, application/image, etc.
                                            normalizedMime.contains("image") || ext in listOf("jpg","jpeg","png","gif","bmp","webp") -> "image"
                                            normalizedMime.contains("pdf")   || ext == "pdf" -> "pdf"
                                            normalizedMime.contains("video") || ext in listOf("mp4","mkv","avi","mov","flv","3gp","webm") -> "video"
                                            else -> "other"
                                        }

                                        when (fileType) {
                                            "image", "pdf", "video" -> {
                                                // same function; the screen decides what to render based on fileType
                                                homeViewModel.navigateToPdfViewer(
                                                    url = filePath,
                                                    fileName=fileName,
                                                    isRemote = false,
                                                    fileType = fileType
                                                )
                                            }
                                            else -> {
                                                openFileInApp(context, filePath) // fallback for docx, zip, etc.
                                            }
                                        }
                                    },
                                    onDownloadFailed = { message ->
                                        isDownloading = false
                                        Timber.e(message)
                                    },
                                )
                            }

                            when {
                                url.contains(
                                    "MessageFileDownload",
                                    ignoreCase = true,
                                ) || url.contains(
                                    "Download",
                                    ignoreCase = true,
                                ) || (
                                    url.findMatchFileExtensionForDownload() ||
                                        mimeType.findMatchMimeTypeForDownload()
                                    ) &&
                                    !url.contains("Base/OpenTerms") &&
                                    !url.contains("operationGuide") -> {
                                    handleDownloadAndOpen(url, mimeType, contentDisposition)
                                }

                                url.contains("Base/OpenTerms", ignoreCase = true) &&
                                    mimeType.findMatchMimeTypeOfApplicationPdf() -> {
                                    homeViewModel.navigateToPdfViewer(url,isRemote = true)
                                }

                                url.contains(
                                    "operationGuide",
                                    ignoreCase = true,
                                ) && mimeType.findMatchMimeTypeOfApplicationPdf() -> {
                                    homeViewModel.navigateToPdfViewer(url,isRemote = true)
                                }

                                else -> {
                                    if (isAuthenticationRequired) {
                                        homeViewModel.navigateToPdfViewer(url,isRemote =true)
                                    }
                                    Timber.e("Unsupported File Mimetype: $mimeType")
                                }
                            }
                        } catch (e: Exception) {
                            isDownloading = false
                            Timber.e("Error handling download: ${e.message}")
                        }
                    }
                    primaryWebView.webViewClient = webViewClient
//                    when (viewModelUrl) {
//                        homeViewModel.apiConfig.mfaEnable,
//                        homeViewModel.apiConfig.mfaDisable,
//                        -> {
//                            clearWebViewSession(primaryWebView) {
//                                primaryWebView.loadUrl(viewModelUrl)
//                            }
//                        }
//
//                        else -> {
//                            primaryWebView.loadUrl(viewModelUrl)
//                        }
//                    }
                    primaryWebView.loadUrl(viewModelUrl)
                }
                val parentLayout = FrameLayout(context)
                parentLayout.layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                )
                parentLayout.addView(primaryWebView)

                parentLayout
            },
            modifier = Modifier.fillMaxSize(),
        )

        // Secondary WebView
        if (isSecondaryWindowActive && secondaryWebView != null) {
            AndroidView(
                factory = { context ->
                    val parentLayout = FrameLayout(context)
                    parentLayout.layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT,
                    )
                    parentLayout.addView(secondaryWebView!!)

                    parentLayout
                },
                modifier = Modifier.fillMaxSize(),
            )
        }

        BackHandler {
            if (primaryWebView.canGoBack()) {
                primaryWebView.goBack()
            } else {
                primaryWebView.loadUrl("javascript:window.close()")
                Timber.e("Back Handler pressed")
            }
        }

        if (isDownloading || isPageLoading) {
            CustomLoadingScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White.copy(alpha = 0.7f)),
            )
        }
    }

    DisposableEffect(primaryWebView) {
        onDispose {
            primaryWebView.removeJavascriptInterface("RequestInspection")
            primaryWebView.destroy()
            secondaryWebView?.destroy()
        }
    }
}

@SuppressLint("QueryPermissionsNeeded")
fun openFileInApp(context: Context, filePath: String) {
    runCatchingOrThrow {
        val file = File(filePath)
        if (!file.exists()) {
            Timber.e("File does not exist: $filePath")
            return
        }

        val fileUri: Uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file,
        )

        // Determine MIME type based on file extension
        val mimeType = file.extension.lowercase().convertFileExtensionToMimeType()

        val intent = Intent(Intent.ACTION_VIEW).apply {
            if (mimeType.findMatchMimeTypeForDownload()) {
                setDataAndType(fileUri, mimeType)
            } else {
                data = fileUri
            }
            flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
        }

        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        } else {
            Timber.e("No application found to open files of type: $mimeType")
        }
    }
}

fun isExternalUrl(url: Uri, internalDomains: List<String>): Boolean {
    val host = url.host
    if (host == null) return true
    return internalDomains.none { internalDomain ->
        val internalDomainHost = Uri.parse(internalDomain).host
        internalDomainHost != null && host.contains(internalDomainHost)
    }
}

fun openUrlInExternalBrowser(
    context: Context,
    url: String,
    homeViewModel: HomeViewModel,
    onHasNoBrowser: () -> Unit,
) {
    Timber.e(url)

    // Check if it's a PDF or image file
    val extension = url.substringAfterLast('.', "").lowercase()
    when (extension) {
        FileExtensions.PDF -> {
            Timber.e("Opening PDF: ${Uri.parse(url)}")
            homeViewModel.navigateToPdfViewer(url, isRemote = true, fileType = "pdf")
        }
        FileExtensions.image.JPG, FileExtensions.image.JPEG,
        FileExtensions.image.PNG, FileExtensions.image.GIF -> {
            Timber.e("Opening image: ${Uri.parse(url)}")
            homeViewModel.navigateToPdfViewer(url, isRemote = true, fileType = "image")
        }
        else -> {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            val pm = context.packageManager
            pm.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)?.let {
                if (it.activityInfo.packageName != context.packageName) {
                    context.startActivity(intent)
                } else {
                    onHasNoBrowser()
                }
            } ?: run {
                onHasNoBrowser()
            }
        }
    }
}

fun clearWebViewSession(webView: WebView, onSessionCleared: () -> Unit) {
    CoroutineScope(Dispatchers.Main).launch {
        val cookieManager = CookieManager.getInstance()
        cookieManager.removeAllCookies { success ->
            if (success) {
                Timber.d("Cookies cleared")
            } else {
                Timber.e("Failed to clear cookies")
            }

            webView.clearCache(true) // Clear WebView cache
            webView.clearHistory() // Clear history
            webView.clearFormData() // Clear form data
            webView.clearSslPreferences() // Clear SSL preferences
            WebStorage.getInstance().deleteAllData() // Clear WebView storage

            Timber.d("WebView session cleared")

            // Invoke the callback after clearing is done
            onSessionCleared()
        }
    }
}
