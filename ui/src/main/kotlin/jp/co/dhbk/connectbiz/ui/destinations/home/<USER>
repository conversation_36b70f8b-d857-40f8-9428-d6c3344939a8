@file:Suppress("DEPRECATION")

package jp.co.dhbk.connectbiz.ui.destinations.home

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun DownloadProgressIndicator(
    downloadProgress: Int,
    message: String,
    modifier: Modifier = Modifier,
) {
    val progress = remember(downloadProgress) { downloadProgress / 1000000f }

    Card(
        // elevation = 4.dp,
        shape = MaterialTheme.shapes.medium,
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
    ) {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge, // Customize text style
                modifier = Modifier.padding(bottom = 8.dp),
                // Space between text and progress bar
            )
            Box(
                modifier = Modifier.fillMaxWidth(), // Full width for the progress indicator
                contentAlignment = Alignment.Center, // Center the percentage text
            ) {
                LinearProgressIndicator(
                    progress = progress,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.fillMaxWidth().height(4.dp),
                )
            }
        }
    }
}
