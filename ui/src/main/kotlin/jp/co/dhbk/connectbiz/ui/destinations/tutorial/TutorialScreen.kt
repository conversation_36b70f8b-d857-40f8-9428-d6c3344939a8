package jp.co.dhbk.connectbiz.ui.destinations.tutorial

import android.app.Activity
import android.os.Build
import androidx.activity.compose.BackHandler
import androidx.annotation.RequiresApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.BackgroundOverlay
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.PrimaryLight
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography
import jp.co.dhbk.connectbiz.ui.destinations.tutorial.TutorialViewModel.TutorialPageState
import jp.co.dhbk.connectbiz.ui.navigation.ConnectivityViewModel
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalButton
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalImageDisplay
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTextView

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
fun TutorialScreen(
    modifier: Modifier = Modifier,
    viewModel: TutorialViewModel = hiltViewModel(),
    connectivityViewModel: ConnectivityViewModel = hiltViewModel(),
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val isConnected = connectivityViewModel.isConnected.collectAsState()
    val activity = LocalContext.current as Activity

    BackHandler {
        activity.finish()
    }

    TutorialScreen(
        state = state,
        onNextClick = viewModel::moveToNextPage,
        onStartClick = {
            viewModel.updateTutorialShow(true)
            if (viewModel.isTutorialShow()) {
                viewModel.navigateToLogin(isConnected.value)
            }
        },
        onPageChange = viewModel::setPage,
        modifier = modifier,
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun TutorialScreen(
    state: TutorialViewModel.State,
    onNextClick: () -> Unit,
    onStartClick: () -> Unit,
    onPageChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    val pageCount = 4
    val pagerState = rememberPagerState(pageCount = { pageCount })
    val currentOnPageChange by rememberUpdatedState(onPageChange)

    LaunchedEffect(state.currentPage) {
        pagerState.animateScrollToPage(state.currentPage)
    }

    // Update ViewModel when the pager state changes
    LaunchedEffect(pagerState.currentPage) {
        currentOnPageChange(pagerState.currentPage)
    }

    ConstraintLayout(
        modifier = modifier
            .fillMaxSize()
            .padding(
                WindowInsets.statusBars
                    .union(WindowInsets.navigationBars)
                    .asPaddingValues(),
            )
            .background(BackgroundOverlay),
    ) {
        val (pager, dotIndicator, button, commonContent) = createRefs()

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.constrainAs(pager) {
                top.linkTo(parent.top)
                bottom.linkTo(commonContent.top, margin = 10.dp)
                height = Dimension.fillToConstraints
            },
        ) { pageIndex ->
            TutorialPage(pageIndex)
        }

        GlobalTextView(
            text = stringResource(R.string.toturial_common_content),
            fontSize = TemplateTypography.body2.fontSize,
            fontWeight = TemplateTypography.body2.fontWeight,
            lineHeight = TemplateTypography.body2.lineHeight,
            textAlign = TextAlign.Center,
            color = TemplateColors.TextTertiary,
            maxLines = 1,
            modifier = Modifier.constrainAs(commonContent) {
                bottom.linkTo(dotIndicator.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            },
        )

        Row(
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .constrainAs(dotIndicator) {
                    bottom.linkTo(button.top, margin = 20.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
                .fillMaxWidth(),
        ) {
            repeat(pageCount) { index ->
                DotIndicator(
                    isSelected = index == state.currentPage,
                    modifier = Modifier.padding(4.dp),
                )
            }
        }

        GlobalButton(
            text = if (state.currentPage == 3) {
                stringResource(id = R.string.toturial_button_start)
            } else {
                stringResource(id = R.string.toturial_button_next)
            },
            fontWeight = FontWeight.Bold,
            onClick = {
                if (state.currentPage < 3) {
                    onNextClick()
                } else {
                    onStartClick()
                }
            },
            cornerRadius = 8.dp,
            textColor = TemplateColors.ButtonTextPrimary,
            modifier = Modifier
                .constrainAs(button) {
                    bottom.linkTo(parent.bottom, margin = 42.dp)
                    start.linkTo(parent.start, margin = 16.dp)
                    end.linkTo(parent.end, margin = 16.dp)
                }
                .height(50.dp)
                .fillMaxWidth(0.9f),
        )

        if (state.isLoading) {
            CustomLoadingScreen(
                modifier = Modifier.fillMaxSize()
                    .background(Color.White.copy(alpha = 0.7f)),
            )
        }
    }
}

@Composable
fun DotIndicator(isSelected: Boolean, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .size(8.dp)
            .background(
                color = if (isSelected) {
                    PrimaryLight
                } else {
                    PrimaryLight.copy(alpha = 0.3f)
                },
                shape = CircleShape,
            ),
    )
}

@Composable
fun TutorialPage(pageIndex: Int, viewModel: TutorialViewModel = hiltViewModel()) {
    val state by viewModel.tutorialPageState.collectAsState()
    when (pageIndex) {
        0, 1, 2 -> {
            TutorialPage(state = state)
        }
        3 -> {
            TutorialPageFour(state = state)
        }
    }
}

@Composable
fun TutorialPage(state: TutorialPageState, modifier: Modifier = Modifier) {
    val title = stringResource(id = state.title)
    val description = stringResource(id = state.description)
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp
    val imageHeightDp = screenHeightDp.dp * 0.3f // 30% of screen height

    ConstraintLayout(
        modifier = modifier
            .fillMaxSize()
            .padding(10.dp),
    ) {
        val (titleText, descriptionText, image) = createRefs()

        GlobalTextView(
            text = title,
            fontSize = TemplateTypography.title1Bold.fontSize,
            fontWeight = TemplateTypography.title1Bold.fontWeight,
            lineHeight = TemplateTypography.title1Bold.lineHeight,
            textAlign = TextAlign.Center,
            color = TemplateColors.TextPrimaryLight,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(titleText) {
                top.linkTo(parent.top, margin = 100.dp)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
                .wrapContentHeight(),
        )

        if (description.isNotEmpty()) {
            GlobalTextView(
                text = description,
                fontSize = TemplateTypography.body2.fontSize,
                fontWeight = TemplateTypography.body2.fontWeight,
                lineHeight = TemplateTypography.body2.lineHeight,
                textAlign = TextAlign.Center,
                color = TemplateColors.TextSecondary,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .constrainAs(descriptionText) {
                        top.linkTo(titleText.bottom, margin = 10.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                    .fillMaxWidth(),
            )
        } else {
            Spacer(modifier = Modifier.height(8.dp))
        }

        Box(
            modifier = Modifier
                .constrainAs(image) {
                    top.linkTo(descriptionText.bottom, margin = 20.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
                .height(imageHeightDp)
                .fillMaxWidth(),
        ) {
            GlobalImageDisplay(
                imageResId = state.imageResId,
                contentDescription = state.contentDescription,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit,
            )
        }
    }
}

@Composable
fun TutorialPageFour(state: TutorialPageState, modifier: Modifier = Modifier) {
    val title = stringResource(id = state.title)
    val configuration = LocalConfiguration.current
    val screenHeightDp = configuration.screenHeightDp
    val imageHeightDp = screenHeightDp.dp * 0.3f // 30% of screen height

    ConstraintLayout(
        modifier = modifier
            .fillMaxSize()
            .padding(10.dp),
    ) {
        val (titleText, _, image) = createRefs()

        GlobalTextView(
            text = title,
            fontSize = TemplateTypography.title1Bold.fontSize,
            fontWeight = TemplateTypography.title1Bold.fontWeight,
            lineHeight = TemplateTypography.title1Bold.lineHeight,
            textAlign = TextAlign.Center,
            color = TemplateColors.TextPrimaryLight,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(titleText) {
                top.linkTo(parent.top, margin = 100.dp)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
                .wrapContentHeight(),
        )

        Box(
            modifier = Modifier
                .constrainAs(image) {
                    top.linkTo(titleText.bottom, margin = 80.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
                .height(imageHeightDp)
                .fillMaxWidth(),
        ) {
            GlobalImageDisplay(
                imageResId = state.imageResId,
                contentDescription = "Tutorial Image four",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit,
            )
        }
    }
}

val mockTutorialPageState = TutorialPageState(
    title = R.string.toturial_page_one_title, // Mock title resource
    description = R.string.toturial_page_one_description, // Mock description resource
    imageResId = R.drawable.tutorial_image_one, // Placeholder image
    contentDescription = "Tutorial Image One",
)

val mockTutorialPageFourState = TutorialPageState(
    title = R.string.toturial_page_four_title, // Mock title resource
    description = R.string.empty_description, // Mock description resource
    imageResId = R.drawable.tutorial_image_four, // Placeholder image
    contentDescription = "Tutorial Image Four",
)

// Preview
@Preview(showBackground = true)
@Composable
private fun PreviewTutorialPage() {
    TutorialPage(
        state = mockTutorialPageState, // Use mock state directly
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewTutorialPageFour() {
    TutorialPageFour(
        state = mockTutorialPageFourState, // Use mock state directly
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
    )
}
