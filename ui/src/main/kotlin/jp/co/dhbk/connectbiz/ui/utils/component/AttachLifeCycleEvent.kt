package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import timber.log.Timber

@Composable
fun AttachLifeCycleEvent(
    onCreate: (() -> Unit)? = null,
    onStart: (() -> Unit)? = null,
    onResume: (() -> Unit)? = null,
) {
    val lifeCycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifeCycleOwner) {
        val eventObserver =
            LifecycleEventObserver { _, event ->
                when (event) {
                    Lifecycle.Event.ON_CREATE -> {
                        Timber.d("OnCreate")
                        onCreate?.invoke()
                    }
                    Lifecycle.Event.ON_START -> {
                        Timber.d("On Start")
                        onStart?.invoke()
                    }
                    Lifecycle.Event.ON_RESUME -> {
                        Timber.d("On Resume")
                        onResume?.invoke()
                    }
                    else -> {
                    }
                }
            }

        lifeCycleOwner.lifecycle.addObserver(eventObserver)
        onDispose {
            lifeCycleOwner.lifecycle.removeObserver(eventObserver)
        }
    }
}
