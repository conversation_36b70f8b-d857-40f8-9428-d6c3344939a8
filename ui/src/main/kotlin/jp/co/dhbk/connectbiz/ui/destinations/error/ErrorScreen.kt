package jp.co.dhbk.connectbiz.ui.destinations.error

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.ButtonTextPrimary
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.TextPrimaryLight
import jp.co.dhbk.connectbiz.ui.navigation.ConnectivityViewModel
import jp.co.dhbk.connectbiz.ui.navigation.ErrorState
import jp.co.dhbk.connectbiz.ui.navigation.NavStateViewModel
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalButton
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalImageDisplay
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTextView
import timber.log.Timber
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Composable function that displays an error screen based on the error state.
 */
@Composable
fun ErrorScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    isConnectedState: State<Boolean>? = null,
    errorState: State<ErrorState>? = null,
    connectivityViewModel: ConnectivityViewModel = hiltViewModel(),
    errorViewModel: ErrorViewModel = hiltViewModel(),
    navStateViewModel: NavStateViewModel = hiltViewModel(),
) {
    val isConnected = isConnectedState ?: connectivityViewModel.isConnected.collectAsState()
    var isPageLoading by remember { mutableStateOf(false) }
    val errorState by (errorState ?: errorViewModel.errorState.collectAsState())
    if (isConnected.value) {
        errorViewModel.setNetworkStatus(connectivityViewModel.isInternetAvailable())
    } else {
        errorViewModel.setNetworkStatus(isConnected.value)
    }

    LaunchedEffect(errorState) {
        when (errorState) {
            is ErrorState.NetworkError -> {
                // Handle network error state changes
                // You could trigger logging, analytics, or any other side effect here
                Timber.e("Network Error: ${ErrorState.NetworkError}")
            }
            is ErrorState.SystemError -> {
                // Handle system error state changes
                Timber.e("System Error: ${ErrorState.SystemError}")
            }
            is ErrorState.GeneralError -> {
                // Handle general error state changes
                Timber.e("General Error: ${ErrorState.SystemError}")
            }
            is ErrorState.None -> {
                errorViewModel.navigateBack()
            }
            else -> {
                // Optionally handle other states or do nothing
            }
        }
    }

    if (isPageLoading) {
        CustomLoadingScreen(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White.copy(alpha = 0.7f)),
        )
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(
                WindowInsets.statusBars
                    .union(WindowInsets.navigationBars)
                    .asPaddingValues(),
            ),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        when (errorState) {
            is ErrorState.NetworkError -> {
                ErrorContent(
                    title = stringResource(R.string.error_network_title),
                    description = stringResource(R.string.error_network_description),
                    buttonText = stringResource(R.string.error_network_button),
                    onRetry = {
//                        val context = LocalContext.current
                        isPageLoading = true
                        retryNetworkOperation(connectivityViewModel, errorViewModel)
                        GlobalScope.launch {
                            delay(200)
                            isPageLoading = false
                        }
                    },
                )
            }
            is ErrorState.SystemError -> {
                ErrorContent(
                    title = stringResource(R.string.error_system_title),
                    description = stringResource(R.string.error_system_description),
                    buttonText = stringResource(R.string.error_system_button),
                    onRetry = { retrySystemOperation(connectivityViewModel, errorViewModel) },
                )
            }
            is ErrorState.GeneralError -> {
                ErrorContent(
                    title = stringResource(R.string.error_general_title),
                    description = stringResource(R.string.error_general_description),
                    buttonText = stringResource(R.string.error_general_button),
                    onRetry = { retryGeneralOperation() },
                )
            }
            else -> { }
        }
    }
}

/**
 * Composable function to display the error content with a title, description, and a retry button.
 *
 * @param title The title text to display.
 * @param description The description text to display.
 * @param buttonText The text for the retry button.
 * @param onRetry The action to perform when the retry button is clicked.
 */
@Composable
fun ErrorContent(
    title: String,
    description: String,
    buttonText: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
        modifier.fillMaxSize()
            .padding(8.dp),
    ) {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(all = 8.dp)
                .align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            GlobalImageDisplay(
                imageResId = R.drawable.error,
                contentDescription = "Error Image",
                imageSize = 50.dp,
            )

            GlobalTextView(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.ExtraBold,
                textAlign = TextAlign.Center,
                color = TextPrimaryLight,
                modifier =
                Modifier
                    .width(300.dp)
                    .height(60.dp),
            )

            GlobalTextView(
                text = description,
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                textAlign = TextAlign.Center,
                color = TextPrimaryLight,
                maxLines = 4,
                modifier =
                Modifier
                    .width(300.dp)
                    .height(100.dp),
            )
        }

        GlobalButton(
            text = buttonText,
            fontWeight = FontWeight.Bold,
            onClick = { onRetry() },
            cornerRadius = 8.dp,
            textColor = ButtonTextPrimary,
            modifier =
            Modifier
                .align(Alignment.BottomCenter)
                .padding(24.dp)
                .height(50.dp),
        )
    }
}

private fun retryNetworkOperation(
    connectivityViewModel: ConnectivityViewModel,
    errorViewModel: ErrorViewModel,
) {
    // Update Error Status
    errorViewModel.setNetworkStatus(connectivityViewModel.isConnected.value)
}

/**
 * Logic to retry a system operation.
 */
private fun retrySystemOperation(
    connectivityViewModel: ConnectivityViewModel,
    errorViewModel: ErrorViewModel,
) {
    // Check if network is available before retrying
    if (!connectivityViewModel.isConnected.value) {
        errorViewModel.setNetworkStatus(false)
        return
    }

    // Network is available, retry system operation
    errorViewModel.setSystemStatus(true) // Set system error state
    // Retry logic (e.g., network call, operation, etc.)
}

/**
 * Logic to retry a general operation.
 */
private fun retryGeneralOperation() {
    // Logic for retrying a general operation goes here.
}

/*@Preview(showBackground = true, name = "Error Screen - Network Error")
@Composable
private fun PreviewErrorScreen() {
    val mockNavController = rememberNavController()
    val mockIsConnectedState = remember { mutableStateOf(true) }
    val mockErrorState = remember { mutableStateOf<ErrorState>(ErrorState.NetworkError) }

    ErrorScreen(
        navController = mockNavController,
        isConnectedState = mockIsConnectedState,
        errorState = mockErrorState,
    )
}*/
