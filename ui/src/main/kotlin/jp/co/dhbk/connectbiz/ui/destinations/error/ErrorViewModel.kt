package jp.co.dhbk.connectbiz.ui.destinations.error
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.connectbiz.ui.navigation.ErrorState
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

@HiltViewModel
class ErrorViewModel
@Inject
constructor(private val navigator: Navigator) : ViewModel() {
    private val _errorState = MutableStateFlow<ErrorState>(ErrorState.None)
    val errorState: StateFlow<ErrorState> = _errorState

    private val _isNetworkAvailable = MutableStateFlow(true)
    val isNetworkAvailable: StateFlow<Boolean> = _isNetworkAvailable

    private val _isSystemError = MutableStateFlow(false)
    val isSystemError: StateFlow<Boolean> = _isSystemError

    /**
     * Set the network availability status and update the error state accordingly.
     *
     * @param isAvailable True if the network is available, False otherwise.
     */
    fun setNetworkStatus(isAvailable: Boolean) {
        _isNetworkAvailable.value = isAvailable
        _errorState.value =
            if (!isAvailable) {
                ErrorState.NetworkError // Set to NetworkError if no network
            } else {
                ErrorState.None // Reset error state when network is available
            }
    }

    fun setSystemStatus(isError: Boolean) {
        if (_isSystemError.value != isError) {
            _isSystemError.value = isError
            updateErrorState()
        }
    }

    private fun updateErrorState() {
        _errorState.value = when {
            _isSystemError.value -> ErrorState.SystemError
            else -> ErrorState.None
        }
    }

    /**
     * Set the error state based on different error conditions.
     *
     * @param error The ErrorState to be set.
     */
    fun setErrorState(error: ErrorState) {
        _errorState.value = error
    }

    /**
     * Clears any error state.
     */
    fun clearErrorState() {
        _errorState.value = ErrorState.None
    }

    fun navigateBack() {
        navigator.navigateToBack()
    }
}
