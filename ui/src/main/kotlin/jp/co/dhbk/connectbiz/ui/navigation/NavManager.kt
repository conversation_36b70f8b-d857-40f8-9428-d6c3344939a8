package jp.co.dhbk.connectbiz.ui.navigation

import jp.co.dhbk.analytics.ConnectbizAnalyticConstant
import jp.co.dhbk.analytics.ConnectbizTracker
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NavManager
@Inject
constructor(private val connectBizTracker: ConnectbizTracker) {
    private val _routeInfo = MutableStateFlow(NavInfo())
    val routeInfo: StateFlow<NavInfo> = _routeInfo

    val navigationStack = mutableListOf<NavInfo>() // To hold the navigation history

    fun navigate(routeInfo: NavInfo?, screenName: String? = null) {
        if (routeInfo == null) {
            return
        }

        Timber.d("Navigating from: ${_routeInfo.value.id} to: ${routeInfo.id}")

        // Check if the route is the same as the last route
        if (_routeInfo.value.id == routeInfo.id) {
            Timber.d("Already on the target route, skipping navigation.")
            return
        }

        // Prevent adding duplicate routes to the stack
        if (navigationStack.isNotEmpty() && navigationStack.last().id == routeInfo.id) {
            Timber.d("Route $routeInfo already exists at the top of the stack, skipping.")
            return
        }

        // Push the current route onto the stack before navigating to a new one
        navigationStack.add(_routeInfo.value)
        _routeInfo.update { routeInfo }

        routeInfo.id?.let { destination ->
            connectBizTracker.logEvent(
                eventName = ConnectbizAnalyticConstant.NAVIGATE,
                screenName = screenName ?: "Unknown Screen",
                params = mapOf(
                    ConnectbizAnalyticConstant.Key.URI to destination,
                ),
            )
        }
    }

    fun goBack() {
        // If the navigation stack is not empty, pop the last route
        if (navigationStack.isNotEmpty()) {
            Timber.d("Going back to previous screen")

            // Get the previous route without removing it from the stack yet
            val previousRoute = navigationStack[navigationStack.size - 1]

            // Check if the last route is different from the current route
            if (previousRoute.id != _routeInfo.value.id) {
                Timber.d("Navigating back to: ${previousRoute.id}")
                _routeInfo.value = previousRoute // Update the current route

                // Now remove the route from the stack
                navigationStack.removeAt(navigationStack.size - 1)
            } else {
                Timber.d("Already on the previous route, skipping navigation.")
            }
        } else {
            Timber.d("No previous screen to go back to")
        }
    }

    /**
     * Navigate back to the previous route in the stack.
     * This should pop the last route from the stack and go to the previous screen.
     */
    fun navigateBack() {
        goBack()
    }
}
