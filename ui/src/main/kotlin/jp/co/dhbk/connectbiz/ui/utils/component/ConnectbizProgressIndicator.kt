package jp.co.dhbk.connectbiz.ui.utils.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.Dialog

@Composable
fun ConnectbizProgressIndicator(modifier: Modifier = Modifier, isDialogIndicator: Boolean = true) {
    if (isDialogIndicator) {
        Dialog(onDismissRequest = { /* TODO: Handle dismiss */ }) {
            ConnectbizProgressBar(modifier = modifier)
        }
    } else {
        ConnectbizProgressBar(modifier = modifier)
    }
}

@Composable
private fun ConnectbizProgressBar(modifier: Modifier = Modifier) {
    Box(modifier = modifier.fillMaxSize()) {
        CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
    }
}
