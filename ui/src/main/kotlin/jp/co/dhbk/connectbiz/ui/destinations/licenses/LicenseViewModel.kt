package jp.co.dhbk.connectbiz.ui.destinations.licenses

import androidx.compose.runtime.Stable
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import jp.co.dhbk.connectbiz.core.common.library.Library
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class LicenseViewModel @Inject constructor(
    private val repository: LicenseRepository, // For fetching libraries
    private val navigator: Navigator, // For navigation handling
) : ViewModel() {

    // StateFlow for libraries
    private val libraries = MutableStateFlow<List<Library>>(emptyList())
    val librariesFlow: StateFlow<List<Library>> get() = libraries

    // StateFlow for dialog state
    private val dialogState = MutableStateFlow<LicenseContentDialogState.State?>(null)
    val dialogStateFlow: StateFlow<LicenseContentDialogState.State?> get() = dialogState

    init {
        loadLibraries()
    }

    // Loads the list of libraries
    private fun loadLibraries() {
        viewModelScope.launch {
            try {
                val libraryList = repository.getLibraries()
                libraries.value = libraryList
            } catch (e: Exception) {
                Timber.e("Error loading libraries: ${e.message}")
                libraries.value = emptyList() // Show an empty list in case of error
            }
        }
    }

    // Shows the license dialog with content
    fun showLicenseDialog(name: String, content: String) {
        dialogState.value = LicenseContentDialogState.State(name, content)
    }

    // Hides the license dialog
    fun hideLicenseDialog() {
        dialogState.value = null
    }

    // Navigates back
    fun goBack() {
        navigator.navigateToBack()
    }
}

/**
 * License content dialog state
 *
 * @param initialState Initial state, default is `null`.
 */
@Stable
class LicenseContentDialogState(
    initialState: State? = null,
) {
    private var _currentState = mutableStateOf(initialState)

    var currentState: State?
        get() = _currentState.value
        set(value) {
            _currentState.value = value
        }

    fun show(name: String, content: String) {
        currentState = State(name, content)
    }

    fun hide() {
        currentState = null
    }

    data class State(
        val name: String,
        val content: String,
    )
}
