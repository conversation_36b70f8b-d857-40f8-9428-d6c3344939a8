package jp.co.dhbk.connectbiz.ui.destinations.splash

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import jp.co.dhbk.connectbiz.data.network.biometricauth.AuthenticationStatus
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography
import jp.co.dhbk.connectbiz.ui.destinations.splash.SplashViewModel.NavigationEvent
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalButton
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTextView

@Composable
fun AuthenticationCancelScreen(
    authenticationStatus: AuthenticationStatus,
    modifier: Modifier = Modifier,
    splashViewModel: SplashViewModel = hiltViewModel(),
) {
    val activity = LocalContext.current as FragmentActivity
    val authState by splashViewModel.authResult.collectAsStateWithLifecycle()

    BackHandler {
        // Custom back press logic
        splashViewModel.navigator.navigateToBack()
    }
    LaunchedEffect(authState) {
        splashViewModel.handleAuthResult()
    }

    // Handle Navigation Events
    LaunchedEffect(Unit) {
        splashViewModel.navigationEvents.collect { event ->
            when (event) {
                is NavigationEvent.NavigateToHome ->
                    splashViewModel.navigateToHome(event.url)
                is NavigationEvent.NavigateToErrorScreen ->
                    splashViewModel.navigateToErrorScreen()
                is NavigationEvent.NavigateToTutorialScreen ->
                    splashViewModel.navigateToTutorialScreen()
                is NavigationEvent.NavigateToMaintenanceScreen ->
                    splashViewModel.navigateToMaintenanceScreen(
                        event.message,
                    )
                is NavigationEvent.NavigateToForceUpdateScreen ->
                    splashViewModel.navigateToForceUpdateScreen(
                        event.message,
                        event.destinationLink,
                    )
                is NavigationEvent.NavigateToBiometricCancelScreen ->
                    splashViewModel
                        .navigateToBiometricCancel()
                is NavigationEvent.NavigateToMFEDisableScreen ->
                    splashViewModel.navigateToHome(event.url)
                is NavigationEvent.NavigateToMFEEnableScreen ->
                    splashViewModel.navigateToHome(event.url)
            }
        }
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        containerColor = TemplateColors.BackgroundLight,
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .background(TemplateColors.BackgroundLight),
        ) {
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(0.dp),
            ) {
                GlobalTextView(
                    text = stringResource(id = R.string.Authentication_Cancel_Title),
                    fontSize = TemplateTypography.title2Bold.fontSize,
                    fontWeight = TemplateTypography.title2Bold.fontWeight,
                    lineHeight = TemplateTypography.title2Bold.lineHeight,
                    color = TemplateTypography.title2Bold.color,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )

                GlobalTextView(
                    text = stringResource(id = R.string.Authentication_Cancel_Description),
                    fontSize = TemplateTypography.body2.fontSize,
                    fontWeight = TemplateTypography.body2.fontWeight,
                    lineHeight = TemplateTypography.body2.lineHeight,
                    color = TemplateColors.TextPrimaryLight,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )

                GlobalButton(
                    text = when (authenticationStatus) {
                        AuthenticationStatus.BIOMETRIC_AVAILABLE_ENABLED -> stringResource(
                            R.string.Authentication_Cancel_Button,
                        )
                        AuthenticationStatus.KEYGUARD_SECURE_ENABLED -> stringResource(
                            R.string.Authentication_Passcode_Button,
                        )
                        else -> stringResource(R.string.Authentication_Cancel_Button)
                    },
                    onClick = {
                        val screenType: ScreenType = ScreenType.SPLASH
                        splashViewModel.authenticate(activity, screenType)
                    },
                    modifier = Modifier.padding(vertical = 16.dp),
                    backgroundColor = TemplateColors.ButtonBackgroundPrimary,
                    textColor = Color.White,
                    enabled = true,
                )

                Text(
                    text = stringResource(id = R.string.Sign_in_link),
                    style = TemplateTypography.body2.copy(color = TemplateColors.TextLink),
                    modifier = Modifier
                        .clickable { splashViewModel.userLoginWith2FactorAuth() }
                        .fillMaxWidth(),
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}
