package jp.co.dhbk.connectbiz.ui.destinations.settings

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateTypography
import jp.co.dhbk.connectbiz.ui.destinations.settings.SettingViewModel.SettingState
import jp.co.dhbk.connectbiz.ui.utils.component.CustomLoadingScreen
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalTopAppBar

@Composable
internal fun SettingsScreen(settingViewModel: SettingViewModel = hiltViewModel()) {
    val settingsState by settingViewModel.state.collectAsStateWithLifecycle()
   /* val biometricLoadState by biometricViewModel.loadStateFlow.collectAsStateWithLifecycle()
    val authResult by biometricViewModel.authResult.collectAsStateWithLifecycle()*/
    val context = LocalContext.current

    BackHandler {
        settingViewModel.goBack()
    }

    SettingsScreen(
        state = settingsState,
        onNavigateBack = settingViewModel::goBack,
        onNavigateToAuth = {
            settingViewModel.navigateAuthScreen()
        },
        onNavigateToLicence = settingViewModel::navigateToLicence,
        onNavigateToSettings = {
            settingViewModel.navigateToSettings(context)
        },
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun SettingsScreen(
    state: SettingState,
    onNavigateBack: () -> Unit,
    onNavigateToAuth: () -> Unit,
    onNavigateToLicence: () -> Unit,
    onNavigateToSettings: () -> Unit,
) {
    Scaffold(
        containerColor = TemplateColors.BackgroundLight,
        topBar = {
            GlobalTopAppBar(
                text = stringResource(R.string.authentication_setting_title),
                onNavigateBack = onNavigateBack,
            )
        },
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .background(TemplateColors.BackgroundLight),
        ) {
            SettingRow(
                text = stringResource(R.string.App_Authentication_Settings),
                onNavigate = onNavigateToAuth,
            )

            SettingRow(
                text = stringResource(R.string.Notification_settings),
                onNavigate = onNavigateToSettings,
            )

            Spacer(modifier = Modifier.height(60.dp))

            Text(
                text = stringResource(R.string.about_app),
                modifier = Modifier.padding(start = 20.dp),
                color = TemplateColors.TextPrimaryLight,
                style = TemplateTypography.title3Bold,
            )

            Spacer(modifier = Modifier.height(4.dp))

            SettingRow(
                text = stringResource(R.string.licence),
                onNavigate = onNavigateToLicence,
            )

            if (state.isLoading) {
                CustomLoadingScreen(
                    modifier = Modifier.fillMaxSize()
                        .background(Color.White.copy(alpha = 0.7f)),
                )
            }
        }
    }
}

@Composable
fun SettingRow(text: String, onNavigate: () -> Unit, modifier: Modifier = Modifier) {
    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .clickable(onClick = onNavigate)
                .height(60.dp)
                .padding(start = 20.dp),
        ) {
            Text(
                text = text,
                modifier = Modifier.weight(1f),
                color = TemplateColors.TextPrimaryLight,
                style = TemplateTypography.body1,
            )
            IconButton(onClick = onNavigate) {
                Image(
                    painter = painterResource(id = R.drawable.forward_arrow),
                    contentDescription = "Navigate Forward",
                    modifier = Modifier.size(16.dp),
                )
            }
        }
        HorizontalDivider(
            color = TemplateColors.DividerColor,
            thickness = 0.5.dp,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview(showBackground = true)
@Composable
internal fun SettingsScreenPreview() {
    val mockSettingsState = SettingState(isLoading = false, isBiometricEnabled = true)

    val mockOnNavigateBack = {}
    val mockOnNavigateToAuth = {}
    val mockOnNavigateToLicence = {}
    val mackOnNavigationToSettings = {}

    SettingsScreen(
        state = mockSettingsState,
        onNavigateBack = mockOnNavigateBack,
        onNavigateToAuth = mockOnNavigateToAuth,
        onNavigateToLicence = mockOnNavigateToLicence,
        onNavigateToSettings = mackOnNavigationToSettings,
    )
}
