package jp.co.dhbk.connectbiz.ui.destinations.splash

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import androidx.hilt.navigation.compose.hiltViewModel
import jp.co.dhbk.connectbiz.data.network.biometricauth.ScreenType
import jp.co.dhbk.connectbiz.ui.R
import jp.co.dhbk.connectbiz.ui.designsystem.TemplateColors.BackgroundOverlay
import jp.co.dhbk.connectbiz.ui.destinations.splash.SplashViewModel.NavigationEvent
import jp.co.dhbk.connectbiz.ui.navigation.NavStateViewModel
import jp.co.dhbk.connectbiz.ui.utils.component.ConnectBizDialog

@Composable
fun SplashScreen(
    modifier: Modifier = Modifier,
    splashViewModel: SplashViewModel = hiltViewModel(),
    navStateViewModel: NavStateViewModel = hiltViewModel(),
) {
    val uiState = splashViewModel.uiState.collectAsState()
    val activity = LocalContext.current as FragmentActivity
    val authState by splashViewModel.authResult.collectAsState()

    // Initialize the ViewModel
    LaunchedEffect(Unit) {
        splashViewModel.initialize()
    }

    // Handle auth state changes
    LaunchedEffect(authState) {
        splashViewModel.handleAuthResult()
    }

    // Refresh remote config when logo animation is done
    LaunchedEffect(uiState.value.isLogoAnimationDone) {
        if (uiState.value.isLogoAnimationDone) {
            splashViewModel.refreshRemoteConfig(activity)
        }
    }

    // Handle navigation events
    LaunchedEffect(Unit) {
        splashViewModel.navigationEvents.collect { event ->
            when (event) {
                is NavigationEvent.NavigateToHome ->
                    splashViewModel.navigateToHome(event.url)
                is NavigationEvent.NavigateToErrorScreen ->
                    splashViewModel.navigateToErrorScreen()
                is NavigationEvent.NavigateToTutorialScreen ->
                    splashViewModel.navigateToTutorialScreen()
                is NavigationEvent.NavigateToMaintenanceScreen ->
                    splashViewModel.navigateToMaintenanceScreen(event.message)
                is NavigationEvent.NavigateToForceUpdateScreen ->
                    splashViewModel.navigateToForceUpdateScreen(
                        event.message,
                        event.destinationLink,
                    )
                is NavigationEvent.NavigateToBiometricCancelScreen ->
                    splashViewModel.navigateToBiometricCancel()
                is NavigationEvent.NavigateToMFEDisableScreen ->
                    splashViewModel.navigateToHome(event.url)
                is NavigationEvent.NavigateToMFEEnableScreen ->
                    splashViewModel.navigateToHome(event.url)
            }
        }
    }

    Box(
        modifier = modifier.fillMaxSize().background(BackgroundOverlay),
        contentAlignment = Alignment.Center,
    ) {
        SplashImage()
        when {
            uiState.value.showAuthenticationNotAvailableDialog -> ConnectBizDialog(
                title = stringResource(R.string.authentication_failed_title_splash),
                message = stringResource(R.string.authentication_failed_description_splash),
                enableCancel = true,
                negativeLabel = null,
                positiveLabel = stringResource(R.string.sign_in_btn),
                onClick = { splashViewModel.navigateToLoginFlow() },
            )
            uiState.value.showAuthenticationRetryDialog -> ConnectBizDialog(
                title = stringResource(R.string.authentication_retry_title_splash),
                message = stringResource(R.string.authentication_retry_description_splash),
                enableCancel = true,
                negativeLabel = null,
                positiveLabel = stringResource(R.string.retry_splash_btn),
                onClick = {
                    val screenType: ScreenType = ScreenType.SPLASH
                    splashViewModel.authenticate(activity, screenType)
                },
            )
            uiState.value.showAuthenticationFailedDialog -> ConnectBizDialog(
                title = stringResource(R.string.authentication_failed_title_splash),
                message = stringResource(R.string.authentication_failed_description_splash),
                enableCancel = true,
                negativeLabel = null,
                positiveLabel = stringResource(R.string.sign_in_btn),
                onClick = { splashViewModel.navigateToLoginFlow() },
            )
            uiState.value.isKeyGuardCancel -> ConnectBizDialog(
                title = stringResource(R.string.keyguard_error_popup_title),
                message = stringResource(R.string.keyguard_error_popup_description),
                enableCancel = true,
                negativeLabel = null,
                positiveLabel = stringResource(R.string.sign_in_btn),
                onClick = { splashViewModel.navigateToLoginFlow() },
            )
        }
    }
}

@Composable
fun SplashImage(modifier: Modifier = Modifier) {
    Image(
        painter = painterResource(id = R.drawable.connectbiz),
        contentDescription = "Splash Screen Logo",
        modifier = modifier.size(200.dp),
    )
}
