#!/bin/bash

jq -n \
    --arg actor "$GITHUB_ACTOR" --arg repo "$GITHUB_REPOSITORY" \
    --arg workflow "$GITHUB_WORKFLOW" --arg status "${JOB_STATUS,,}" --arg runid "$GITHUB_RUN_ID" \
    --arg success "$1" --arg failure "$2" '{
    username: "GitHub",
    channel: "proj-dahl-build",
    icon_emoji: ":bank:",
    attachments: [{
        color: ( if $status == "success" then "good" else "danger" end ),
        fallback: ( if $status == "success" then $success else $failure end ),
        author_name: $actor,
        author_link: ( "https://github.com/" + $actor ),
        author_icon: ( "https://github.com/" + $actor + ".png?size=32" ),
        footer: ( "<https://github.com/" + $repo + "|" + $repo + ">" ),
        fields: [
            {
                title: "workflow",
                value: $workflow,
                short: true
            },
            {
                title: "status",
                value: ( "<https://github.com/" + $repo + "/actions/runs/" + $runid + "|" + $status + ">" ),
                short: true
            },
            {
                title: "Dahl",
                value: ( if $status == "success" then $success else $failure end ),
                short: false
            }
        ]
    }]
}' | curl -H 'Content-Type: application/json' -d@- "$SLACK_WEBHOOK"
