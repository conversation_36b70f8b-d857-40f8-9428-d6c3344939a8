name: setup
description: Common setups in workflows such as Gradle, Ruby, etc.

runs:
  using: 'composite'
  steps:
    - name: Set up JDK
      uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
      with:
        distribution: 'zulu'
        java-version: '17'

    - name: Set up Gradle
      uses: gradle/actions/setup-gradle@06832c7b30a0129d7fb559bcc6e43d26f6374244 # v4.3.1
      with:
        gradle-version: wrapper

    - name: Set up Ruby
      uses: ruby/setup-ruby@354a1ad156761f5ee2b7b13fa8e09943a5e8d252 # v1.229.0
      with:
        ruby-version: '3.3'
        bundler-cache: true
