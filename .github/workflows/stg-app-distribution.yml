name: stg-app-distribution
run-name: Distribute Stg apps to Firebase App Distribution

on:
  push:
    branches:
      - release/**
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Set up
        uses: ./.github/workflows/setup

      - name: Set up secrets
        uses: ./.github/workflows/setup-stg-debug-secrets
        with:
          google-services: ${{ secrets.GOOGLE_SERVICES_STG }}

      - name: Obtain and store signature keys
        env:
          KEYSTORE_PROPERTIES: ${{ secrets.KEYSTORE_PROPERTIES_STG }}
          KEYSTORE_BASE64: ${{ secrets.KEYSTORE_BASE64_STG }}
        run: |
          mkdir -p signing/
          printenv KEYSTORE_PROPERTIES > signing/release.properties
          printenv KEYSTORE_BASE64 | base64 --decode > signing/release.keystore

      - name: Obtain and store google_application_credentials.json
        env:
          GOOGLE_APPLICATION_CREDENTIALS_JSON: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS_STG }}
        run: |
          printenv GOOGLE_APPLICATION_CREDENTIALS_JSON > google_application_credentials.json

      - name: Build apk
        run: |
          ./gradlew clean app:assembleStgRelease

      - name: Add build-tools path
        run: |
          echo "/usr/local/lib/android/sdk/build-tools/35.0.0" >> $GITHUB_PATH

      - name: Print apk fingerprint
        run: |
          apksigner verify -v --print-certs app/build/outputs/apk/stg/release/app-stg-release.apk

      - name: Distribute apps
        env:
          GOOGLE_APPLICATION_CREDENTIALS: google_application_credentials.json
        run: ./gradlew assembleStgRelease appDistributionUploadStgRelease

      - name: Slack Build Notification
        run: .github/notify.sh ':bank:Stg版が配信されました！' ':bank:Stg版の配信に失敗しました！'
        env:
          JOB_STATUS: ${{ job.status }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        if: always()
