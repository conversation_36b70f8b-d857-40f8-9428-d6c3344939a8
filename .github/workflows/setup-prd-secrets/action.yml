name: setup-secrets-prd
description: Set up prd secrets

inputs:
  google-services:
    description: 'app/src/prd/google-services.json'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Obtain and store google-services.json
      env:
        GOOGLE_SERVICES: ${{ inputs.google-services }}
      shell: bash
      run: |
        mkdir -p app/src/prd/
        printenv GOOGLE_SERVICES > app/src/prd/google-services.json
