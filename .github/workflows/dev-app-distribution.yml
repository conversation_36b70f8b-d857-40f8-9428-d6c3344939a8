name: dev-app-distribution
run-name: Distribute Dev apps to Firebase App Distribution

on:
  push:
    branches:
      - develop
      - release/**
      - preview/**
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Set up
        uses: ./.github/workflows/setup

      - name: Obtain and store signature keys
        env:
          KEYSTORE_PROPERTIES: ${{ secrets.KEYSTORE_PROPERTIES_DEV }}
          KEYSTORE_BASE64: ${{ secrets.KEYSTORE_BASE64_DEV }}
        run: |
          mkdir -p signing/
          printenv KEYSTORE_PROPERTIES > signing/release.properties
          printenv KEYSTORE_BASE64 | base64 --decode > signing/release.keystore

      - name: Obtain and store google_application_credentials.json
        env:
          GOOGLE_APPLICATION_CREDENTIALS_JSON: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS_DEV }}
        run: |
          printenv GOOGLE_APPLICATION_CREDENTIALS_JSON > google_application_credentials.json

      - name: Distribute apps
        env:
          GOOGLE_APPLICATION_CREDENTIALS: google_application_credentials.json
        run: ./gradlew assembleDevRelease appDistributionUploadDevRelease

      - name: Slack Build Notification
        run: .github/notify.sh ':bank:Dev版が配信されました！' ':bank:Dev版の配信に失敗しました！'
        env:
          JOB_STATUS: ${{ job.status }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        if: always()
