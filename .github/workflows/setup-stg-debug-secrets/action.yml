name: setup-secrets-stg
description: Set up stg secrets

inputs:
  google-services:
    description: 'app/src/stg/google-services.json'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Obtain and store google-services.json
      env:
        GOOGLE_SERVICES: ${{ inputs.google-services }}
      shell: bash
      run: |
        mkdir -p app/src/stg/
        printenv GOOGLE_SERVICES > app/src/stg/google-services.json
