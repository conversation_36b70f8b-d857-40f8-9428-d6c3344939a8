name: run-lint
run-name: Run lint

on: pull_request

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Set up
        uses: ./.github/workflows/setup

      - name: Run Android Lint
        run: ./gradlew lint

      - name: Run ktlintCheck
        run: ./gradlew ktlintCheck
        continue-on-error: true

      - name: Run Danger
        env:
          DANGER_GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: bundle exec danger

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Run ktlintFormat
        run: ./gradlew ktlintFormat

      - name: Commit changes
        shell: bash -e {0}
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "inukichi"
          git commit -am "Apply ktlint Format" && git push origin HEAD
          exit 0
