name: run-unit-test
run-name: Run unit test

on: pull_request

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up
        uses: ./.github/workflows/setup

      - name: Run DevDebugUnitTest
        run: ./gradlew testDevDebugUnitTest

      - name: Output the test report
        uses: asadmansr/android-test-report-action@384cd31388782f4106dc4a1b37eea2ff02e0aad7 # v1.2.0
        if: ${{ always() }}

      - name: Run DebugUnitTest
        run: ./gradlew testDebugUnitTest

      - name: Output the test report
        uses: asadmansr/android-test-report-action@384cd31388782f4106dc4a1b37eea2ff02e0aad7 # v1.2.0
        if: ${{ always() }}
