name: deploy-apps-to-google-play-console
run-name: Upload Prd apps to Google Play Console

on:
  push:
    tags:
      - v*.*.*-*

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Set up
        uses: ./.github/workflows/setup

      - name: Set up secrets
        uses: ./.github/workflows/setup-prd-secrets
        with:
          google-services: ${{ secrets.GOOGLE_SERVICES_RELEASE }}

      - name: Obtain and store signature keys
        env:
          KEYSTORE_PROPERTIES: ${{ secrets.KEYSTORE_PROPERTIES_RELEASE }}
          KEYSTORE_BASE64: ${{ secrets.KEYSTORE_BASE64_RELEASE }}
        run: |
          mkdir -p signing/
          printenv KEYSTORE_PROPERTIES > signing/release.properties
          printenv KEYSTORE_BASE64 | base64 --decode > signing/release.keystore

      - name: Build aab
        run: |
          ./gradlew clean app:bundlePrdRelease

      - name: Create service account
        run: echo '${{ secrets.GOOGLE_PLAY_DEPLOY_SERVICE_ACCOUNT }}' > service-account.json

      - name: Upload to Google Play Console
        uses: r0adkll/upload-google-play@935ef9c68bb393a8e6116b1575626a7f5be3a7fb # v1.1.3
        with:
          serviceAccountJson: service-account.json
          packageName: jp.co.dhbk.connectbizapp
          releaseFiles: app/build/outputs/bundle/prdRelease/app-prd-release.aab
          track: internal
          status: draft
