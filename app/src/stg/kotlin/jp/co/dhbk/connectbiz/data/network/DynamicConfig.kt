package jp.co.dhbk.connectbiz.data.network

import javax.inject.Inject
import javax.inject.<PERSON><PERSON>

@Singleton
@Suppress("ktlint:standard:max-line-length")
class DynamicConfig @Inject constructor() {
    var mfaEnable: String =
        "https://devcnbdb2c.b2clogin.com/devcnbdb2c.onmicrosoft.com/oauth2/v2.0/authorize?p=B2C_1A_MFASigninWithKMSI&client_id=c1485aec-4e71-40c4-a49c-54210ac1616c&nonce=defaultNonce&redirect_uri=https%3A%2F%2Fwww.lifeing-board.com%2Fsignin-oidc&scope=openid&response_type=id_token"
    var mfaDisable: String =
        "https://devcnbdb2c.b2clogin.com/devcnbdb2c.onmicrosoft.com/oauth2/v2.0/authorize?p=B2C_1A_MFASigninWithKMSI&client_id=c1485aec-4e71-40c4-a49c-54210ac1616c&nonce=defaultNonce&redirect_uri=https%3A%2F%2Fwww.lifeing-board.com%2Fsignin-oidc&scope=openid&response_type=id_token&m8zNQWV4b5HOZjXQ5yScThy0KYM1rVvR=RftAypaSs7Bij6ZzcwT1Jje1sQ_mCa-n"
    var apiBaseURL: String = "https://www.lifeing-board.com/"
}
