package jp.co.dhbk.connectbizapp

import android.content.Intent
import android.graphics.Color.TRANSPARENT
import android.net.Uri
import android.os.Build
import android.os.Bundle
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavOptions
import androidx.navigation.compose.rememberNavController
import dagger.hilt.android.AndroidEntryPoint
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.ui.designsystem.MainScreen
import jp.co.dhbk.connectbiz.ui.destinations.splash.SplashViewModel
import jp.co.dhbk.connectbiz.ui.navigation.NavInfo
import jp.co.dhbk.connectbiz.ui.navigation.NavManager
import jp.co.dhbk.connectbiz.ui.navigation.NavStateViewModel
import jp.co.dhbk.connectbiz.ui.navigation.navgraph.Navigator
import jp.co.dhbk.connectbiz.usecase.notificationusecase.NotificationUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : FragmentActivity() {
    @Inject
    lateinit var navManager: NavManager

    private val splashViewModel: SplashViewModel by viewModels()

    // private val connectivityViewModel: ConnectivityViewModel by viewModels()
    private val navStateViewModel: NavStateViewModel by viewModels()

    @Inject
    lateinit var preference: ConnectbizPreference

    @Inject
    lateinit var navigator: Navigator

    @Inject
    lateinit var notificationUseCase: NotificationUseCase

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        notificationUseCase = NotificationUseCase(preference)
        // Enable edge-to-edge styling
        enableDynamicEdgeToEdge()

        // Handle incoming links
        Timber.d("onCreate: Intent data - ${intent?.extras?.getString("url")}")
        handleIncomingLink(intent)

        // Set up Compose UI
        setContent {
            val navController = rememberNavController()
            // Track navigation state
            navController.addOnDestinationChangedListener { _, destination, _ ->
                destination.route?.let { route ->
                    navStateViewModel.updateCurrentRoute(route)
                }
            }

            MainScreen(
                navManager = navManager,
                navController = navController,
                navigator = navigator,
            )
        }

        // Observe connectivity changes
        // observeConnectivityChanges()
        // ATTENTION: This was auto-generated to handle app links.
        val appLinkIntent: Intent = intent
        val appLinkAction: String? = appLinkIntent.action
        val appLinkData: Uri? = appLinkIntent.data
        Timber.d("Asset link $appLinkIntent")
        Timber.d("Asset link $appLinkAction")
        Timber.d("Asset link $appLinkData")
        if (appLinkData != null) {
            processNotificationLink(appLinkData.toString())
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Timber.d("onNewIntent: Intent data - ${intent.extras?.getString("url")}")
        handleIncomingLink(intent)
        // Observe connectivity changes
        // observeConnectivityChanges()
        // ATTENTION: This was auto-generated to handle app links.
        val appLinkIntent: Intent = intent
        val appLinkAction: String? = appLinkIntent.action
        val appLinkData: Uri? = appLinkIntent.data
        Timber.d("Asset link $appLinkIntent")
        Timber.d("Asset link $appLinkAction")
        Timber.d("Asset link $appLinkData")
        if (appLinkData != null) {
            processNotificationLink(appLinkData.toString())
        }
    }

    private fun handleIncomingLink(intent: Intent) {
        val notificationUrl = intent.getStringExtra("Url")

        when {
            notificationUrl != null -> processNotificationLink(notificationUrl)
            else -> Timber.d("No incoming link to handle")
        }
    }

//    private fun processAppLink(url: Uri) {
//        notificationUseCase.clearNotificationData()
//        notificationUseCase.setNotificationData(url.toString(), true)
//        notificationUseCase.saveToPreferences()
//        Timber.d( "NavigationStackbefore:${navigator.getNavigationStack()}")
//        CoroutineScope(Dispatchers.Main).launch {
// //            navigator.navigationStackClear()
//            delay(100) // Short delay to ensure UI update is processed
//            navManager.navigate(
//                NavInfo(
//                    id = Route.Welcome.SPLASH,
//                    navOption = NavOptions.Builder()
//                        .setPopUpTo(
//                            Route.Welcome.SPLASH,
//                            inclusive = true
//                        )
//                        .build()
//                )
//            )
//        }
//        Timber.d( "NavigationStackafter:${navigator.getNavigationStack()}")
//    }

    private fun processNotificationLink(url: String) {
        Timber.d("Processing notification link: $url")
        notificationUseCase.clearNotificationData()
        notificationUseCase.setNotificationData(url, true)
        notificationUseCase.saveToPreferences()
        Timber.d("NavigationStackbefore:${navigator.getNavigationStack()}")
        CoroutineScope(Dispatchers.Main).launch {
            navigator.navigationStackClear()
            navManager.navigate(
                NavInfo(
                    id = Route.Welcome.SPLASH,
                    navOption = NavOptions.Builder()
                        .setPopUpTo(
                            Route.Welcome.SPLASH,
                            inclusive = true,
                        )
                        .build(),
                ),
            )
        }
        Timber.d("NavigationStackafter:${navigator.getNavigationStack()}")
    }

    private fun enableDynamicEdgeToEdge() {
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.light(scrim = TRANSPARENT, darkScrim = TRANSPARENT),
            navigationBarStyle = SystemBarStyle.light(scrim = TRANSPARENT, darkScrim = TRANSPARENT),
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.d("MainActivity destroyed")
    }
}
