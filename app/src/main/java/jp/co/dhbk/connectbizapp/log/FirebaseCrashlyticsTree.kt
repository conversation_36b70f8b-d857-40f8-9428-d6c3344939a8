package jp.co.dhbk.connectbizapp.log

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber

/**
 * [FirebaseCrashlytics] にログを送信するための [Timber.Tree]
 */
class FirebaseCrashlyticsTree : Timber.Tree() {
    private val firebaseCrashlytics by lazy { FirebaseCrashlytics.getInstance() }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        when (priority) {
            Log.VERBOSE, Log.DEBUG, Log.INFO -> return
            else -> Unit
        }

        firebaseCrashlytics.apply {
            setCustomKey("priority", priority)
            setCustomKey("tag", tag.orEmpty())
            setCustomKey("message", message)
        }.also {
            if (t != null) {
                it.recordException(t)
            } else {
                it.log(message)
            }
        }
    }
}
