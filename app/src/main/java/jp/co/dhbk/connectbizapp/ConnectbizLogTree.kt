package jp.co.dhbk.connectbizapp

import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber
import javax.inject.Inject

class ConnectbizLogTree
@Inject
constructor() : Timber.Tree() {
    private val crashlytics = FirebaseCrashlytics.getInstance()

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // super.log(priority, tag, message, t)
    }

    override fun e(message: String?, vararg args: Any?) {
        message?.let {
            crashlytics.recordException(Throwable(it))
        }
    }

    override fun e(t: Throwable?) {
        t?.let {
            crashlytics.recordException(t)
        }
    }
}
