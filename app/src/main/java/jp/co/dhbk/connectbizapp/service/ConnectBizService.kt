package jp.co.dhbk.connectbizapp.service

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.media.RingtoneManager
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import dagger.hilt.android.AndroidEntryPoint
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.data.network.apis.ApiService
import jp.co.dhbk.connectbiz.domain.model.DeviceTokenRequest
import jp.co.dhbk.connectbizapp.MainActivity
import jp.co.dhbk.connectbizapp.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class ConnectBizService(
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Default),
) : FirebaseMessagingService() {
    @Inject
    lateinit var connectbizPreference: ConnectbizPreference

    @Inject
    lateinit var apiService: ApiService

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        val idToken = connectbizPreference.getSessionToken() ?: return
        coroutineScope.launch {
            apiService.sendDeviceToken(
                DeviceTokenRequest(
                    deviceToken = token,
                    idToken = idToken,
                ),
            )
        }
    }

    @SuppressLint("TimberArgCount")
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Timber.d("Remote message $remoteMessage")

        remoteMessage.notification?.let {
            Timber.d("Remote message body ${it.body}")
            Timber.d("Remote message title ${it.title}")
            showNotification(it.title, it.body)
        }

        if (remoteMessage.data.isNotEmpty()) {
            Timber.e("Data ${remoteMessage.data}")
            val url = remoteMessage.data["Url"]
            if (url != null) {
                Timber.d("Remote message data URL $url")
                Timber.d("app in foreground ${isAppInForeground()}")

                // Just pass the URL to the notification's Intent, not processing yet.
                showNotification(
                    remoteMessage.notification?.title,
                    remoteMessage.notification?.body,
                    url,
                )
            } else {
                Timber.e("URL is Null")
            }
        }
    }

    private fun showNotification(title: String?, body: String?, url: String? = null) {
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        val channelId = "connectbiz_id"
        val channelName = "CONNECT-BIZ"
        val channelDescription = "connectBiz Application"
        val notificationId = System.currentTimeMillis().toInt()
        val channel =
            NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_DEFAULT)
        channel.description = channelDescription
        channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        notificationManager.createNotificationChannel(channel)

        if (!url.isNullOrEmpty()) {
            val intent = Intent(this, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                putExtra("Url", url)
            }

            val pendingIntent = PendingIntent.getActivity(
                this,
                notificationId,
                intent,
                PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE,
            )

            val notificationBuilder = NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setAutoCancel(true)
                .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
                .setBadgeIconType(NotificationCompat.BADGE_ICON_SMALL)

            notificationManager.notify(notificationId, notificationBuilder.build())
        }
    }

    private fun isAppInForeground(): Boolean {
        val activityManager = getSystemService(ACTIVITY_SERVICE) as ActivityManager
        val appProcesses = activityManager.runningAppProcesses ?: return false
        for (appProcess in appProcesses) {
            if (appProcess.processName == packageName) {
                return appProcess.importance ==
                    ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
            }
        }
        return false
    }
}
