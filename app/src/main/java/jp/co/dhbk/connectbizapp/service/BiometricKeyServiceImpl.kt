package jp.co.dhbk.connectbizapp.service

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import dagger.hilt.android.qualifiers.ApplicationContext
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricKeyService
import jp.co.dhbk.connectbizapp.R
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.inject.Inject

/**
 * 生体認証用の暗号化キー及び[Cipher]オブジェクトを生成するためのクラス
 *
 * @property context
 */
class BiometricKeyServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context,
) : BiometricKeyService {

    private fun createKey(): SecretKey {
        val algorithm = KeyProperties.KEY_ALGORITHM_AES
        val provider = "AndroidKeyStore"
        val keyGenerator = KeyGenerator.getInstance(algorithm, provider)

        val keyName = context.getString(R.string.biometric_key)
        val purposes = KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        val keyGenParameterSpec = KeyGenParameterSpec.Builder(keyName, purposes)
            .setBlockModes(KeyProperties.BLOCK_MODE_CBC)
            .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_PKCS7)
            .build()

        keyGenerator.init(keyGenParameterSpec)
        return keyGenerator.generateKey()
    }

    override fun getEncryptCipher(): Cipher {
        val algorithm = KeyProperties.KEY_ALGORITHM_AES
        val blockMode = KeyProperties.BLOCK_MODE_CBC
        val padding = KeyProperties.ENCRYPTION_PADDING_PKCS7
        return Cipher.getInstance("$algorithm/$blockMode/$padding").apply {
            init(Cipher.ENCRYPT_MODE, createKey())
        }
    }
}
