package jp.co.dhbk.connectbizapp

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import jp.co.dhbk.connectbiz.data.network.DynamicConfigService

@Module
@InstallIn(SingletonComponent::class)
internal abstract class DynamicConfigServiceModule {

    @Binds
    abstract fun bindDynamicConfigService(impl: DynamicConfigServiceImpl): DynamicConfigService
}
