package jp.co.dhbk.connectbizapp

import Route
import android.annotation.SuppressLint
import android.app.Application
import androidx.lifecycle.ProcessLifecycleOwner
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import dagger.hilt.android.HiltAndroidApp
import jp.co.dhbk.analytics.ConnectbizTracker
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPrefKey
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizPreference
import jp.co.dhbk.connectbiz.core.common.utils.ConnectbizSession
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import jp.co.dhbk.connectbiz.ui.navigation.NavInfo
import jp.co.dhbk.connectbiz.ui.navigation.NavManager
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState.isAuthenticationStarted
import jp.co.dhbk.connectbizapp.log.FirebaseCrashlyticsTree
import timber.log.Timber
import java.net.CookieHandler
import java.net.CookieManager
import java.net.CookiePolicy
import java.net.URL
import java.util.UUID
import javax.inject.Inject

@HiltAndroidApp
class ConnectbizApplication : Application() {
    @Inject
    lateinit var firebaseRemoteConfig: FirebaseRemoteConfig

    @Inject
    lateinit var preference: ConnectbizPreference

    @Inject
    lateinit var connectbizLogTree: ConnectbizLogTree

    @Inject
    lateinit var connectbizSession: ConnectbizSession

    @Inject
    lateinit var connectbizTracker: ConnectbizTracker

    @Inject
    lateinit var navManager: NavManager

    @Inject
    lateinit var apiConfig: ApiConfig

    @SuppressLint("SuspiciousIndentation")
    override fun onCreate() {
        super.onCreate()
        val cookieManager = CookieManager()
        cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ALL)
        CookieHandler.setDefault(cookieManager)

        ProcessLifecycleOwner.get().lifecycle.addObserver(
            AppLifecycleObserver(
                onAppBackground = {
                    Timber.d("App went to background ${preference.getLastVistedURL()}")
                },
                onAppForeground = {
                    Timber.d("App came to foreground")
                    preference.getLastVistedURL()?.let { lastVisitedUrl ->
                        try {
                            val visitedDomain = URL(lastVisitedUrl).host
                            val signInDomain = URL(apiConfig.mfaEnable).host
                            if (visitedDomain != signInDomain || preference.getLastVistedURL()
                                == null
                            ) {
                                Timber.d("Reset Navigation: Navigating to Splash Screen")
//                                resetNavigationStateToSplash()
                            } else {
                                Timber.d(
                                    "Stay on the page. Last visited " +
                                        "domain matches sign-in domain: $visitedDomain",
                                )
                            }
                        } catch (e: Exception) {
                            Timber.e("Error parsing URL: ${e.message}")
                            resetNavigationStateToSplash()
                        }
                    }
                },
                // Provide the flag
            ),
        )

        FirebaseApp.initializeApp(this)
        // Obtain the FirebaseAnalytics instance
        FirebaseAnalytics.getInstance(this)
        val configSettings =
            FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(0) // set your desired fetch interval
                .build()

        firebaseRemoteConfig.setConfigSettingsAsync(configSettings)
        // Initialize Timber for logging
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            FirebaseCrashlyticsTree()
        }

        // Set up session properties
        connectbizSession.appName = getString(R.string.app_name)
        connectbizSession.packageName = BuildConfig.APPLICATION_ID
        connectbizSession.appVersion = BuildConfig.VERSION_NAME
        connectbizSession.buildNumber = BuildConfig.VERSION_CODE.toString()

        // Handle device UUID
        var deviceId = preference.getString(ConnectbizPrefKey.UUID)
        if (deviceId == null) {
            deviceId = UUID.randomUUID().toString()
            preference.save(ConnectbizPrefKey.UUID, deviceId)
        }

        connectbizSession.uuid = deviceId
        connectbizTracker.trackUserProperty(mapOf(ConnectbizPrefKey.UUID to deviceId))
    }

    private fun resetNavigationStateToSplash() {
        isAuthenticationStarted = false
        navManager.navigate(NavInfo(id = Route.Welcome.SPLASH))
    }
}
