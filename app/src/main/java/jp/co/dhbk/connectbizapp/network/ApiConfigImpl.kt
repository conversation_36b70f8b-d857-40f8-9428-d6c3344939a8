package jp.co.dhbk.connectbizapp.network

import jp.co.dhbk.connectbiz.data.network.ApiConfig
import jp.co.dhbk.connectbiz.data.network.DynamicConfig
import jp.co.dhbk.connectbizapp.BuildConfig
import javax.inject.Inject

/**
 * [ApiConfig] の実装クラス
 */
internal class ApiConfigImpl
@Inject
constructor(
    private val dynamicConfig: DynamicConfig,
) : ApiConfig {
    override val baseUrl: String get() = dynamicConfig.apiBaseURL
    override val isDebug: Boolean get() = BuildConfig.DEBUG
    override val versionCode: Int get() = BuildConfig.VERSION_CODE
    override val versionName: String get() = BuildConfig.VERSION_NAME
    override val mfaEnable: String get() = dynamicConfig.mfaEnable
    override val mfaDisable: String get() = dynamicConfig.mfaDisable
}
