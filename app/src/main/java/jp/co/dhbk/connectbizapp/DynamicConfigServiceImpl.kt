package jp.co.dhbk.connectbizapp

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import jp.co.dhbk.connectbiz.data.network.DynamicConfig
import jp.co.dhbk.connectbiz.data.network.DynamicConfigService
import javax.inject.Inject

/**
 * DynamicConfigの設定を更新するためのクラス
 *
 * @property context
 */
class DynamicConfigServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val dynamicConfig: DynamicConfig,
) : DynamicConfigService {

    override fun updateDynamicConfig(
        signInWithMfa: String,
        signInWithoutMfa: String,
        apiBaseURL: String,
    ) {
        dynamicConfig.mfaEnable = signInWithMfa
        dynamicConfig.mfaDisable = signInWithoutMfa
        dynamicConfig.apiBaseURL = apiBaseURL
    }
}
