package jp.co.dhbk.connectbizapp

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import jp.co.dhbk.connectbiz.ui.utils.component.GlobalAppState
import timber.log.Timber

class AppLifecycleObserver(
    private val onAppBackground: () -> Unit,
    private val onAppForeground: () -> Unit,
) : DefaultLifecycleObserver {

    private var isInBackground = false

    override fun onStart(owner: LifecycleOwner) {
        if (isInBackground) {
            isInBackground = false
            onAppForeground()
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        if (GlobalAppState.isCustomTabOpened || GlobalAppState.isFileSelectionInProgress ||
            GlobalAppState.isAuthenticationStarted
        ) {
            Timber.d("Custom Tab is open. Skipping navigation reset.")
            return
        }
        isInBackground = true
        onAppBackground()
    }
}
