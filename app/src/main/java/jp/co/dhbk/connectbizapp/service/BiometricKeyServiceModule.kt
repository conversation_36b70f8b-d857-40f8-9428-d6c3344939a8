package jp.co.dhbk.connectbizapp.service

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import jp.co.dhbk.connectbiz.data.network.biometricauth.BiometricKeyService

@Module
@InstallIn(SingletonComponent::class)
internal abstract class BiometricKeyServiceModule {

    @Binds
    abstract fun bindBiometricKeyService(impl: BiometricKeyServiceImpl): BiometricKeyService
}
