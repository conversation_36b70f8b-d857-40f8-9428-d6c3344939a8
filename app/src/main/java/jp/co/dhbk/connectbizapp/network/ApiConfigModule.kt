package jp.co.dhbk.connectbizapp.network

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import jp.co.dhbk.connectbiz.data.network.ApiConfig
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
internal abstract class ApiConfigModule {
    @Binds
    @Singleton
    abstract fun bindsApiConfig(impl: ApiConfigImpl): ApiConfig
}
