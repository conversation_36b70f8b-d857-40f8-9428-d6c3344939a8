import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.util.Properties

plugins {
    alias(libs.plugins.buildlogic.android.application)
    alias(libs.plugins.buildlogic.android.compose)
    alias(libs.plugins.buildlogic.android.hilt)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.appdistribution)
    alias(libs.plugins.firebase.crashlytics)
    alias(libs.plugins.aboutlibraries)
    alias(libs.plugins.buildlogic.retrofit)
}

android {
    namespace = "jp.co.dhbk.connectbizapp"

    defaultConfig {
        applicationId = "jp.co.dhbk.connectbizapp"
        versionCode = 12
        versionName = "1.0.1"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    signingConfigs {
        register("release") {
            val file = rootProject.file("signing/release.keystore")
            if (file.exists()) {
                val properties =
                    Properties().apply {
                        load(FileInputStream(rootProject.file("signing/release.properties")))
                    }
                storeFile = file
                storePassword = properties.getProperty("storePassword")
                keyAlias = properties.getProperty("keyAlias")
                keyPassword = properties.getProperty("keyPassword")
            }
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
            signingConfig = signingConfigs.findByName("release")
        }
        debug {
            versionNameSuffix = "-debug"
            isDebuggable = true
        }
    }

    // Define product flavors for different environments
    flavorDimensions.add("default")
    productFlavors {
        register("dev") {
            applicationId = "jp.co.fuller.dhbk.connectbizapp.dev"
            versionNameSuffix = "-dev+${getGitInfo()}"
            firebaseAppDistribution {
                artifactType = "apk"
                releaseNotes = getGitLog(10)
                groups = "Fuller, NEX-GEN"
            }
        }

        register("stg") {
            applicationId = "jp.co.dhbk.connectbizapp.stg"
            versionNameSuffix = "-stg+${getGitInfo()}"
            firebaseAppDistribution {
                artifactType = "apk"
                releaseNotes = getGitLog(10)
                groups = "Fuller, NEX-GEN"
            }
        }

        register("prd") {
            applicationId = "jp.co.dhbk.connectbizapp"
        }
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            pickFirsts += "META-INF/versions/9/OSGI-INF/MANIFEST.MF"
            // excludes += "META-INF/versions/9/OSGI-INF/MANIFEST.MF"
        }
    }
    aboutLibraries {
        configPath = "aboutlibraries-config"
        allowedLicenses =
            arrayOf(
                "Apache License 2.0",
                "Android Software Development Kit License",
                "MIT License",
                "Bouncy Castle Licence",
            )
        strictMode = com.mikepenz.aboutlibraries.plugin.StrictMode.FAIL
        excludeFields = arrayOf("artifactVersion", "description", "funding", "tag")
    }
}

dependencies {

    implementation(platform(libs.firebase.bom)) // Firebase BOM
    implementation(libs.firebase.messaging) // Firebase Messaging
    implementation(libs.google.firebase.crashlytics.ktx)
    implementation(libs.play.services.measurement.api)
    implementation(libs.androidx.lifecycle.process)
    // to add module
    implementation(project(":core:analytics"))
    implementation(project(":core:common"))
    implementation(project(":core:data"))
    implementation(project(":core:ui"))
    implementation(project(":domain:model"))
    implementation(project(":domain:repository"))
    implementation(project(":domain:usecase"))
    implementation(project(":data"))
    implementation(project(":ui"))
    implementation(libs.firebase.config.ktx)
    testImplementation(libs.androidx.biometric)
}

/**
 * @param commands shell commands
 * @return shell commands の実行結果
 */
fun Project.execute(vararg commands: String): String {
    val out = ByteArrayOutputStream()
    exec {
        commandLine = listOf("sh", "-c").plus(commands)
        standardOutput = out
        isIgnoreExitValue = true
    }
    return out.toString().trim()
}

/**
 * @return `$gitBranch.$gitHash`
 */
fun getGitInfo(): String {
    return "${getGitBranch()}.${getGitHash()}"
}

/**
 * @return hash of git branch
 */
fun getGitHash(): String {
    return execute("cd ${project.rootDir} ; git rev-parse --short HEAD")
}

/**
 * @return git branch
 */
fun getGitBranch(): String {
    return execute("cd ${project.rootDir} ; git name-rev --name-only HEAD")
}

/**
 * @param size 取得したいログの件数
 * @return git log
 */
fun getGitLog(size: Int): String {
    return execute("cd ${project.rootDir} ; git log -$size --pretty=format:'%h %s'")
}
