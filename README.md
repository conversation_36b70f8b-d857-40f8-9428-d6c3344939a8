# dahl_android


 Android app Handling Connect-Biz Website in Android Mobile Application with Login, Biometric Authentication



# List of framework used

This applicaiton is build with most advance features available in the android like Jetpack compose with kotlin framework. Here are the few notable framework

1. Jetpack compose
2. Kotlin
3. Hilt Android
4. Navigation Framework
5. Material3
6. <PERSON><PERSON>
7. Timber
8. <PERSON>pt<PERSON> for Encrypted Shared Preferences
9. Accompanist for permission, pager
10. Biometric Authentication (Finger print/ Face ID / Password / PIN /Pattern)
11. AndroidX library 
12. MVVM
13. Build-logic implemented(Refer from Fuller Android-template repo)

# Dependencies
To run this application you need firebase configuration for analytics , crashlytics

1.  Firebase analytics ,Crashlytics, cloud messaging(Push Notification)
2.  Biometric Authentication using AndroidX library
# dahl_android
第四北越銀行アプリ for Android
